import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { FeatureFlagKey } from '@/utils/featureFlags';

interface FeatureFlag {
  id: string;
  flag_key: string;
  flag_name: string;
  description: string;
  is_enabled: boolean;
  category: string;
}

export const useFeatureFlags = () => {
  const [flags, setFlags] = useState<FeatureFlag[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFlags();
  }, []);

  const fetchFlags = async () => {
    try {
      const { data, error } = await supabase
        .from('feature_flags')
        .select('*')
        .eq('is_enabled', true);

      if (error) throw error;
      setFlags(data || []);
    } catch (error) {
      console.error('Error fetching feature flags:', error);
    } finally {
      setLoading(false);
    }
  };

  const isEnabled = (flagKey: FeatureFlagKey): boolean => {
    const flag = flags.find(f => f.flag_key === flagKey);
    return flag?.is_enabled || false;
  };

  return {
    flags,
    loading,
    isEnabled,
    refetch: fetchFlags
  };
};