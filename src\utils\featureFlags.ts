import { supabase } from '@/integrations/supabase/client';

export const FEATURE_FLAGS = {
  PROPERTY_BOOKING_ENABLED: 'property_booking_enabled',
  ONLINE_RESERVATIONS: 'online_reservations',
  EVENT_BOOKING: 'event_booking',
  PAGE_ABOUT_VISIBLE: 'page_about_visible',
  PAGE_SERVICES_VISIBLE: 'page_services_visible',
  PAGE_PROPERTIES_VISIBLE: 'page_properties_visible',
  PAGE_EVENTS_VISIBLE: 'page_events_visible',
} as const;

export type FeatureFlagKey = typeof FEATURE_FLAGS[keyof typeof FEATURE_FLAGS];

export const getFeatureFlag = async (flagKey: FeatureFlagKey): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('feature_flags')
      .select('is_enabled')
      .eq('flag_key', flagKey)
      .eq('is_enabled', true)
      .single();

    if (error) {
      console.warn(`Feature flag ${flagKey} not found or disabled`);
      return false;
    }

    return data?.is_enabled || false;
  } catch (error) {
    console.error('Error fetching feature flag:', error);
    return false;
  }
};

export const getAllFeatureFlags = async (): Promise<Record<string, boolean>> => {
  try {
    const { data, error } = await supabase
      .from('feature_flags')
      .select('flag_key, is_enabled');

    if (error) throw error;

    return (data || []).reduce((acc, flag) => {
      acc[flag.flag_key] = flag.is_enabled;
      return acc;
    }, {} as Record<string, boolean>);
  } catch (error) {
    console.error('Error fetching all feature flags:', error);
    return {};
  }
};