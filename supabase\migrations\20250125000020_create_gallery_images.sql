-- Create gallery_images table
CREATE TABLE IF NOT EXISTS public.gallery_images (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  image_url text NOT NULL,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.gallery_images ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to gallery_images" ON public.gallery_images
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage gallery_images" ON public.gallery_images
  FOR ALL USING (auth.role() = 'authenticated');

-- Create updated_at trigger
CREATE TRIGGER handle_gallery_images_updated_at
  BEFORE UPDATE ON public.gallery_images
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample gallery images
INSERT INTO public.gallery_images (title, image_url, display_order, is_active) VALUES
('Luxury Suite', 'https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&q=80', 1, true),
('Restaurant Corner', 'https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80', 2, true),
('Elegant Lobby', 'https://images.unsplash.com/photo-1460353581641-37baddab0fa2?q=80&w=1200&auto=format&fit=crop', 3, true),
('Property Exterior', 'https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?q=80&w=1200&auto=format&fit=crop', 4, true),
('Fine Dining', 'https://images.unsplash.com/photo-1528605248644-14dd04022da1?q=80&w=1200&auto=format&fit=crop', 5, true),
('Banquet Entry', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?q=80&w=1200&auto=format&fit=crop', 6, true),
('Gourmet Dessert', 'https://images.unsplash.com/photo-1496412705862-e0088f16f791?q=80&w=1200&auto=format&fit=crop', 7, true),
('Event Decoration', 'https://images.unsplash.com/photo-1518481612222-68bbe828ecd1?q=80&w=1200&auto=format&fit=crop', 8, true);