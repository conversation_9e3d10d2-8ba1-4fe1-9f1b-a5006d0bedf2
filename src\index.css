@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hotel Management System Design System
All colors MUST be HSL for consistent theming */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Hotel Management Primary Colors - Deep Navy & Gold */
    --primary: 217 91% 16%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 217 91% 25%;
    --primary-dark: 217 91% 10%;

    /* Gold Accent Colors for Luxury Touch */
    --gold: 43 96% 56%;
    --gold-foreground: 0 0% 15%;
    --gold-light: 43 96% 65%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 220 13% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 217 91% 95%;
    --accent-foreground: 217 91% 16%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 15%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 16%;

    --radius: 0.75rem;

    /* Hotel Management Gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--primary-light)) 100%
    );
    --gradient-gold: linear-gradient(
      135deg,
      hsl(var(--gold)) 0%,
      hsl(var(--gold-light)) 100%
    );
    --gradient-subtle: linear-gradient(
      180deg,
      hsl(var(--background)) 0%,
      hsl(var(--muted)) 100%
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(var(--primary-dark)) 0%,
      hsl(var(--primary)) 50%,
      hsl(var(--primary-light)) 100%
    );

    /* Shadows for Depth */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.15);
    --shadow-card: 0 4px 20px -4px hsl(var(--primary) / 0.1);
    --shadow-gold: 0 8px 25px -8px hsl(var(--gold) / 0.3);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-light: 217 91% 35%;
    --primary-dark: 217 91% 8%;

    --gold: 43 96% 56%;
    --gold-foreground: 0 0% 15%;
    --gold-light: 43 96% 65%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 15%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Hotel Management Custom Classes */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-card hover:-translate-y-1;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary to-gold bg-clip-text text-transparent;
  }

  .btn-primary {
    @apply bg-primary hover:bg-primary-light text-primary-foreground shadow-elegant transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
  }

  .btn-gold {
    @apply bg-gold hover:bg-gold-light text-gold-foreground shadow-gold transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
  }

  /* Responsive utilities */
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .heading-responsive {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .padding-responsive {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .margin-responsive {
    @apply mx-4 sm:mx-6 lg:mx-8;
  }

  .gap-responsive {
    @apply gap-3 sm:gap-4 lg:gap-6;
  }

  /* Mobile-first grid utilities */
  .grid-responsive-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* Button responsive utilities */
  .btn-responsive {
    @apply px-4 sm:px-6 py-2.5 sm:py-3 text-sm sm:text-base;
  }

  /* Card responsive utilities */
  .card-responsive {
    @apply p-4 sm:p-6 rounded-xl sm:rounded-2xl;
  }

  /* Line clamp utilities for better text truncation */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Crisp icon rendering */
  .icon-crisp {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
}
