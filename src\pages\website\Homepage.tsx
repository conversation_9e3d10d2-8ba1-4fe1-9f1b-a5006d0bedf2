import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import SwipeCards from '@/components/ui/Swipecard';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import { supabase } from '@/integrations/supabase/client';
import EventsShowcase from '@/components/EventsShowcase';
import RoomsShowcase from '@/components/RoomsShowcase';
import DiningSection from '@/components/website/DiningSection';
import ContactSection from '@/components/ContactSection';
import FAQSection from '@/components/website/FAQSection';
import { motion } from 'framer-motion';
import {
  Star,
  Users,
  Calendar,
  MapPin,
  Phone,
  Clock,
  ChefHat,
  Wine,
  Bed,
  PartyPopper,
  CheckCircle,
  ArrowRight,
  Crown,
  Menu,
  X,
  Building2,
  Utensils,
  Map,
  ChevronLeft,
  ChevronRight,
  Wifi,
  Snowflake,
  Tv2,
  CupSoda,
  Image,
  Coffee,
  BedDouble,
  ShowerHead,
  Sparkles,
  Bath,
  CheckCircle2,
  CalendarRange,
  Flame,
  Check,
  PhoneCall,
  Images,
  MessagesSquare,
  Quote,
  Mail,
  Send
} from 'lucide-react';
import * as LucideIcons from 'lucide-react';

interface CarouselImage {
  id: string;
  title: string;
  description: string;
  image_url: string;
  display_order: number;
}

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  display_order: number;
}

interface Property {
  id: string;
  name: string;
  description: string;
  image_url: string;
  location: string;
  price_per_night: number;
  rating: number;
  amenities: string[];
}

const Homepage = () => {
  const [carouselImages, setCarouselImages] = useState<CarouselImage[]>([]);
  const [features, setFeatures] = useState<Feature[]>([]);
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchContent();
  }, []);

  const fetchContent = async () => {
    try {
      // Fetch carousel images
      const { data: carouselData } = await supabase
        .from('carousel_images')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      // Fetch features
      const { data: featuresData } = await supabase
        .from('features')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      // Fetch featured properties
      const { data: propertiesData } = await supabase
        .from('properties')
        .select('*')
        .eq('is_featured', true)
        .eq('is_active', true);

      setCarouselImages(carouselData || []);
      setFeatures(featuresData || []);
      setProperties(propertiesData || []);
    } catch (error) {
      console.error('Error fetching content:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIconComponent = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName] || Crown;
    return IconComponent;
  };

  const testimonials = [
    {
      name: "Priya Sharma",
      rating: 5,
      comment: "Amazing food quality and excellent service. The multi-cuisine options are fantastic!",
      location: "Surat"
    },
    {
      name: "Rajesh Patel",
      rating: 5,
      comment: "Perfect venue for our wedding reception. The staff was incredibly helpful.",
      location: "Bardoli"
    },
    {
      name: "Meera Shah",
      rating: 5,
      comment: "Great vegan options and the all-you-can-eat experience was wonderful.",
      location: "Navsari"
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-2xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900 min-h-screen" style={{ fontFamily: "'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'", scrollBehavior: 'smooth' }}>
      {/* Hero Section */}
      <section className="relative">
        <div className="relative h-[100vh] sm:h-[90vh] lg:h-[85vh] w-full overflow-hidden">
          <motion.video
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 w-full h-full object-cover"
            src="https://videos.pexels.com/video-files/856130/856130-hd_1920_1080_25fps.mp4"
            autoPlay
            loop
            muted
            playsInline
            poster="https://images.unsplash.com/photo-1501117716987-c8e2a3a67a0b?q=80&w=1760&auto=format&fit=crop"
            aria-label="Aerial view of property"
          />
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute inset-0 bg-gradient-to-b from-emerald-950/70 via-emerald-950/50 to-emerald-950/70"
          ></motion.div>
          <div className="relative z-10 h-full">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-full">
              <div className="flex flex-col laptop-sm:flex-row items-center mt-20 sm:mt-14 md:mt-8 sm:justify-center justify-between sm:h-full h-[80%] py-4 sm:py-8 laptop-sm:py-0 gap-4 sm:gap-6 laptop-sm:gap-12">
                {/* Left Content */}
                <div className="w-full laptop-sm:w-1/2 space-y-4 sm:space-y-6 laptop-sm:space-y-8 text-center laptop-sm:text-left order-1">
                  <motion.div 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="inline-flex items-center gap-3 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-white/10 backdrop-blur-sm ring-1 ring-white/20"
                  >
                    <MapPin className="w-3 sm:w-4 h-3 sm:h-4 text-amber-300" />
                    <span className="text-white/90 text-xs sm:text-sm font-medium">Bardoli–Vyara Highway</span>
                  </motion.div>

                  <div className="space-y-2 sm:space-y-4 lg:space-y-6">
                    <motion.h1 
                      initial={{ y: 50, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 1, delay: 1, ease: "easeOut" }}
                      className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold leading-[1.1] tracking-tight text-white px-4 sm:px-0"
                      style={{ fontFamily: "'Cormorant Garamond', serif" }}
                    >
                      Where Luxury Meets Tradition
                    </motion.h1>

                    <motion.p 
                      initial={{ y: 30, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.8, delay: 1.3, ease: "easeOut" }}
                      className="text-sm sm:text-base laptop-sm:text-lg text-white/90 leading-relaxed max-w-xl mx-auto laptop-sm:mx-0 px-4 sm:px-0"
                    >
                      Experience elegant stays, gourmet dining, and unforgettable celebrations at Meadow De Jalsa.
                    </motion.p>
                  </div>

                  <motion.div 
                    initial={{ y: 40, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 1.6, ease: "easeOut" }}
                    className="flex flex-col sm:flex-row items-center justify-center laptop-sm:justify-start gap-3 sm:gap-4"
                  >
                    <Link to="/properties" className="w-full sm:w-auto">
                      <motion.button 
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="w-full sm:w-auto group inline-flex items-center justify-center gap-2 rounded-full px-5 sm:px-6 py-3 sm:py-3.5 text-sm sm:text-base font-medium tracking-tight text-emerald-950 shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/50"
                      >
                        <Calendar className="w-4 sm:w-5 h-4 sm:h-5" />
                        Book Your Stay
                      </motion.button>
                    </Link>
                    <Link to="/events" className="w-full sm:w-auto">
                      <motion.button 
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="w-full sm:w-auto inline-flex items-center justify-center gap-2 rounded-full px-5 sm:px-6 py-3 sm:py-3.5 text-sm sm:text-base text-white/90 ring-1 ring-white/30 hover:bg-white/10 transition"
                      >
                        <PartyPopper className="w-4 sm:w-5 h-4 sm:h-5" />
                        Plan Your Event
                      </motion.button>
                    </Link>
                  </motion.div>
                </div>

                {/* Right SwipeCards */}
                <motion.div 
                  initial={{ x: 100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 1, delay: 1.2, ease: "easeOut" }}
                  className="w-full laptop-sm:w-1/2 flex items-center justify-center laptop-sm:justify-end order-2 px-4 sm:px-0"
                >
                  <div className="w-full max-w-[260px] sm:max-w-[300px] laptop-sm:max-w-[500px] ">
                    <SwipeCards />
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Quick Info Strip */}
      <motion.section 
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        viewport={{ once: true }}
        className="bg-white border-y border-slate-200"
      >
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-4 sm:py-5 grid grid-cols-1 xs:grid-cols-2 laptop-sm:grid-cols-4 gap-2 sm:gap-3">
          {[
            { icon: Clock, label: "Check-in", value: "2:00 PM" },
            { icon: ChefHat, label: "Restaurant", value: "11 AM – 11 PM" },
            { icon: Phone, label: "WiFi", value: "High-speed" },
            { icon: MapPin, label: "Parking", value: "Ample & Secure" }
          ].map((item, index) => (
            <motion.div 
              key={item.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1, ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
              className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition cursor-pointer"
            >
              <div className="h-8 w-8 sm:h-9 sm:w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center flex-shrink-0">
                <item.icon className="w-4 h-4 sm:w-5 sm:h-5" />
              </div>
              <div className="min-w-0">
                <div className="text-[11px] sm:text-[12px] text-slate-500">{item.label}</div>
                <div className="text-xs sm:text-sm font-medium truncate">{item.value}</div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>


      {/* About Snapshot */}
      <section className="bg-white">
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8 sm:py-12"
        >
          <div className="text-center">
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
              className="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-emerald-950 font-semibold px-4 sm:px-0" 
              style={{ fontFamily: "'Cormorant Garamond', serif" }}
            >
              Luxury Stay & Memorable Events on Bardoli–Vyara Highway
            </motion.h2>
            <motion.p 
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              viewport={{ once: true }}
              className="mt-3 text-sm sm:text-base text-slate-600 max-w-2xl mx-auto px-4 sm:px-0"
            >
              A modern retreat offering refined rooms, curated dining, and bespoke event experiences.
            </motion.p>
          </div>
        </motion.div>

        {/* Highlights */}
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-10 sm:pb-14">
          <div className="grid grid-cols-1 sm:grid-cols-2 laptop-sm:grid-cols-4 gap-3 sm:gap-4">
            {[
              { to: "/properties", img: "https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1760&auto=format&fit=crop", alt: "Rooms & Suites", icon: Building2, label: "Stay", desc: "Rooms & suites designed for comfort and elegance." },
              { to: "/services", img: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=1760&auto=format&fit=crop", alt: "Restaurant", icon: Utensils, label: "Dine", desc: "Taste luxury, taste tradition with curated cuisine." },
              { to: "/events", img: "https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&w=1760&auto=format&fit=crop", alt: "Events & Banquets", icon: PartyPopper, label: "Celebrate", desc: "Weddings, corporate events, parties, and more." },
              { to: "/about", img: "https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80", alt: "Nearby Attractions", icon: Map, label: "Explore", desc: "Discover the best of Bardoli & Vyara nearby." }
            ].map((item, index) => (
              <motion.div
                key={item.label}
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.7, delay: index * 0.15, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
              >
                <Link to={item.to} className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-xl transition-all duration-300 block">
                  <motion.img 
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                    src={item.img} 
                    alt={item.alt} 
                    className="h-40 sm:h-48 w-full object-cover" 
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
                  <div className="absolute inset-x-0 bottom-0 p-3 sm:p-4">
                    <motion.div 
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.3 + (index * 0.1) }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.05 }}
                      className="inline-flex items-center gap-2 px-2.5 sm:px-3 py-1 sm:py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200"
                    >
                      <motion.div
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.5 }}
                      >
                        <item.icon className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-emerald-900" />
                      </motion.div>
                      <span className="text-xs sm:text-[13px] font-medium text-emerald-950">{item.label}</span>
                    </motion.div>
                    <motion.p 
                      initial={{ opacity: 0, y: 15 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + (index * 0.1) }}
                      viewport={{ once: true }}
                      className="mt-2 sm:mt-3 text-white/90 text-xs sm:text-sm"
                    >
                      {item.desc}
                    </motion.p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Events Showcase Carousel */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <EventsShowcase />
      </motion.div>

      {/* Rooms & Suites */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <RoomsShowcase />
      </motion.div>

      {/* Featured Properties */}
      {/* <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-gray-900">Featured Properties</h2>
            <p className="text-lg text-muted-foreground">
              Discover our premium accommodations and event venues
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {properties.map((property) => (
              <Card key={property.id} className="overflow-hidden card-hover">
                <div className="aspect-video relative">
                  <img
                    src={property.image_url}
                    alt={property.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                    <Badge className="bg-gold text-gold-foreground">
                      ₹{property.price_per_night}/night
                    </Badge>
                  </div>
                </div>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>{property.name}</CardTitle>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-gold text-gold" />
                      <span className="text-sm font-medium">{property.rating}</span>
                    </div>
                  </div>
                  <CardDescription>{property.description}</CardDescription>
                  <p className="text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 inline mr-1" />
                    {property.location}
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {property.amenities.map((amenity, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {amenity}
                      </Badge>
                    ))}
                  </div>
                  <Link to="/properties">
                    <Button className="w-full">
                      View Details
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link to="/properties">
              <Button variant="outline" size="lg">
                View All Properties
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section> */}

      {/* Signature Dining Section */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <DiningSection />
      </motion.div>

      {/* CTA Section */}
      <section className="bg-[url('https://images.unsplash.com/photo-1518277850984-94e89c892b26?q=80&w=1760&auto=format&fit=crop')] bg-cover bg-center bg-fixed">
        <div className="bg-emerald-950/80">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16 text-center">
            <h3 className="text-white tracking-tight text-2xl sm:text-3xl lg:text-4xl font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Ready to make memories?</h3>
            <p className="mt-3 text-white/85 text-sm sm:text-base">Reserve your stay or plan a bespoke event with our expert team.</p>
            <div className="mt-6 sm:mt-7 flex flex-col sm:flex-row items-center justify-center gap-3">
              <Link to="/properties" className="w-full sm:w-auto">
                <button className="w-full sm:w-auto inline-flex items-center justify-center gap-2 rounded-full px-5 py-3 text-sm sm:text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                  <Calendar className="w-4 sm:w-5 h-4 sm:h-5" />
                  Book Now
                </button>
              </Link>
              <Link to="/contact" className="w-full sm:w-auto inline-flex items-center justify-center gap-2 rounded-full px-5 py-3 text-sm sm:text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                <MessagesSquare className="w-4 sm:w-5 h-4 sm:h-5" />
                Talk to Us
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="bg-slate-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>What Our Guests Say</h2>
            <p className="mt-3 text-sm sm:text-base text-slate-600 max-w-2xl mx-auto">
              Authentic experiences shared by our valued guests from across Gujarat
            </p>
          </div>

          <div className="grid grid-cols-1 laptop-sm:grid-cols-3 gap-4 sm:gap-6">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="relative bg-white rounded-2xl p-4 sm:p-6 ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <div className="absolute -top-2 sm:-top-3 left-4 sm:left-6">
                  <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-emerald-900 text-amber-300 grid place-items-center">
                    <Quote className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                  </div>
                </div>

                <div className="pt-2 sm:pt-3">
                  <p className="text-slate-700 text-sm sm:text-[15px] leading-relaxed mb-3 sm:mb-4">
                    "{testimonial.comment}"
                  </p>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm sm:text-[16px] font-semibold text-emerald-950">{testimonial.name}</div>
                      <div className="text-xs sm:text-[13px] text-slate-500">{testimonial.location}</div>
                    </div>

                    <div className="flex items-center gap-0.5 sm:gap-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-3.5 w-3.5 sm:h-4 sm:w-4 fill-amber-400 text-amber-400" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 sm:mt-10 text-center">
            <div className="inline-flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2 rounded-full bg-emerald-50 ring-1 ring-emerald-200">
              <div className="flex items-center gap-0.5 sm:gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-3.5 w-3.5 sm:h-4 sm:w-4 fill-amber-400 text-amber-400" />
                ))}
              </div>
              <span className="text-xs sm:text-[14px] text-emerald-950 font-medium">4.9/5 from 200+ reviews</span>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection />

      {/* Contact Section */}
      <ContactSection />

    </div>
  );
};

export default Homepage;