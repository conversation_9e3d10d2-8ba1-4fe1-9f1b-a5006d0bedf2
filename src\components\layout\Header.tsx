import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  Search, 
  Settings,
  User,
  Moon,
  Sun,
  Menu
} from 'lucide-react';
import { Input } from '@/components/ui/input';

interface HeaderProps {
  userRole: string;
  onMenuClick?: () => void;
}

export const Header: React.FC<HeaderProps> = ({ userRole, onMenuClick }) => {
  const getRoleDisplayName = () => {
    const roleNames = {
      'super-admin': 'Super Administrator',
      'hotel-manager': 'Hotel Manager',
      'receptionist': 'Receptionist',
      'housekeeping': 'Housekeeping Staff',
      'guest': 'Guest'
    };
    return roleNames[userRole as keyof typeof roleNames] || 'User';
  };

  return (
    <header className="bg-card border-b shadow-sm px-3 sm:px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Mobile menu button */}
        {onMenuClick && (
          <button
            onClick={onMenuClick}
            className="p-2 rounded-lg hover:bg-accent lg:hidden mr-3"
          >
            <Menu className="h-5 w-5" />
          </button>
        )}
        
        {/* Search */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search guests, rooms, bookings..."
              className="pl-10 bg-background text-sm"
            />
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Notifications */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-5 w-5" />
            <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-destructive text-destructive-foreground">
              3
            </Badge>
          </Button>

          {/* Settings */}
          <Button variant="ghost" size="sm">
            <Settings className="h-5 w-5" />
          </Button>

          {/* Theme Toggle */}
          <Button variant="ghost" size="sm">
            <Moon className="h-5 w-5" />
          </Button>

          {/* User Info */}
          <div className="flex items-center space-x-3 pl-2 sm:pl-4 border-l">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-primary/10 rounded-full">
                <User className="h-4 w-4 text-primary" />
              </div>
              <div className="text-right hidden sm:block">
                <p className="text-sm font-medium">Admin User</p>
                <p className="text-xs text-muted-foreground">{getRoleDisplayName()}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;