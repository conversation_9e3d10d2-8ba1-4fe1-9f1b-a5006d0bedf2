import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash2, Search, Eye, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import AmenitiesForm from "@/components/master-tables/AmenitiesForm";
import { motion } from "framer-motion";

interface Amenity {
  id: string;
  property_id?: string;
  name: string;
  category: string;
  sub_category?: string;
  description?: string;
  is_chargeable: boolean;
  charge_amount: number;
  charge_type?: string;
  is_active: boolean;
  properties_admin?: {
    name: string;
  };
}

const Amenities = () => {
  const [amenities, setAmenities] = useState<Amenity[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showForm, setShowForm] = useState(false);
  const [editingAmenity, setEditingAmenity] = useState<Amenity | null>(null);
  const [viewingAmenity, setViewingAmenity] = useState<Amenity | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchAmenities();
  }, []);

  const fetchAmenities = async () => {
    try {
      const { data, error } = await supabase
        .from("amenities_admin")
        .select(
          `
          *,
          properties_admin (
            name
          )
        `
        )
        .eq("is_deleted", false)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setAmenities(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch amenities",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this amenity?")) return;

    try {
      const { error } = await supabase
        .from("amenities_admin")
        .update({ is_deleted: true })
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Amenity deleted successfully",
      });
      fetchAmenities();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete amenity",
        variant: "destructive",
      });
    }
  };

  const filteredAmenities = amenities.filter(
    (amenity) =>
      amenity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      amenity.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      amenity.properties_admin?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-80" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-64" />
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
          Amenities Master
        </h1>
        <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Amenity
        </Button>
      </motion.div>

      <motion.div
        className="flex items-center space-x-4"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search amenities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </motion.div>

      <motion.div
        className="grid gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {filteredAmenities.map((amenity, index) => (
          <motion.div
            key={amenity.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            whileHover={{ scale: 1.01, y: -2 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant hover:shadow-card transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                  <div className="flex-1">
                    <CardTitle className="flex flex-wrap items-center gap-2 text-lg">
                      {amenity.name}
                      <Badge
                        variant={amenity.is_active ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {amenity.is_active ? "Active" : "Inactive"}
                      </Badge>
                      {amenity.is_chargeable && (
                        <Badge
                          variant="outline"
                          className="text-xs bg-gold/10 text-gold border-gold/20"
                        >
                          💰 Chargeable
                        </Badge>
                      )}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Category: {amenity.category}
                      {amenity.properties_admin?.name &&
                        ` | Property: ${amenity.properties_admin.name}`}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewingAmenity(amenity)}
                      className="hover:bg-primary/10 hover:border-primary/20"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingAmenity(amenity);
                        setShowForm(true);
                      }}
                      className="hover:bg-warning/10 hover:border-warning/20"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(amenity.id)}
                      className="hover:bg-destructive/10 hover:border-destructive/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                  <div className="flex flex-col">
                    <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                      Category
                    </span>
                    <span className="text-foreground">{amenity.category}</span>
                  </div>
                  {amenity.sub_category && (
                    <div className="flex flex-col">
                      <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                        Sub Category
                      </span>
                      <span className="text-foreground">
                        {amenity.sub_category}
                      </span>
                    </div>
                  )}
                  {amenity.is_chargeable && (
                    <>
                      <div className="flex flex-col">
                        <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                          Charge
                        </span>
                        <span className="text-foreground font-semibold text-gold">
                          ₹{amenity.charge_amount}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                          Charge Type
                        </span>
                        <span className="text-foreground">
                          {amenity.charge_type}
                        </span>
                      </div>
                    </>
                  )}
                </div>
                {amenity.description && (
                  <p className="text-sm text-muted-foreground mt-3 p-3 bg-muted/30 rounded-lg">
                    {amenity.description}
                  </p>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {showForm && (
        <AmenitiesForm
          amenity={editingAmenity}
          onClose={() => {
            setShowForm(false);
            setEditingAmenity(null);
          }}
          onSuccess={() => {
            fetchAmenities();
            setShowForm(false);
            setEditingAmenity(null);
          }}
        />
      )}

      {viewingAmenity && (
        <Dialog
          open={!!viewingAmenity}
          onOpenChange={() => setViewingAmenity(null)}
        >
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Amenity Details</DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Name
                  </label>
                  <p className="text-base font-medium">{viewingAmenity.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Category
                  </label>
                  <p className="text-base">{viewingAmenity.category}</p>
                </div>
                {viewingAmenity.sub_category && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Sub Category
                    </label>
                    <p className="text-base">{viewingAmenity.sub_category}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Chargeable
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        viewingAmenity.is_chargeable
                          ? "bg-orange-500"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <Badge
                      variant={
                        viewingAmenity.is_chargeable ? "default" : "secondary"
                      }
                      className="font-medium"
                    >
                      {viewingAmenity.is_chargeable ? "💰 Yes" : "🆓 No"}
                    </Badge>
                  </div>
                </div>
                {viewingAmenity.is_chargeable && (
                  <>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Charge Amount
                      </label>
                      <p className="text-base">
                        ₹{viewingAmenity.charge_amount}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Charge Type
                      </label>
                      <p className="text-base">
                        {viewingAmenity.charge_type || "N/A"}
                      </p>
                    </div>
                  </>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Status
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        viewingAmenity.is_active
                          ? "bg-green-500"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <Badge
                      variant={
                        viewingAmenity.is_active ? "default" : "secondary"
                      }
                      className="font-medium"
                    >
                      {viewingAmenity.is_active ? "✓ Active" : "✕ Inactive"}
                    </Badge>
                  </div>
                </div>
                {viewingAmenity.properties_admin?.name && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Property
                    </label>
                    <p className="text-base">
                      {viewingAmenity.properties_admin.name}
                    </p>
                  </div>
                )}
              </div>

              {viewingAmenity.description && (
                <div className="border-t pt-4">
                  <label className="text-sm font-medium text-muted-foreground">
                    Description
                  </label>
                  <p className="text-base mt-1">{viewingAmenity.description}</p>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Amenities;
