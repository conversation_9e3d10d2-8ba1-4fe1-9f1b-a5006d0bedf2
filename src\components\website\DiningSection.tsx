import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { PhoneCall, Check } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import * as LucideIcons from 'lucide-react';
import TableReservationModal from '@/components/TableReservationModal';

interface DiningExperience {
  id: string;
  title: string;
  description: string;
  icon: string;
  items: string[];
  display_order: number;
  is_active: boolean;
}

interface Menu {
  id: string;
  title: string;
  description: string;
  pdf_url: string;
  display_order: number;
  is_active: boolean;
}

const DiningSection: React.FC = () => {
  const [diningExperiences, setDiningExperiences] = useState<DiningExperience[]>([]);
  const [menus, setMenus] = useState<Menu[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    fetchDiningData();
  }, []);

  const fetchDiningData = async () => {
    try {
      const [diningData, menusData] = await Promise.all([
        supabase.from('dining_experiences').select('*').eq('is_active', true).order('display_order'),
        supabase.from('menus').select('*').eq('is_active', true).order('display_order')
      ]);

      setDiningExperiences(diningData.data || []);
      setMenus(menusData.data || []);
    } catch (error) {
      console.error('Error fetching dining data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIconComponent = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName] || LucideIcons.Star;
    return IconComponent;
  };

  if (loading) {
    return (
      <section className="bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-12"></div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-64 bg-slate-200 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="bg-white">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Signature <span className="text-amber-600">Dining</span> Experience
          </h2>
          <p className="mt-3 text-slate-600 max-w-2xl mx-auto">
            Savor authentic flavors with our curated menu featuring Indian, Continental, and Pan-Asian cuisines
          </p>
        </motion.div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {diningExperiences.map((category, index) => {
            const IconComponent = getIconComponent(category.icon);
            return (
              <motion.div 
                key={category.id} 
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="bg-slate-50 rounded-2xl p-6 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
              >
                <motion.div 
                  initial={{ opacity: 0, scale: 0.5, rotate: -180 }}
                  whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 + (index * 0.1), ease: "easeOut" }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mb-4"
                >
                  <IconComponent className="w-6 h-6" />
                </motion.div>
                <motion.h3 
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.4 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[18px] font-semibold text-emerald-950 mb-2"
                >
                  {category.title}
                </motion.h3>
                <motion.p 
                  initial={{ opacity: 0, x: -15 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[13px] text-slate-600 mb-4"
                >
                  {category.description}
                </motion.p>
                <div className="space-y-2">
                  {category.items?.map((item, idx) => (
                    <motion.div 
                      key={idx} 
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.6 + (index * 0.1) + (idx * 0.05) }}
                      viewport={{ once: true }}
                      whileHover={{ x: 5 }}
                      className="flex items-center gap-2 text-[13px] text-slate-700"
                    >
                      <motion.div
                        initial={{ scale: 0 }}
                        whileInView={{ scale: 1 }}
                        transition={{ duration: 0.2, delay: 0.7 + (index * 0.1) + (idx * 0.05) }}
                        viewport={{ once: true }}
                      >
                        <Check className="w-3 h-3 text-emerald-700" />
                      </motion.div>
                      <span>{item}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            );
          })}
        </div>

        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mt-10"
        >
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <motion.button 
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsModalOpen(true)}
              className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[14px] text-emerald-950 ring-1 ring-slate-300 hover:bg-emerald-50 hover:ring-emerald-300 transition"
            >
              <PhoneCall className="w-4 h-4" />
              Reserve Your Table
            </motion.button>
            {menus.length > 0 && (
              <motion.a 
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                href={menus[0].pdf_url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[14px] text-white bg-emerald-900 hover:bg-emerald-800 transition"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                </svg>
                View Menu PDF
              </motion.a>
            )}
          </div>
        </motion.div>
      </motion.div>
      
      <TableReservationModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </section>
  );
};

export default DiningSection;