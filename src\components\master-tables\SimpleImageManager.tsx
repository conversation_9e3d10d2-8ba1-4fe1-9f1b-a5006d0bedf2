import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { ImageUploadField } from '../admin/ImageUploadField';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface SimpleImageManagerProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  multiple?: boolean;
}

const SimpleImageManager: React.FC<SimpleImageManagerProps> = ({
  images,
  onImagesChange,
  multiple = true
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newImageUrl, setNewImageUrl] = useState('');
  const [uploading, setUploading] = useState(false);
  const { toast } = useToast();

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `master-tables/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      if (multiple) {
        setNewImageUrl(data.publicUrl);
      } else {
        onImagesChange([data.publicUrl]);
      }

      toast({
        title: "Success",
        description: "Image uploaded successfully",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Error",
        description: "Failed to upload image",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      event.target.value = '';
    }
  };

  const addImage = () => {
    if (!newImageUrl.trim()) return;
    onImagesChange([...images, newImageUrl.trim()]);
    setNewImageUrl('');
    setShowAddForm(false);
  };

  const removeImage = (index: number) => {
    onImagesChange(images.filter((_, i) => i !== index));
  };

  if (!multiple) {
    return (
      <div className="space-y-4">
        <Label className="text-base font-medium">Image</Label>
        <div className="flex gap-4">
          <div className="flex-1">
            <ImageUploadField
              value={images[0] || ''}
              onChange={(value) => onImagesChange([value])}
              onFileUpload={handleImageUpload}
              uploading={uploading}
              placeholder="Enter image URL or upload image"
            />
          </div>
          {images[0] && (
            <img
              src={images[0]}
              alt="Preview"
              className="w-16 h-16 object-cover rounded"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder.svg';
              }}
            />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Label className="text-base font-medium">Images</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowAddForm(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Image
        </Button>
      </div>

      <div className="grid gap-4">
        {images.map((imageUrl, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-center gap-4">
              <img
                src={imageUrl}
                alt={`Image ${index + 1}`}
                className="w-16 h-16 object-cover rounded"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
              <div className="flex-1">
                <p className="text-sm font-medium truncate">{imageUrl}</p>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeImage(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {showAddForm && (
        <Card className="p-4 border-dashed">
          <div className="space-y-4">
            <div>
              <Label htmlFor="image_url">Image URL *</Label>
              <ImageUploadField
                value={newImageUrl}
                onChange={setNewImageUrl}
                onFileUpload={handleImageUpload}
                uploading={uploading}
                placeholder="Enter image URL or upload image"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
              >
                Cancel
              </Button>
              <Button type="button" onClick={addImage}>
                Add Image
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default SimpleImageManager;