-- Add multiple images support for properties and rooms

-- Property Images Table
CREATE TABLE public.property_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties_admin(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_title VARCHAR(255),
    image_description TEXT,
    image_type VARCHAR(50) CHECK (image_type IN ('EXTERIOR', 'INTERIOR', 'LOBBY', 'RESTAURANT', 'POOL', 'SPA', 'GARDEN', 'ROOM', 'AMENITY', 'OTHER')),
    display_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Room Images Table
CREATE TABLE public.room_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_id UUID NOT NULL REFERENCES rooms(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_title VARCHAR(255),
    image_description TEXT,
    image_type VARCHAR(50) CHECK (image_type IN ('BEDROOM', 'BATHROOM', 'BALCONY', 'VIEW', 'AMENITY', 'ENTRANCE', 'OTHER')),
    display_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Room Type Images Table (for room type gallery)
CREATE TABLE public.room_type_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_type_id UUID NOT NULL REFERENCES room_types(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_title VARCHAR(255),
    image_description TEXT,
    image_type VARCHAR(50) CHECK (image_type IN ('BEDROOM', 'BATHROOM', 'BALCONY', 'VIEW', 'AMENITY', 'LAYOUT', 'OTHER')),
    display_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Add indexes for better performance
CREATE INDEX idx_property_images_property_id ON property_images(property_id);
CREATE INDEX idx_property_images_display_order ON property_images(display_order);
CREATE INDEX idx_property_images_is_primary ON property_images(is_primary);

CREATE INDEX idx_room_images_room_id ON room_images(room_id);
CREATE INDEX idx_room_images_display_order ON room_images(display_order);
CREATE INDEX idx_room_images_is_primary ON room_images(is_primary);

CREATE INDEX idx_room_type_images_room_type_id ON room_type_images(room_type_id);
CREATE INDEX idx_room_type_images_display_order ON room_type_images(display_order);
CREATE INDEX idx_room_type_images_is_primary ON room_type_images(is_primary);

-- Add comments
COMMENT ON TABLE property_images IS 'Multiple images for properties with categorization and ordering';
COMMENT ON TABLE room_images IS 'Multiple images for individual rooms';
COMMENT ON TABLE room_type_images IS 'Multiple images for room types (template gallery)';

-- Enable Row Level Security
ALTER TABLE public.property_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_type_images ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Authenticated users can manage property_images" ON public.property_images FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage room_images" ON public.room_images FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage room_type_images" ON public.room_type_images FOR ALL USING (auth.role() = 'authenticated');

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_property_images_updated_at
    BEFORE UPDATE ON public.property_images
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_room_images_updated_at
    BEFORE UPDATE ON public.room_images
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_room_type_images_updated_at
    BEFORE UPDATE ON public.room_type_images
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample images data
INSERT INTO public.property_images (property_id, image_url, image_title, image_type, display_order, is_primary) VALUES
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), '/p/583A8268 - Copy.JPG', 'Resort Exterior View', 'EXTERIOR', 1, true),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), '/p/583A8274 - Copy.JPG', 'Main Lobby', 'LOBBY', 2, false),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), '/p/e1.JPG', 'Swimming Pool Area', 'POOL', 3, false),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), '/p/e2.jpg', 'Restaurant Interior', 'RESTAURANT', 4, false),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), '/p/e3.jpg', 'Garden View', 'GARDEN', 5, false);

INSERT INTO public.room_type_images (room_type_id, image_url, image_title, image_type, display_order, is_primary) VALUES
((SELECT id FROM room_types WHERE code = 'DLX' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '/p/e10.jpg', 'Deluxe Room Bedroom', 'BEDROOM', 1, true),
((SELECT id FROM room_types WHERE code = 'DLX' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '/p/e11.jpg', 'Deluxe Room Bathroom', 'BATHROOM', 2, false),
((SELECT id FROM room_types WHERE code = 'STE' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '/p/e15.jpg', 'Suite Living Area', 'BEDROOM', 1, true),
((SELECT id FROM room_types WHERE code = 'STE' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '/p/e16.jpg', 'Suite Balcony View', 'BALCONY', 2, false);

-- Function to ensure only one primary image per entity
CREATE OR REPLACE FUNCTION ensure_single_primary_property_image()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_primary = true THEN
        UPDATE property_images 
        SET is_primary = false 
        WHERE property_id = NEW.property_id 
        AND id != NEW.id 
        AND is_primary = true;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION ensure_single_primary_room_image()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_primary = true THEN
        UPDATE room_images 
        SET is_primary = false 
        WHERE room_id = NEW.room_id 
        AND id != NEW.id 
        AND is_primary = true;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION ensure_single_primary_room_type_image()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_primary = true THEN
        UPDATE room_type_images 
        SET is_primary = false 
        WHERE room_type_id = NEW.room_type_id 
        AND id != NEW.id 
        AND is_primary = true;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to ensure single primary image
CREATE TRIGGER ensure_single_primary_property_image_trigger
    BEFORE INSERT OR UPDATE ON property_images
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_primary_property_image();

CREATE TRIGGER ensure_single_primary_room_image_trigger
    BEFORE INSERT OR UPDATE ON room_images
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_primary_room_image();

CREATE TRIGGER ensure_single_primary_room_type_image_trigger
    BEFORE INSERT OR UPDATE ON room_type_images
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_primary_room_type_image();