import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import * as LucideIcons from 'lucide-react';

interface OperatingHour {
  id: string;
  service_name: string;
  icon: string;
  schedule: Record<string, string>;
  display_order: number;
  is_active: boolean;
}

const OperatingHoursSection: React.FC = () => {
  const [operatingHours, setOperatingHours] = useState<OperatingHour[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOperatingHours();
  }, []);

  const fetchOperatingHours = async () => {
    try {
      const { data } = await supabase
        .from('operating_hours')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      setOperatingHours(data || []);
    } catch (error) {
      console.error('Error fetching operating hours:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIconComponent = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName] || LucideIcons.Clock;
    return IconComponent;
  };

  if (loading) {
    return (
      <section className="bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-12"></div>
            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-48 bg-slate-200 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (operatingHours.length === 0) {
    return null;
  }

  return (
    <section className="bg-white">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Operating Hours
          </h2>
          <p className="mt-3 text-slate-600">
            We're here to serve you with convenient timings throughout the week
          </p>
        </motion.div>
        
        <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          {operatingHours.map((hours, index) => {
            const IconComponent = getIconComponent(hours.icon);
            return (
              <motion.div 
                key={hours.id} 
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4 + (index * 0.15), ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="text-center p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300 bg-slate-50"
              >
                <motion.div 
                  initial={{ scale: 0, rotate: -180 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 + (index * 0.15) }}
                  viewport={{ once: true }}
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mx-auto mb-4"
                >
                  <IconComponent className="w-6 h-6" />
                </motion.div>
                <motion.h3 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.7 + (index * 0.15) }}
                  viewport={{ once: true }}
                  className="text-[18px] font-semibold text-emerald-950 mb-4"
                >
                  {hours.service_name}
                </motion.h3>
                <motion.div 
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.8 + (index * 0.15) }}
                  viewport={{ once: true }}
                  className="space-y-3 text-[14px]"
                >
                  {Object.entries(hours.schedule || {}).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-slate-600">{key}:</span>
                      <span className="font-medium text-slate-900">{value}</span>
                    </div>
                  ))}
                </motion.div>
              </motion.div>
            );
          })}
        </div>
      </motion.div>
    </section>
  );
};

export default OperatingHoursSection;