// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://whotybnlcucquzalbwtn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indob3R5Ym5sY3VjcXV6YWxid3RuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg3MDU4MTEsImV4cCI6MjA3NDI4MTgxMX0.7NEmelmonwzjet5viRx7jFs2Z2dsEQjl7f-qHLBpBmA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});