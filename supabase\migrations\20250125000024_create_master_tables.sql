-- Create master tables for property management system

-- Properties Admin Master Table
CREATE TABLE public.properties_admin (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    
    -- Property Classification
    type VARCHAR(50) NOT NULL CHECK (type IN ('HOTEL', 'RESORT', 'VILLA', 'APARTMENT', 'HOSTEL', 'BED_AND_BREAKFAST')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('BUDGET', 'STANDARD', 'DELUXE', 'LUXURY', 'SUPER_LUXURY')),
    star_rating INTEGER CHECK (star_rating BETWEEN 1 AND 7),
    
    -- Contact Information
    email VARCHAR(255),
    phone VARCHAR(20),
    alternate_phone VARCHAR(20),
    
    -- Address Information
    address_line1 TEXT NOT NULL,
    address_line2 TEXT,
    landmark VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL DEFAULT 'India',
    pincode VARCHAR(20) NOT NULL,
    
    -- Operational Settings
    currency VARCHAR(3) NOT NULL DEFAULT 'INR',
    fiscal_year_start DATE DEFAULT '2024-04-01',
    check_in_time TIME NOT NULL DEFAULT '14:00:00',
    check_out_time TIME NOT NULL DEFAULT '12:00:00',
    late_check_out_until TIME DEFAULT '14:00:00',
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Room Types Master Table
CREATE TABLE public.room_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties_admin(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- Occupancy Details
    base_occupancy INTEGER NOT NULL CHECK (base_occupancy > 0),
    max_occupancy INTEGER NOT NULL CHECK (max_occupancy >= base_occupancy),
    extra_bed_capacity INTEGER DEFAULT 0 CHECK (extra_bed_capacity >= 0),
    extra_bed_charge DECIMAL(10,2) DEFAULT 0,
    
    -- Physical Attributes
    bed_type VARCHAR(100),
    bed_count INTEGER DEFAULT 1,
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    UNIQUE(property_id, code)
);

-- Amenities Admin Master Table
CREATE TABLE public.amenities_admin (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID REFERENCES properties_admin(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('ROOM', 'BATHROOM', 'TECHNOLOGY', 'FACILITY', 'SERVICE')),
    sub_category VARCHAR(100),
    description TEXT,
    
    -- Pricing
    is_chargeable BOOLEAN NOT NULL DEFAULT false,
    charge_amount DECIMAL(10,2) DEFAULT 0,
    charge_type VARCHAR(20) CHECK (charge_type IN ('PER_NIGHT', 'ONE_TIME', 'PER_PERSON')),
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(property_id, name)
);

-- Room Type Amenities Junction Table
CREATE TABLE public.room_type_amenities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_type_id UUID NOT NULL REFERENCES room_types(id) ON DELETE CASCADE,
    amenity_id UUID NOT NULL REFERENCES amenities_admin(id) ON DELETE CASCADE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(room_type_id, amenity_id)
);

-- Rooms Inventory Table
CREATE TABLE public.rooms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties_admin(id) ON DELETE CASCADE,
    room_type_id UUID NOT NULL REFERENCES room_types(id) ON DELETE CASCADE,
    room_number VARCHAR(20) NOT NULL,
    floor INTEGER CHECK (floor >= 0),
    wing VARCHAR(50),
    view_type VARCHAR(50),
    
    -- Status Management
    status VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE' CHECK (status IN ('AVAILABLE', 'OCCUPIED', 'MAINTENANCE', 'OUT_OF_ORDER', 'CLEANING')),
    key_code VARCHAR(50),
    tax_percentages VARCHAR(50),
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    UNIQUE(property_id, room_number)
);

-- Add comments
COMMENT ON TABLE properties_admin IS 'Master table storing property/organization details and settings';
COMMENT ON COLUMN properties_admin.code IS 'Unique property code used in numbering systems';
COMMENT ON TABLE room_types IS 'Defines room categories with occupancy and attribute details';
COMMENT ON COLUMN room_types.code IS 'Short code for room type (e.g., DBL, STD, DLX)';
COMMENT ON TABLE amenities_admin IS 'Master catalogue of amenities with pricing and category info';
COMMENT ON TABLE room_type_amenities IS 'Many-to-many relationship between room types and amenities';
COMMENT ON TABLE rooms IS 'Physical room inventory with status and attribute management';

-- Enable Row Level Security
ALTER TABLE public.properties_admin ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.amenities_admin ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_type_amenities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for authenticated users
CREATE POLICY "Authenticated users can manage properties_admin" ON public.properties_admin FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage room_types" ON public.room_types FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage amenities_admin" ON public.amenities_admin FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage room_type_amenities" ON public.room_type_amenities FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage rooms" ON public.rooms FOR ALL USING (auth.role() = 'authenticated');

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_properties_admin_updated_at
    BEFORE UPDATE ON public.properties_admin
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_room_types_updated_at
    BEFORE UPDATE ON public.room_types
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_amenities_admin_updated_at
    BEFORE UPDATE ON public.amenities_admin
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_rooms_updated_at
    BEFORE UPDATE ON public.rooms
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample data
-- Sample Properties
INSERT INTO public.properties_admin (name, code, description, type, category, star_rating, email, phone, address_line1, city, state, pincode) VALUES
('Meadow De Jalsa Resort', 'MDJ001', 'Luxury resort with world-class amenities', 'RESORT', 'LUXURY', 5, '<EMAIL>', '+91-87400-27008', 'Bardoli–Vyara Highway', 'Bardoli', 'Gujarat', '394601'),
('Red Chili Grand Hotel', 'RCH001', 'Premium business hotel in city center', 'HOTEL', 'DELUXE', 4, '<EMAIL>', '+91-98765-43210', '123 Business District', 'Surat', 'Gujarat', '395007'),
('Coastal Villa Resort', 'CVR001', 'Beachfront luxury villas', 'VILLA', 'SUPER_LUXURY', 6, '<EMAIL>', '+91-87654-32109', 'Marine Drive', 'Diu', 'Daman and Diu', '362520');

-- Sample Room Types
INSERT INTO public.room_types (property_id, name, code, description, base_occupancy, max_occupancy, extra_bed_capacity, extra_bed_charge, bed_type, bed_count) VALUES
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), 'Deluxe Room', 'DLX', 'Spacious room with garden view', 2, 3, 1, 1500.00, 'King', 1),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), 'Suite', 'STE', 'Luxury suite with separate living area', 2, 4, 2, 2000.00, 'King', 1),
((SELECT id FROM properties_admin WHERE code = 'RCH001'), 'Standard Room', 'STD', 'Comfortable standard accommodation', 2, 2, 0, 0.00, 'Queen', 1),
((SELECT id FROM properties_admin WHERE code = 'RCH001'), 'Executive Room', 'EXE', 'Premium room with business amenities', 2, 3, 1, 1200.00, 'King', 1),
((SELECT id FROM properties_admin WHERE code = 'CVR001'), 'Beach Villa', 'BVL', 'Private villa with direct beach access', 4, 6, 2, 3000.00, 'King', 2);

-- Sample Amenities
INSERT INTO public.amenities_admin (property_id, name, category, sub_category, description, is_chargeable, charge_amount, charge_type) VALUES
(NULL, 'Wi-Fi', 'TECHNOLOGY', 'Internet', 'High-speed wireless internet', false, 0, NULL),
(NULL, 'Air Conditioning', 'ROOM', 'Climate Control', 'Individual room AC control', false, 0, NULL),
(NULL, 'Mini Bar', 'ROOM', 'Refreshments', 'In-room mini refrigerator with beverages', true, 500.00, 'PER_NIGHT'),
(NULL, 'Room Service', 'SERVICE', 'Food & Beverage', '24/7 in-room dining service', true, 100.00, 'PER_NIGHT'),
(NULL, 'Spa Services', 'SERVICE', 'Wellness', 'Professional spa and massage services', true, 2500.00, 'ONE_TIME'),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), 'Private Pool', 'FACILITY', 'Recreation', 'Exclusive pool access for suite guests', true, 1000.00, 'PER_NIGHT'),
((SELECT id FROM properties_admin WHERE code = 'CVR001'), 'Beach Access', 'FACILITY', 'Recreation', 'Direct private beach access', false, 0, NULL);

-- Sample Rooms
INSERT INTO public.rooms (property_id, room_type_id, room_number, floor, wing, view_type, status, key_code) VALUES
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), (SELECT id FROM room_types WHERE code = 'DLX' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '101', 1, 'East Wing', 'Garden View', 'AVAILABLE', 'MDJ101'),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), (SELECT id FROM room_types WHERE code = 'DLX' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '102', 1, 'East Wing', 'Garden View', 'AVAILABLE', 'MDJ102'),
((SELECT id FROM properties_admin WHERE code = 'MDJ001'), (SELECT id FROM room_types WHERE code = 'STE' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '201', 2, 'West Wing', 'Pool View', 'AVAILABLE', 'MDJ201'),
((SELECT id FROM properties_admin WHERE code = 'RCH001'), (SELECT id FROM room_types WHERE code = 'STD' AND property_id = (SELECT id FROM properties_admin WHERE code = 'RCH001')), '301', 3, 'North Tower', 'City View', 'AVAILABLE', 'RCH301'),
((SELECT id FROM properties_admin WHERE code = 'RCH001'), (SELECT id FROM room_types WHERE code = 'EXE' AND property_id = (SELECT id FROM properties_admin WHERE code = 'RCH001')), '401', 4, 'South Tower', 'City View', 'OCCUPIED', 'RCH401'),
((SELECT id FROM properties_admin WHERE code = 'CVR001'), (SELECT id FROM room_types WHERE code = 'BVL' AND property_id = (SELECT id FROM properties_admin WHERE code = 'CVR001')), 'V01', 0, 'Beachfront', 'Sea View', 'AVAILABLE', 'CVR001');

-- Sample Room Type Amenities Associations
INSERT INTO public.room_type_amenities (room_type_id, amenity_id) VALUES
((SELECT id FROM room_types WHERE code = 'DLX'), (SELECT id FROM amenities_admin WHERE name = 'Wi-Fi' AND property_id IS NULL)),
((SELECT id FROM room_types WHERE code = 'DLX'), (SELECT id FROM amenities_admin WHERE name = 'Air Conditioning' AND property_id IS NULL)),
((SELECT id FROM room_types WHERE code = 'STE'), (SELECT id FROM amenities_admin WHERE name = 'Wi-Fi' AND property_id IS NULL)),
((SELECT id FROM room_types WHERE code = 'STE'), (SELECT id FROM amenities_admin WHERE name = 'Air Conditioning' AND property_id IS NULL)),
((SELECT id FROM room_types WHERE code = 'STE'), (SELECT id FROM amenities_admin WHERE name = 'Mini Bar' AND property_id IS NULL)),
((SELECT id FROM room_types WHERE code = 'STE'), (SELECT id FROM amenities_admin WHERE name = 'Private Pool')),
((SELECT id FROM room_types WHERE code = 'BVL'), (SELECT id FROM amenities_admin WHERE name = 'Beach Access'));