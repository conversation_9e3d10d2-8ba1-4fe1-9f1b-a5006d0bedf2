-- Create dining_experiences table
CREATE TABLE IF NOT EXISTS public.dining_experiences (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text NOT NULL,
  icon text NOT NULL,
  items text[] DEFAULT '{}',
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create menus table for PDF management
CREATE TABLE IF NOT EXISTS public.menus (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text,
  pdf_url text NOT NULL,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.dining_experiences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.menus ENABLE ROW LEVEL SECURITY;

-- Create policies for dining_experiences
CREATE POLICY "Allow public read access to dining_experiences" ON public.dining_experiences
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage dining_experiences" ON public.dining_experiences
  FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for menus
CREATE POLICY "Allow public read access to menus" ON public.menus
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage menus" ON public.menus
  FOR ALL USING (auth.role() = 'authenticated');

-- Create updated_at triggers
CREATE TRIGGER handle_dining_experiences_updated_at
  BEFORE UPDATE ON public.dining_experiences
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_menus_updated_at
  BEFORE UPDATE ON public.menus
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample dining experiences
INSERT INTO public.dining_experiences (title, description, icon, items, display_order, is_active) VALUES
('Signature Starters', 'Expertly crafted appetizers to begin your culinary journey', 'Flame', ARRAY['Tandoori Platter', 'Paneer Tikka', 'Chicken Seekh Kebab', 'Veg Spring Rolls'], 1, true),
('Main Course Delights', 'Authentic flavors from Indian, Continental & Pan-Asian cuisines', 'ChefHat', ARRAY['Butter Chicken', 'Dal Makhani', 'Thai Green Curry', 'Grilled Fish'], 2, true),
('Beverages & Mocktails', 'Refreshing drinks and signature mocktails', 'Wine', ARRAY['Fresh Lime Soda', 'Virgin Mojito', 'Masala Chai', 'Fresh Juices'], 3, true),
('Desserts & Sweets', 'Sweet endings to complete your dining experience', 'Coffee', ARRAY['Sizzling Brownie', 'Gulab Jamun', 'Ice Cream', 'Kulfi'], 4, true);

-- Insert sample menu
INSERT INTO public.menus (title, description, pdf_url, display_order, is_active) VALUES
('Complete Menu Card', 'Our full dining menu with all categories and pricing', '/new menu jalsa red chillies.pdf', 1, true);