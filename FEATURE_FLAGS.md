# Feature Flags System

This project includes a flexible feature flags system that allows you to control application features dynamically without code deployments.

## Overview

The feature flags system consists of:
- A `feature_flags` database table to store flag configurations
- A management interface in the Content Management dashboard
- React hooks and utilities for checking flags in components

## Database Schema

The `feature_flags` table includes:
- `flag_key`: Unique identifier for the flag (e.g., 'property_booking_enabled')
- `flag_name`: Human-readable name for the flag
- `description`: Detailed description of what the flag controls
- `is_enabled`: Boolean indicating if the flag is active
- `category`: Grouping category for organization

## Managing Feature Flags

### Admin Dashboard
1. Navigate to the Dashboard
2. Go to Content Management
3. Click on the "Feature Flags" tab
4. Use the toggle switches to enable/disable flags instantly
5. Add new flags using the "Add Feature Flag" button

### Available Categories
- **Properties**: Flags related to property listings and booking
- **Bookings**: Flags for reservation and booking features
- **Events**: Flags for event-related functionality
- **General**: Miscellaneous application features

## Using Feature Flags in Code

### 1. Using the Hook (Recommended)
```tsx
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import { FEATURE_FLAGS } from '@/utils/featureFlags';

const MyComponent = () => {
  const { isEnabled } = useFeatureFlags();
  
  return (
    <div>
      {isEnabled(FEATURE_FLAGS.PROPERTY_BOOKING_ENABLED) ? (
        <BookingButton />
      ) : (
        <ComingSoonButton />
      )}
    </div>
  );
};
```

### 2. Using Utility Functions
```tsx
import { getFeatureFlag, FEATURE_FLAGS } from '@/utils/featureFlags';

const checkFeature = async () => {
  const isEnabled = await getFeatureFlag(FEATURE_FLAGS.PROPERTY_BOOKING_ENABLED);
  if (isEnabled) {
    // Feature is enabled
  }
};
```

## Default Feature Flags

The system comes with these pre-configured flags:

### Property Booking Buttons (`property_booking_enabled`)
- **Category**: Properties
- **Description**: Show Details and Book buttons on properties page. When disabled, shows Coming Soon button instead.
- **Default**: Enabled

### Online Reservations (`online_reservations`)
- **Category**: Bookings
- **Description**: Enable online table reservations and room bookings
- **Default**: Enabled

### Event Booking (`event_booking`)
- **Category**: Events
- **Description**: Allow users to book events and venues online
- **Default**: Enabled

## Adding New Feature Flags

### 1. Add to Constants
Update `src/utils/featureFlags.ts`:
```tsx
export const FEATURE_FLAGS = {
  // ... existing flags
  NEW_FEATURE: 'new_feature_key',
} as const;
```

### 2. Create in Database
Use the admin interface or add to a migration:
```sql
INSERT INTO feature_flags (flag_key, flag_name, description, is_enabled, category) VALUES
('new_feature_key', 'New Feature Name', 'Description of what this controls', false, 'general');
```

### 3. Use in Components
```tsx
{isEnabled(FEATURE_FLAGS.NEW_FEATURE) && <NewFeatureComponent />}
```

## Best Practices

1. **Use Constants**: Always use the `FEATURE_FLAGS` constants instead of string literals
2. **Descriptive Names**: Use clear, descriptive flag names and descriptions
3. **Categories**: Organize flags into logical categories
4. **Default Values**: Consider the default state carefully (enabled vs disabled)
5. **Cleanup**: Remove unused flags and their code references when features are stable

## Migration File

The feature flags table is created by migration `20250125000023_create_feature_flags.sql`. This migration:
- Creates the `feature_flags` table with proper indexes
- Sets up Row Level Security (RLS) policies
- Inserts default feature flags
- Creates update triggers for timestamps

## Security

- Only authenticated users can manage feature flags
- Public users can only read enabled flags
- All flag operations are logged with timestamps
- RLS policies ensure proper access control