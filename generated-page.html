<html lang="en"><head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Meadow De Jalsa — Luxury Stay &amp; Events</title>
    <meta name="description" content="Meadow De Jalsa — Luxury stay, premium dining, and memorable events on Bardoli–Vyara Highway.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;500;600&amp;family=Inter:wght@300;400;500;600&amp;display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 100 100%27%3E%3Crect width=%27100%27 height=%27100%27 rx=%2718%27 fill=%27%230a1f1f%27/%3E%3Ctext x=%2750%27 y=%2758%27 font-family=%27Georgia%27 font-size=%2750%27 text-anchor=%27middle%27 fill=%27%23fbbf24%27%3EMJ%3C/text%3E%3C/svg%3E">
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "Meadow De Jalsa",
        "url": "https://www.meadowdejalsa.example",
        "telephone": "+91 87400-27008",
        "email": "<EMAIL>",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Bardoli–Vyara Highway",
          "addressLocality": "Bardoli",
          "addressRegion": "GJ",
          "postalCode": "394601",
          "addressCountry": "IN"
        },
        "amenityFeature": [
          { "@type": "LocationFeatureSpecification", "name": "Free WiFi", "value": true },
          { "@type": "LocationFeatureSpecification", "name": "Air Conditioning", "value": true },
          { "@type": "LocationFeatureSpecification", "name": "Room Service", "value": true }
        ],
        "checkinTime": "14:00",
        "checkoutTime": "11:00",
        "petsAllowed": false,
        "starRating": { "@type": "Rating", "ratingValue": "4.5" },
        "servesCuisine": [ "Indian", "Continental", "Chinese" ],
        "hasMap": "https://maps.google.com/?q=Bardoli-Vyara+Highway"
      }
    </script>
  </head>
  <body class="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900" style="font-family: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'; scroll-behavior: smooth;">
    <!-- Sticky Utility Bar -->
    <div class="fixed inset-x-0 top-0 z-50">
      <div class="w-full bg-white/80 backdrop-blur border-b border-slate-200">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-2 flex items-center justify-between">
          <div class="flex items-center gap-5 text-[13px]">
            <a href="tel:+************" class="inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
              <i data-lucide="phone" class="w-4 h-4"></i>
              <span>+91 87400-27008</span>
            </a>
            <a href="mailto:<EMAIL>" class="hidden sm:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
              <i data-lucide="mail" class="w-4 h-4"></i>
              <span><EMAIL></span>
            </a>
            <a href="#contact" class="hidden md:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
              <i data-lucide="map-pin" class="w-4 h-4"></i>
              <span>Bardoli–Vyara Highway</span>
            </a>
          </div>
          <div class="flex items-center gap-3">
            <a href="https://instagram.com" target="_blank" rel="noopener" class="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Instagram">
              <i data-lucide="instagram" class="w-4 h-4"></i>
            </a>
            <a href="https://facebook.com" target="_blank" rel="noopener" class="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Facebook">
              <i data-lucide="facebook" class="w-4 h-4"></i>
            </a>
          </div>
        </div>
      </div>

      <!-- Main Nav -->
      <nav class="w-full bg-white/90 backdrop-blur border-b border-slate-200">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
          <a href="#home" class="flex items-center gap-3">
            <div class="h-9 w-9 rounded-lg bg-emerald-950 text-amber-400 grid place-items-center text-sm tracking-tight font-semibold" style="font-family: 'Cormorant Garamond', serif;">MJ</div>
            <div class="flex flex-col leading-tight">
              <span class="text-[15px] sm:text-[16px] text-emerald-950 font-semibold tracking-tight" style="font-family: 'Cormorant Garamond', serif;">Meadow De Jalsa</span>
              <span class="text-[11px] text-slate-500">Luxury Stay &amp; Events</span>
            </div>
          </a>
          <button id="navToggle" class="lg:hidden inline-flex items-center justify-center rounded-md p-2 hover:bg-slate-100 text-slate-700 hover:text-emerald-900 transition" aria-label="Toggle navigation">
            <i data-lucide="menu" class="w-5 h-5"></i>
          </button>
          <div id="navMenu" class="hidden lg:flex items-center gap-1">
            <a href="#home" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Home</a>
            <a href="#about" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">About</a>
            <a href="#rooms" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Rooms</a>
            <a href="#restaurant" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Restaurant</a>
            <a href="#events" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Events</a>
            <a href="#gallery" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Gallery</a>
            <a href="#contact" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Contact</a>
            <button id="bookNowTop" class="ml-2 inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium tracking-tight shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-900/30">
              <i data-lucide="calendar" class="w-4 h-4"></i>
              Book Now
            </button>
          </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobileMenu" class="hidden lg:hidden border-t border-slate-200 bg-white">
          <div class="px-4 py-3 flex flex-col gap-1">
            <a href="#home" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Home</a>
            <a href="#about" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">About</a>
            <a href="#rooms" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Rooms</a>
            <a href="#restaurant" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Restaurant</a>
            <a href="#events" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Events</a>
            <a href="#gallery" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Gallery</a>
            <a href="#contact" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Contact</a>
            <button id="bookNowTopMobile" class="mt-2 inline-flex items-center justify-center gap-2 rounded-full px-4 py-2 text-[15px] text-emerald-950 font-medium tracking-tight shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
              <i data-lucide="calendar" class="w-4 h-4"></i>
              Book Now
            </button>
          </div>
        </div>
      </nav>
    </div>

    <main class="pt-[128px]">
      <!-- HOME -->
      <section id="home" class="relative">
        <!-- Hero -->
        <div class="relative h-[72vh] sm:h-[80vh] w-full overflow-hidden">
          <video class="absolute inset-0 w-full h-full object-cover" src="https://videos.pexels.com/video-files/856130/856130-hd_1920_1080_25fps.mp4" autoplay="" loop="" muted="" playsinline="" poster="https://images.unsplash.com/photo-1501117716987-c8e2a3a67a0b?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" aria-label="Aerial view of property"></video>
          <div class="absolute inset-0 bg-gradient-to-b from-emerald-950/60 via-emerald-950/40 to-emerald-950/60"></div>
          <div class="relative z-10 h-full">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full grid">
              <div class="m-auto text-center">
                <div class="inline-flex items-center gap-3 px-3 py-1.5 rounded-full bg-white/10 backdrop-blur ring-1 ring-white/20 mb-5">
                  <i data-lucide="map-pin" class="w-4 h-4 text-amber-300"></i>
                  <span class="text-white/90 text-[12px] sm:text-[13px]">Bardoli–Vyara Highway</span>
                </div>
                <h1 class="text-white tracking-tight text-4xl sm:text-5xl lg:text-6xl font-semibold leading-[1.1]" style="font-family: 'Cormorant Garamond', serif;">
                  Where Luxury Meets Tradition
                </h1>
                <p class="mt-4 text-white/85 text-[15px] sm:text-[16px] max-w-2xl mx-auto">
                  Experience elegant stays, gourmet dining, and unforgettable celebrations at Meadow De Jalsa.
                </p>
                <div class="mt-8 flex items-center justify-center gap-3">
                  <button class="group inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/50" id="bookNowHero">
                    <i data-lucide="calendar-range" class="w-5 h-5"></i>
                    Book Your Stay
                  </button>
                  <a href="#events" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white/90 ring-1 ring-white/30 hover:bg-white/10 transition">
                    <i data-lucide="sparkles" class="w-5 h-5"></i>
                    Plan Your Event
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Info Strip -->
        <div class="bg-white border-y border-slate-200">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-5 grid grid-cols-2 sm:grid-cols-4 gap-3">
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="key-round" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Check-in</div>
                <div class="text-sm font-medium">2:00 PM</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="clock-3" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Restaurant</div>
                <div class="text-sm font-medium">11 AM – 11 PM</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="wifi" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Connectivity</div>
                <div class="text-sm font-medium">High-speed WiFi</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="car" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Parking</div>
                <div class="text-sm font-medium">Ample &amp; Secure</div>
              </div>
            </div>
          </div>
        </div>

        <!-- About Snapshot -->
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
          <div class="text-center">
            <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">
              Luxury Stay &amp; Memorable Events on Bardoli–Vyara Highway
            </h2>
            <p class="mt-3 text-slate-600 max-w-2xl mx-auto">
              A modern retreat offering refined rooms, curated dining, and bespoke event experiences.
            </p>
          </div>
        </div>

        <!-- Highlights -->
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-14">
          <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Stay -->
            <a href="#rooms" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Rooms &amp; Suites" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="building-2" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Stay</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Rooms &amp; suites designed for comfort and elegance.</p>
              </div>
            </a>
            <!-- Dine -->
            <a href="#restaurant" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Restaurant" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="utensils" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Dine</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Taste luxury, taste tradition with curated cuisine.</p>
              </div>
            </a>
            <!-- Celebrate -->
            <a href="#events" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Events &amp; Banquets" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="party-popper" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Celebrate</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Weddings, corporate events, parties, and more.</p>
              </div>
            </a>
            <!-- Explore -->
            <a href="#about" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" alt="Nearby Attractions" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="map" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Explore</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Discover the best of Bardoli &amp; Vyara nearby.</p>
              </div>
            </a>
          </div>
        </div>

        <!-- Events Showcase Carousel -->
        <div class="bg-emerald-950">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-white tracking-tight text-2xl sm:text-3xl font-semibold" style="font-family: 'Cormorant Garamond', serif;">Events Showcase</h3>
              <div class="flex items-center gap-2">
                <button id="eventsPrev" class="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Previous">
                  <i data-lucide="chevron-left" class="w-5 h-5"></i>
                </button>
                <button id="eventsNext" class="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Next">
                  <i data-lucide="chevron-right" class="w-5 h-5"></i>
                </button>
              </div>
            </div>
            <div class="relative">
              <div id="eventsTrack" class="flex gap-4 overflow-hidden scroll-smooth">
                <!-- Slides -->
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1522673607200-164d1b6ce486?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Weddings" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Weddings</div>
                    <p class="mt-3 text-sm text-slate-600">Grand celebrations tailored to your story.</p>
                  </div>
                </div>
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Corporate Events" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Corporate</div>
                    <p class="mt-3 text-sm text-slate-600">Professional settings for conferences and meets.</p>
                  </div>
                </div>
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Parties" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Parties</div>
                    <p class="mt-3 text-sm text-slate-600">Birthdays and anniversaries with flair.</p>
                  </div>
                </div>
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Exhibitions" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Exhibitions</div>
                    <p class="mt-3 text-sm text-slate-600">Versatile layouts for shows and expos.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Social Live Feed -->
        <div class="bg-white">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-2xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Live Feed</h3>
              <div class="flex items-center gap-3">
                <a href="https://instagram.com" target="_blank" class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <i data-lucide="instagram" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px]">Instagram</span>
                </a>
                <a href="https://facebook.com" target="_blank" class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <i data-lucide="facebook" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px]">Facebook</span>
                </a>
              </div>
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
              <!-- 6 feed items -->
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&amp;q=80" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&amp;q=80" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
            </div>
          </div>
        </div>

        <!-- CTA -->
        <div class="bg-[url('https://images.unsplash.com/photo-1518277850984-94e89c892b26?q=80&amp;w=1760&amp;auto=format&amp;fit=crop')] bg-cover bg-center bg-fixed">
          <div class="bg-emerald-950/80">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
              <h3 class="text-white tracking-tight text-3xl sm:text-4xl font-semibold" style="font-family: 'Cormorant Garamond', serif;">Ready to make memories?</h3>
              <p class="mt-3 text-white/85">Reserve your stay or plan a bespoke event with our expert team.</p>
              <div class="mt-7 flex items-center justify-center gap-3">
                <button id="bookNowCTA" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                  <i data-lucide="calendar" class="w-5 h-5"></i>
                  Book Now
                </button>
                <a href="#contact" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                  <i data-lucide="messages-square" class="w-5 h-5"></i>
                  Talk to Us
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- ABOUT -->
      <section id="about" class="bg-white">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="grid lg:grid-cols-2 gap-10 items-center">
            <div>
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold mb-3" style="font-family: 'Cormorant Garamond', serif;">Meadow De Jalsa — where luxury meets tradition.</h2>
              <p class="text-slate-600">
                From a humble vision to a captivating destination, our story is woven with heartfelt hospitality, contemporary design, and timeless values.
              </p>
              <!-- Timeline -->
              <div class="mt-8 space-y-6">
                <div class="flex gap-4">
                  <div class="flex flex-col items-center">
                    <div class="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                    <div class="flex-1 w-px bg-slate-200"></div>
                  </div>
                  <div>
                    <div class="text-sm text-slate-500">Est. 2016</div>
                    <div class="text-[18px] font-medium text-emerald-950">Foundations</div>
                    <p class="text-sm text-slate-600">The idea of a luxury stopover on the Bardoli–Vyara highway comes alive.</p>
                  </div>
                </div>
                <div class="flex gap-4">
                  <div class="flex flex-col items-center">
                    <div class="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                    <div class="flex-1 w-px bg-slate-200"></div>
                  </div>
                  <div>
                    <div class="text-sm text-slate-500">2018–2021</div>
                    <div class="text-[18px] font-medium text-emerald-950">Growth</div>
                    <p class="text-sm text-slate-600">Rooms, banquet spaces, and a signature restaurant take shape.</p>
                  </div>
                </div>
                <div class="flex gap-4">
                  <div class="flex flex-col items-center">
                    <div class="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                    <div class="flex-1 w-px bg-slate-200"></div>
                  </div>
                  <div>
                    <div class="text-sm text-slate-500">Today</div>
                    <div class="text-[18px] font-medium text-emerald-950">Destination</div>
                    <p class="text-sm text-slate-600">A preferred venue for luxury stays and memorable events.</p>
                  </div>
                </div>
              </div>
              <!-- Highlights -->
              <div class="mt-8 grid sm:grid-cols-3 gap-3">
                <div class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="leaf" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Eco-friendly</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Energy-efficient lighting, water-saving fixtures, greenery.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="route" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Highway Location</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Seamless access and ample secure parking.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="medal" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Premium Hospitality</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Attentive service and bespoke experiences.</p>
                </div>
              </div>
            </div>
            <!-- Gallery Masonry -->
            <div class="columns-2 gap-3">
              <img src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Lobby">
              <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&amp;q=80" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Banquet">
              <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Dining">
              <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Room">
            </div>
          </div>

          <!-- Mission & Vision -->
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-4">
            <div class="grid md:grid-cols-2 gap-4">
              <div class="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                <h4 class="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style="font-family: 'Cormorant Garamond', serif;">Our Mission</h4>
                <p class="text-slate-600 text-[15px]">To craft refined stays and memorable events through attentive service, culinary excellence, and thoughtful design.</p>
              </div>
              <div class="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                <h4 class="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style="font-family: 'Cormorant Garamond', serif;">Our Vision</h4>
                <p class="text-slate-600 text-[15px]">To be the region’s most loved destination for celebrations and serene getaways.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- ROOMS & SUITES -->
      <section id="rooms" class="bg-slate-50">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="flex items-end justify-between gap-4">
            <div>
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Rooms &amp; Suites</h2>
              <p class="text-slate-600 mt-1">Choose your perfect stay with immersive visuals and amenities.</p>
            </div>
            <div class="flex items-center gap-2">
              <button data-filter="all" class="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">All</button>
              <button data-filter="Standard" class="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Standard</button>
              <button data-filter="Deluxe" class="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Deluxe</button>
              <button data-filter="Suite" class="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Suite</button>
            </div>
          </div>

          <div id="roomsGrid" class="mt-8 grid md:grid-cols-2 lg:grid-cols-3 gap-5">
            <!-- Room Card -->
            <div class="room-card" data-category="Standard">
              <div class="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <!-- Slider -->
                <div class="relative">
                  <div class="overflow-hidden">
                    <div class="flex transition-transform duration-500" data-slider="1" data-index="0">
                      <img src="https://images.unsplash.com/photo-1562790351-d273a961e0e9?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="h-48 w-full object-cover flex-shrink-0" alt="Standard Room 1">
                      <img src="https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="h-48 w-full object-cover flex-shrink-0" alt="Standard Room 2">
                      <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&amp;q=80" class="h-48 w-full object-cover flex-shrink-0" alt="Standard Room 3">
                    </div>
                  </div>
                  <button class="abs-btn left-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover              :bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-prev="1" aria-label="Previous image">
                    <i data-lucide="chevron-left" class="w-4 h-4"></i>
                  </button>
                  <button class="abs-btn right-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-next="1" aria-label="Next image">
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                  </button>
                  <div class="absolute inset-x-0 bottom-2 flex items-center justify-center gap-1.5">
                    <button class="h-1.5 w-4 rounded-full bg-white/70 data-[active=true]:bg-amber-400 transition" data-dot="1" data-to="0" aria-label="Slide 1"></button>
                    <button class="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot="1" data-to1"="" aria-label="Slide 2"></button>
                    <button class="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot="1" data-to="2" aria-label="Slide 3"></button>
                  </div>
                </div>
                <!-- Content -->
                <div class="p-4">
                  <div class="flex items-start justify-between gap-3">
                    <div>
                      <h3 class="text-[18px] font-semibold text-emerald-950">Standard Room</h3>
                      <p class="text-[13px] text-slate-500">Cozy comfort, perfect for solo travelers or couples.</p>
                    </div>
                    <div class="text-right">
                      <div class="text-[11px] text-slate-500">From</div>
                      <div class="text-[15px] font-semibold text-emerald-900">₹2,999</div>
                    </div>
                  </div>
                  <div class="mt-3 flex flex-wrap items-center gap-2 text-[12px] text-slate-600">
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="wifi" class="w-3.5 h-3.5"></i> WiFi</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="snowflake" class="w-3.5 h-3.5"></i> AC</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="tv-2" class="w-3.5 h-3.5"></i> Smart TV</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="cup-soda" class="w-3.5 h-3.5"></i> Minibar</span>
                  </div>
                  <div class="mt-4 flex items-center justify-between">
                    <button class="inline-flex items-center gap-2 text-[14px] text-emerald-900 hover:text-emerald-700 transition" data-open="bookingModal" data-room="Standard Room">
                      <i data-lucide="calendar" class="w-4 h-4"></i>
                      Book
                    </button>
                    <a href="#gallery" class="inline-flex items-center gap-2 text-[14px] text-slate-700 hover:text-emerald-900 transition">
                      <i data-lucide="image" class="w-4 h-4"></i>
                      Gallery
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Room Card -->
            <div class="room-card" data-category="Deluxe">
              <div class="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <!-- Slider -->
                <div class="relative">
                  <div class="overflow-hidden">
                    <div class="flex transition-transform duration-500" data-slider="2" data-index="0">
                      <img src="https://images.unsplash.com/photo-1554995207-c18c203602cb?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="h-48 w-full object-cover flex-shrink-0" alt="Deluxe Room 1">
                      <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" class="h-48 w-full object-cover flex-shrink-0" alt="Deluxe Room 2">
                      <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&amp;q=80" class="h-48 w-full object-cover flex-shrink-0" alt="Deluxe Room 3">
                    </div>
                  </div>
                  <button class="abs-btn left-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-prev="2" aria-label="Previous image">
                    <i data-lucide="chevron-left" class="w-4 h-4"></i>
                  </button>
                  <button class="abs-btn right-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-next="2" aria-label="Next image">
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                  </button>
                  <div class="absolute inset-x-0 bottom-2 flex items-center justify-center gap-1.5">
                    <button class="h-1.5 w-4 rounded-full bg-white/70 data-[active=true]:bg-amber-400 transition" data-dot="2" data-to="0" aria-label="Slide 1"></button>
                    <button class="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot="2" data-to="1" aria-label="Slide 2"></button>
                    <button class="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot="2" data-to="2" aria-label="Slide 3"></button>
                  </div>
                </div>
                <!-- Content -->
                <div class="p-4">
                  <div class="flex items-start justify-between gap-3">
                    <div>
                      <h3 class="text-[18px] font-semibold text-emerald-950">Deluxe Room</h3>
                      <p class="text-[13px] text-slate-500">Spacious elegance with premium amenities.</p>
                    </div>
                    <div class="text-right">
                      <div class="text-[11px] text-slate-500">From</div>
                      <div class="text-[15px] font-semibold text-emerald-900">₹4,499</div>
                    </div>
                  </div>
                  <div class="mt-3 flex flex-wrap items-center gap-2 text-[12px] text-slate-600">
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="wifi" class="w-3.5 h-3.5"></i> WiFi 200 Mbps</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="coffee" class="w-3.5 h-3.5"></i> Tea/Coffee</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="bed-double" class="w-3.5 h-3.5"></i> King Bed</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="shower-head" class="w-3.5 h-3.5"></i> Rain Shower</span>
                  </div>
                  <div class="mt-4 flex items-center justify-between">
                    <button class="inline-flex items-center gap-2 text-[14px] text-emerald-900 hover:text-emerald-700 transition" data-open="bookingModal" data-room="Deluxe Room">
                      <i data-lucide="calendar" class="w-4 h-4"></i>
                      Book
                    </button>
                    <a href="#gallery" class="inline-flex items-center gap-2 text-[14px] text-slate-700 hover:text-emerald-900 transition">
                      <i data-lucide="image" class="w-4 h-4"></i>
                      Gallery
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Room Card -->
            <div class="room-card" data-category="Suite">
              <div class="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <!-- Slider -->
                <div class="relative">
                  <div class="overflow-hidden">
                    <div class="flex transition-transform duration-500" data-slider="3" data-index="0">
                      <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&amp;q=80" class="h-48 w-full object-cover flex-shrink-0" alt="Suite 1">
                      <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" class="h-48 w-full object-cover flex-shrink-0" alt="Suite 2">
                      <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="h-48 w-full object-cover flex-shrink-0" alt="Suite 3">
                    </div>
                  </div>
                  <button class="abs-btn left-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-prev="3" aria-label="Previous image">
                    <i data-lucide="chevron-left" class="w-4 h-4"></i>
                  </button>
                  <button class="abs-btn right-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-next="3" aria-label="Next image">
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                  </button>
                  <div class="absolute inset-x-0 bottom-2 flex items-center justify-center gap-1.5">
                    <button class="h-1.5 w-4 rounded-full bg-white/70 data-[active=true]:bg-amber-400 transition" data-dot="3" data-to="0" aria-label="Slide 1"></button>
                    <button class="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot="3" data-to="1" aria-label="Slide 2"></button>
                    <button class="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot="3" data-to="2" aria-label="Slide 3"></button>
                  </div>
                </div>
                <!-- Content -->
                <div class="p-4">
                  <div class="flex items-start justify-between gap-3">
                    <div>
                      <h3 class="text-[18px] font-semibold text-emerald-950">Luxury Suite</h3>
                      <p class="text-[13px] text-slate-500">Separate living, curated interiors, premium comforts.</p>
                    </div>
                    <div class="text-right">
                      <div class="text-[11px] text-slate-500">From</div>
                      <div class="text-[15px] font-semibold text-emerald-900">₹6,999</div>
                    </div>
                  </div>
                  <div class="mt-3 flex flex-wrap items-center gap-2 text-[12px] text-slate-600">
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="concierge-bell" class="w-3.5 h-3.5"></i> Butler on-call</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="sofa" class="w-3.5 h-3.5"></i> Lounge</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="sparkles" class="w-3.5 h-3.5"></i> Luxury Linen</span>
                    <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="bath" class="w-3.5 h-3.5"></i> Soaking Tub</span>
                  </div>
                  <div class="mt-4 flex items-center justify-between">
                    <button class="inline-flex items-center gap-2 text-[14px] text-emerald-900 hover:text-emerald-700 transition" data-open="bookingModal" data-room="Luxury Suite">
                      <i data-lucide="calendar" class="w-4 h-4"></i>
                      Book
                    </button>
                    <a href="#gallery" class="inline-flex items-center gap-2 text-[14px] text-slate-700 hover:text-emerald-900 transition">
                      <i data-lucide="image" class="w-4 h-4"></i>
                      Gallery
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Amenities CTA -->
          <div class="mt-10 p-5 rounded-2xl bg-gradient-to-br from-emerald-50 to-amber-50 ring-1 ring-emerald-100/60">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div class="flex items-center gap-3">
                <div class="h-10 w-10 rounded-lg bg-emerald-900 text-amber-300 grid place-items-center">
                  <i data-lucide="check-circle-2" class="w-5 h-5"></i>
                </div>
                <p class="text-slate-700 text-[15px]">All stays include complimentary breakfast, free WiFi, and secure parking.</p>
              </div>
              <button class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500" data-open="bookingModal">
                <i data-lucide="calendar-range" class="w-4 h-4"></i>
                Check Availability
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- RESTAURANT -->
      <section id="restaurant" class="bg-white">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="grid lg:grid-cols-2 gap-10 items-center">
            <div class="space-y-4">
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Signature Restaurant</h2>
              <p class="text-slate-600">Savor a blend of Indian classics and global favorites in a refined setting. Fresh ingredients, handcrafted spices, and chef-led presentations.</p>
              <div class="grid sm:grid-cols-2 gap-3">
                <div class="p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="chef-hat" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Chef’s Specials</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Seasonal menus, farm-fresh produce.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="cocktail" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Mocktails &amp; Brews</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Creative sips crafted at the bar.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="leaf" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Veg-forward</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Plenty of vegetarian choices.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="clock-3" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">11 AM – 11 PM</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Lunch, hi-tea, and dinner daily.</p>
                </div>
              </div>
              <div class="flex items-center gap-3 pt-2">
                <button class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500" data-open="enquiryModal" data-type="Table Reservation">
                  <i data-lucide="calendar-check" class="w-4 h-4"></i>
                  Reserve a Table
                </button>
                <a href="#gallery" class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-900 ring-1 ring-slate-200 hover:bg-slate-50 transition">
                  <i data-lucide="image" class="w-4 h-4"></i>
                  View Ambience
                </a>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-3">
              <img src="https://images.unsplash.com/photo-1473093226795-af9932fe5856?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Dish 1">
              <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&amp;q=80" class="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Dish 2">
              <img src="https://images.unsplash.com/photo-1544025162-d76694265947?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Dining Room">
              <img src="https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Bar Counter">
            </div>
          </div>
        </div>
      </section>

      <!-- EVENTS -->
      <section id="events" class="bg-slate-50">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="grid lg:grid-cols-3 gap-8 items-start">
            <div class="lg:col-span-2">
              <img src="https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Banquet Hall" class="w-full h-64 sm:h-96 object-cover rounded-2xl ring-1 ring-slate-200">
            </div>
            <div class="space-y-4">
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Events &amp; Banquets</h2>
              <p class="text-slate-600">From intimate gatherings to grand weddings, our versatile venues, expert planners, and curated menus ensure unforgettable celebrations.</p>
              <ul class="space-y-2 text-[14px] text-slate-700">
                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i> Grand ballroom up to 600 guests</li>
                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i> Lawn space for outdoor ceremonies</li>
                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i> Themed decor and lighting packages</li>
                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i> Dedicated bridal and green rooms</li>
              </ul>
              <div class="pt-2">
                <button class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500" data-open="enquiryModal" data-type="Event Enquiry">
                  <i data-lucide="messages-square" class="w-4 h-4"></i>
                  Get a Quote
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- GALLERY -->
      <section id="gallery" class="bg-white">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="flex items-end justify-between gap-4">
            <div>
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Gallery</h2>
              <p class="text-slate-600 mt-1">Explore moments from stays, dining, and celebrations.</p>
            </div>
            <div class="flex items-center gap-2">
              <button class="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="all">All</button>
              <button class="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="rooms">Rooms</button>
              <button class="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="dining">Dining</button>
              <button class="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events">Events</button>
              <button class="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="outdoor">Outdoors</button>
            </div>
          </div>

          <div id="galleryGrid" class="mt-8 columns-2 md:columns-3 lg:columns-4 gap-3 [column-fill:_balance]">
            <!-- Items -->
            <a href="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="rooms" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Room Interior">
            </a>
            <a href="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="dining" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Dining Table">
            </a>
            <a href="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Wedding Decor">
            </a>
            <a href="https://images.unsplash.com/photo-1549877452-9c387954fbc6?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="outdoor" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&amp;q=80" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Outdoors">
            </a>
            <a href="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="dining" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Restaurant Interior">
            </a>
            <a href="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Exhibition">
            </a>
            <a href="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="rooms" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Lobby">
            </a>
            <a href="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&amp;w=2000&amp;auto=format&amp;fit=crop" class="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events" data-lightbox="">
              <img src="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="w-full object-cover group-hover:scale-[1.03] transition" alt="Corporate">
            </a>
          </div>
        </div>
      </section>

      <!-- CONTACT -->
      <section id="contact" class="bg-slate-50">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="grid lg:grid-cols-2 gap-8">
            <div class="space-y-4">
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Contact Us</h2>
              <p class="text-slate-600">We’d love to host you. Reach out for bookings, events, or dining reservations.</p>
              <div class="grid sm:grid-cols-2 gap-3">
                <a href="tel:+************" class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition flex items-start gap-3">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="phone" class="w-4 h-4"></i>
                  </div>
                  <div>
                    <div class="text-[12px] text-slate-500">Call</div>
                    <div class="text-[15px] font-medium text-emerald-950">+91 87400-27008</div>
                  </div>
                </a>
                <a href="mailto:<EMAIL>" class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition flex items-start gap-3">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="mail" class="w-4 h-4"></i>
                  </div>
                  <div>
                    <div class="text-[12px] text-slate-500">Email</div>
                    <div class="text-[15px] font-medium text-emerald-950"><EMAIL></div>
                  </div>
                </a>
                <div class="p-4 rounded-xl ring-1 ring-slate-200 flex items-start gap-3">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="map-pin" class="w-4 h-4"></i>
                  </div>
                  <div>
                    <div class="text-[12px] text-slate-500">Address</div>
                    <div class="text-[15px] font-medium text-emerald-950">Bardoli–Vyara Highway, Bardoli</div>
                  </div>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200 flex items-start gap-3">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="clock-4" class="w-4 h-4"></i>
                  </div>
                  <div>
                    <div class="text-[12px] text-slate-500">Timings</div>
                    <div class="text-[15px] font-medium text-emerald-950">Hotel: 24x7 • Restaurant: 11 AM – 11 PM</div>
                  </div>
                </div>
              </div>
              <div class="rounded-2xl overflow-hidden ring-1 ring-slate-200">
                <iframe title="Map to Meadow De Jalsa" src="https://maps.google.com/maps?q=Bardoli-Vyara%20Highway&amp;t=&amp;z=12&amp;ie=UTF8&amp;iwloc=&amp;output=embed" class="w-full h-64 border-0"></iframe>
              </div>
            </div>
            <form id="contactForm" class="bg-white p-6 rounded-2xl ring-1 ring-slate-200">
              <h3 class="text-xl font-semibold text-emerald-950">Send us a message</h3>
              <div class="mt-4 grid sm:grid-cols-2 gap-3">
                <div>
                  <label class="text-[12px] text-slate-500">Full Name</label>
                  <input type="text" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Your name">
                </div>
                <div>
                  <label class="text-[12px] text-slate-500">Phone</label>
                  <input type="tel" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="+91">
                </div>
                <div class="sm:col-span-2">
                  <label class="text-[12px] text-slate-500">Email</label>
                  <input type="email" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="<EMAIL>">
                </div>
                <div class="sm:col-span-2">
                  <label class="text-[12px] text-slate-500">Message</label>
                  <textarea rows="4" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Tell us about your plan..."></textarea>
                </div>
              </div>
              <div class="mt-4 flex items-center justify-between">
                <label class="inline-flex items-center gap-2 text-[12px] text-slate-600">
                  <input type="checkbox" class="rounded border-slate-300 text-emerald-700 focus:ring-emerald-300" required="">
                  I agree to be contacted.
                </label>
                <button type="submit" class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                  <i data-lucide="send" class="w-4 h-4"></i>
                  Send
                </button>
              </div>
              <p id="contactSuccess" class="mt-3 hidden text-[13px] text-emerald-700">Thanks! We’ll get back to you shortly.</p>
            </form>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="bg-emerald-950 text-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-10">
        <div class="grid md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center gap-3">
              <div class="h-9 w-9 rounded-lg bg-white/10 grid place-items-center text-amber-300 font-semibold" style="font-family: 'Cormorant Garamond', serif;">MJ</div>
              <div class="leading-tight">
                <div class="text-lg font-semibold">Meadow De Jalsa</div>
                <div class="text-[12px] text-emerald-200/80">Luxury Stay &amp; Events</div>
              </div>
            </div>
            <p class="mt-3 text-sm text-emerald-100/80">Elegant stays, curated dining, and memorable events on the Bardoli–Vyara Highway.</p>
            <div class="mt-4 flex items-center gap-3">
              <a href="https://instagram.com" class="p-2 rounded-md bg-white/10 hover:bg-white/15"><i data-lucide="instagram" class="w-4 h-4"></i></a>
              <a href="https://facebook.com" class="p-2 rounded-md bg-white/10 hover:bg-white/15"><i data-lucide="facebook" class="w-4 h-4"></i></a>
            </div>
          </div>
          <div>
            <div class="text-sm font-semibold mb-3">Explore</div>
            <ul class="space-y-2 text-[14px] text-emerald-100/80">
              <li><a href="#home" class="hover:text-white">Home</a></li>
              <li><a href="#about" class="hover:text-white">About</a></li>
              <li><a href="#rooms" class="hover:text-white">Rooms</a></li>
              <li><a href="#restaurant" class="hover:text-white">Restaurant</a></li>
              <li><a href="#events" class="hover:text-white">Events</a></li>
              <li><a href="#gallery" class="hover:text-white">Gallery</a></li>
              <li><a href="#contact" class="hover:text-white">Contact</a></li>
            </ul>
          </div>
          <div>
            <div class="text-sm font-semibold mb-3">Policies</div>
            <ul class="space-y-2 text-[14px] text-emerald-100/80">
              <li>Check-in: 2 PM • Check-out: 11 AM</li>
              <li>Non-smoking rooms</li>
              <li>Pets not allowed</li>
              <li>Free cancellation up to 48 hrs</li>
            </ul>
          </div>
          <div>
            <div class="text-sm font-semibold mb-3">Newsletter</div>
            <form id="newsletterForm" class="flex gap-2">
              <input type="email" required="" placeholder="Your email" class="w-full rounded-lg bg-white/10 placeholder:text-emerald-100/60 text-white px-3 py-2 ring-1 ring-white/10 focus:outline-none focus:ring-white/30">
              <button class="rounded-lg px-3 py-2 bg-amber-400 text-emerald-950 text-sm font-medium hover:bg-amber-300">Join</button>
            </form>
            <p id="newsletterSuccess" class="mt-2 hidden text-[13px] text-emerald-200">Subscribed! Welcome aboard.</p>
          </div>
        </div>
        <div class="mt-8 pt-6 border-t border-white/10 text-[12px] text-emerald-100/70 flex items-center justify-between">
          <span>© <span id="year"></span> Meadow De Jalsa. All rights reserved.</span>
          <a href="#home" class="inline-flex items-center gap-2 hover:text-white"><i data-lucide="arrow-up" class="w-4 h-4"></i> Back to top</a>
        </div>
      </div>
    </footer>

    <!-- Floating WhatsApp -->
    <a href="https://wa.me/************?text=Hello%20Meadow%20De%20Jalsa%2C%20I'd%20like%20to%20enquire." target="_blank" rel="noopener" class="fixed bottom-5 right-5 z-40 inline-flex items-center gap-2 rounded-full px-4 py-2 bg-emerald-500 hover:bg-emerald-600 text-white shadow-lg">
      <i data-lucide="message-circle" class="w-5 h-5"></i>
      Chat
   a&gt;

    <!-- Booking Modal -->
    <div id="bookingModal" class="hidden fixed inset-0 z-50">
      <div class="absolute inset-0 bg-slate-900/60 backdrop-blur-sm"></div>
      <div class="relative mx-auto max-w-lg mt-16 sm:mt-24 bg-white rounded-2xl ring-1 ring-slate-200 overflow-hidden">
        <div class="p-5 border-b border-slate-200 flex items-center justify-between">
          <div class="flex items-center gap-2">
            <i data-lucide="calendar-range" class="w-5 h-5 text-emerald-900"></i>
            <h3 class="text-[18px] font-semibold text-emerald-950">Book Your Stay</h3>
          </div>
          <button class="p-2 rounded-md hover:bg-slate-100" data-close="bookingModal" aria-label="Close booking modal">
            <i data-lucide="x" class="w-4 h-4"></i>
          </button>
        </div>
        <form id="bookingForm" class="p-5 grid sm:grid-cols-2 gap-3">
          <div class="sm:col-span-2">
            <label class="text-[12px] text-slate-500">Room Type</label>
            <select id="bookingRoomType" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]">
              <option>Standard Room</option>
              <option>Deluxe Room</option>
              <option>Luxury Suite</option>
            </select>
          </div>
          <div>
            <label class="text-[12px] text-slate-500">Check-in</label>
            <input type="date" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]">
          </div>
          <div>
            <label class="text-[12px] text-slate-500">Check-out</label>
            <input type="date" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]">
          </div>
          <div>
            <label class="text-[12px] text-slate-500">Guests</label>
            <input type="number" min="1" value="2" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]">
          </div>
          <div>
            <label class="text-[12px] text-slate-500">Name</label>
            <input type="text" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Full name">
          </div>
          <div class="sm:col-span-2">
            <label class="text-[12px] text-slate-500">Phone</label>
            <input type="tel" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="+91">
          </div>
          <div class="sm:col-span-2 flex items-center justify-between">
            <label class="inline-flex items-center gap-2 text-[12px] text-slate-600">
              <input type="checkbox" class="rounded border-slate-300 text-emerald-700 focus:ring-emerald-300" required="">
              I agree to the booking terms.
            </label>
            <button class="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
              <i data-lucide="check-circle-2" class="w-4 h-4"></i>
              Confirm
            </button>
          </div>
          <p id="bookingSuccess" class="sm:col-span-2 hidden text-[13px] text-emerald-700">Request received! Our team will contact you to confirm.</p>
        </form>
      </div>
    </div>

    <!-- Enquiry Modal -->
    <div id="enquiryModal" class="hidden fixed inset-0 z-50">
      <div class="absolute inset-0 bg-slate-900/60 backdrop-blur-sm"></div>
      <div class="relative mx-auto max-w-lg mt-16 sm:mt-24 bg-white rounded-2xl ring-1 ring-slate-200 overflow-hidden">
        <div class="p-5 border-b border-slate-200 flex items-center justify-between">
          <div class="flex items-center gap-2">
            <i data-lucide="messages-square" class="w-5 h-5 text-emerald-900"></i>
            <h3 class="text-[18px] font-semibold text-emerald-950">Quick Enquiry</h3>
          </div>
          <button class="p-2 rounded-md hover:bg-slate-100" data-close="enquiryModal" aria-label="Close enquiry modal">
            <i data-lucide="x" class="w-4 h-4"></i>
          </button>
        </div>
        <form id="enquiryForm" class="p-5 grid sm:grid-cols-2 gap-3">
          <div class="sm:col-span-2">
            <label class="text-[12px] text-slate-500">Enquiry Type</label>
            <input id="enquiryType" type="text" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" value="General">
          </div>
          <div>
            <label class="text-[12px] text-slate-500">Name</label>
            <input type="text" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]">
          </div>
          <div>
            <label class="text-[12px] text-slate-500">Phone</label>
            <input type="tel" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]">
          </div>
          <div class="sm:col-span-2">
            <label class="text-[12px] text-slate-500">Message</label>
            <textarea rows="4" required="" class="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Tell us more..."></textarea>
          </div>
          <div class="sm:col-span-2 flex items-center justify-between">
            <label class="inline-flex items-center gap-2 text-[12px] text-slate-600">
              <input type="checkbox" class="rounded border-slate-300 text-emerald-700 focus:ring-emerald-300              required /&gt;
              I agree to be contacted.
            &lt;/label&gt;
            &lt;button type=" submit"="">
              <i data-lucide="send" class="w-4 h-4"></i>
              Send
            
          </label></div>
          <p id="enquirySuccess" class="sm:col-span-2 hidden text-[13px] text-emerald-700">Thanks! We’ll reach out shortly.</p>
        </form>
      </div>
    </div>

    <!-- Lightbox -->
    <div id="lightbox" class="hidden fixed inset-0 z-50">
      <div class="absolute inset-0 bg-slate-900/70 backdrop-blur-sm"></div>
      <div class="relative mx-auto max-w-4xl mt-16 sm:mt-20 px-4">
        <div class="relative bg-white rounded-2xl ring-1 ring-slate-200 overflow-hidden">
          <button class="absolute top-2 right-2 z-10 p-2 rounded-md bg-white/90 hover:bg-white text-slate-700 ring-1 ring-slate-200" id="lightboxClose" aria-label="Close image">
            <i data-lucide="x" class="w-4 h-4"></i>
          </button>
          <img id="lightboxImg" src="" alt="Gallery preview" class="w-full max-h-[80vh] object-contain bg-black/5">
        </div>
      </div>
    </div>

    <!-- Scripts -->
    <script>
      // Initialize icons
      document.addEventListener('DOMContentLoaded', () => {
        if (window.lucide) window.lucide.createIcons();
      });

      // Year
      document.getElementById('year').textContent = new Date().getFullYear();

      // Mobile nav toggle
      const navToggle = document.getElementById('navToggle');
      const mobileMenu = document.getElementById('mobileMenu');
      if (navToggle && mobileMenu) {
        navToggle.addEventListener('click', () => {
          mobileMenu.classList.toggle('hidden');
        });
        mobileMenu.querySelectorAll('a').forEach(a => a.addEventListener('click', () => mobileMenu.classList.add('hidden')));
      }

      // Events carousel
      (function () {
        const track = document.getElementById('eventsTrack');
        const prev = document.getElementById('eventsPrev');
        const next = document.getElementById('eventsNext');
        if (!track) return;
        const scrollBy = () => {
          const card = track.querySelector(':scope > *');
          if (!card) return 320;
          const r = card.getBoundingClientRect();
          return r.width + 16; // gap
        };
        prev?.addEventListener('click', () => track.scrollBy({ left: -scrollBy(), behavior: 'smooth' }));
        next?.addEventListener('click', () => track.scrollBy({ left: scrollBy(), behavior: 'smooth' }));
      })();

      // Fix typo in room slider dot attribute (data-to1 -> data-to)
      document.querySelectorAll('[data-to1]').forEach(el => {
        el.setAttribute('data-to', el.getAttribute('data-to1'));
        el.removeAttribute('data-to1');
      });

      // Room sliders
      (function () {
        const updateSlider = (wrap) => {
          const index = parseInt(wrap.dataset.index || '0', 10);
          const imgs = wrap.querySelectorAll('img');
          const width = wrap.parentElement.clientWidth;
          wrap.style.transform = `translateX(-${index * width}px)`;
          const sliderId = wrap.dataset.slider;
          document.querySelectorAll(`[data-dot="${sliderId}"]`).forEach((dot, i) => {
            dot.setAttribute('data-active', i === index);
            dot.classList.toggle('w-4', i === index);
            dot.classList.toggle('w-1.5', i !== index);
          });
        };

        // Handle next/prev
        document.querySelectorAll('[data-next],[data-prev]').forEach(btn => {
          btn.addEventListener('click', () => {
            const id = btn.dataset.next || btn.dataset.prev;
            const wrap = document.querySelector(`[data-slider="${id}"]`);
            if (!wrap) return;
            const total = wrap.querySelectorAll('img').length;
            let idx = parseInt(wrap.dataset.index || '0', 10);
            if (btn.dataset.next) idx = (idx + 1) % total;
            if (btn.dataset.prev) idx = (idx - 1 + total) % total;
            wrap.dataset.index = idx;
            updateSlider(wrap);
          });
        });

        // Dots
        document.querySelectorAll('[data-dot]').forEach(dot => {
          dot.addEventListener('click', () => {
            const id = dot.dataset.dot;
            const to = parseInt(dot.dataset.to ?? '0', 10);
            const wrap = document.querySelector(`[data-slider="${id}"]`);
            if (!wrap) return;
            wrap.dataset.index = to;
            updateSlider(wrap);
          });
        });

        // Resize recalculation
        window.addEventListener('resize', () => {
          document.querySelectorAll('[data-slider]').forEach(updateSlider);
        });

        // Initial
        document.querySelectorAll('[data-slider]').forEach(updateSlider);
      })();

      // Room filters
      (function () {
        const buttons = document.querySelectorAll('.room-filter');
        const cards = document.querySelectorAll('.room-card');
        buttons.forEach(btn => btn.addEventListener('click', () => {
          const f = btn.dataset.filter;
          buttons.forEach(b => b.classList.remove('bg-emerald-900','text-white'));
          btn.classList.add('bg-emerald-900','text-white');
          cards.forEach(card => {
            const show = f === 'all' || card.dataset.category === f;
            card.classList.toggle('hidden', !show);
          });
        }));
      })();

      // Gallery filters
      (function () {
        const btns = document.querySelectorAll('.gallery-filter');
        const items = document.querySelectorAll('#galleryGrid [data-group]');
        btns.forEach(btn => btn.addEventListener('click', () => {
          const g = btn.dataset.group;
          btns.forEach(b => b.classList.remove('bg-emerald-900','text-white'));
          btn.classList.add('bg-emerald-900','text-white');
          items.forEach(it => {
            const show = g === 'all' || it.dataset.group === g;
            it.classList.toggle('hidden', !show);
          });
        }));
      })();

      // Lightbox
      (function () {
        const lb = document.getElementById('lightbox');
        const img = document.getElementById('lightboxImg');
        const closeBtn = document.getElementById('lightboxClose');
        document.querySelectorAll('[data-lightbox]').forEach(a => {
          a.addEventListener('click', (e) => {
            e.preventDefault();
            const href = a.getAttribute('href');
            img.src = href;
            lb.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
          });
        });
        const close = () => {
          lb.classList.add('hidden');
          img.src = '';
          document.body.classList.remove('overflow-hidden');
        };
        closeBtn?.addEventListener('click', close);
        lb.addEventListener('click', (e) => {
          if (e.target === lb || e.target.classList.contains('backdrop-blur-sm')) close();
        });
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape' && !lb.classList.contains('hidden')) close();
        });
      })();

      // Modals open/close
      (function () {
        const openers = document.querySelectorAll('[data-open]');
        const closers = document.querySelectorAll('[data-close]');
        const toggle = (id, show) => {
          const modal = document.getElementById(id);
          if (!modal) return;
          modal.classList.toggle('hidden', !show);
          document.body.classList.toggle('overflow-hidden', show);
        };
        openers.forEach(btn => {
          btn.addEventListener('click', () => {
            const id = btn.dataset.open;
            // Pre-fill booking room type if provided
            if (id === 'bookingModal' && btn.dataset.room) {
              const sel = document.getElementById('bookingRoomType');
              if (sel) sel.value = btn.dataset.room;
            }
            // Pre-fill enquiry type if provided
            if (id === 'enquiryModal' && btn.dataset.type) {
              const input = document.getElementById('enquiryType');
              if (input) input.value = btn.dataset.type;
            }
            toggle(id, true);
          });
        });
        closers.forEach(btn => {
          btn.addEventListener('click', () => toggle(btn.dataset.close, false));
        });
        // Close on overlay click
        ['bookingModal','enquiryModal'].forEach(id => {
          const modal = document.getElementById(id);
          modal?.addEventListener('click', (e) => {
            if (e.target === modal || e.target.classList.contains('backdrop-blur-sm')) {
              modal.classList.add('hidden');
              document.body.classList.remove('overflow-hidden');
            }
          });
        });
        // ESC close
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            ['bookingModal','enquiryModal','lightbox'].forEach(id => {
              const m = document.getElementById(id);
              if (m && !m.classList.contains('hidden')) m.classList.add('hidden');
            });
            document.body.classList.remove('overflow-hidden');
          }
        });

        // "Book Now" shortcuts
        ['bookNowTop','bookNowTopMobile','bookNowHero','bookNowCTA'].forEach(id => {
          const el = document.getElementById(id);
          el?.addEventListener('click', () => toggle('bookingModal', true));
        });
      })();

      // Forms
      (function () {
        const showMsg = (el) => {
          el.classList.remove('hidden');
          setTimeout(() => el.classList.add('hidden'), 4000);
        };

        const bookingForm = document.getElementById('bookingForm');
        const bookingSuccess = document.getElementById('bookingSuccess');
        bookingForm?.addEventListener('submit', (e) => {
          e.preventDefault();
          bookingForm.reset();
          showMsg(bookingSuccess);
        });

        const enquiryForm = document.getElementById('enquiryForm');
        const enquirySuccess = document.getElementById('enquirySuccess');
        enquiryForm?.addEventListener('submit', (e) => {
          e.preventDefault();
          enquiryForm.reset();
          showMsg(enquirySuccess);
        });

        const contactForm = document.getElementById('contactForm');
        const contactSuccess = document.getElementById('contactSuccess');
        contactForm?.addEventListener('submit', (e) => {
          e.preventDefault();
          contactForm.reset();
          showMsg(contactSuccess);
        });

        const newsletterForm = document.getElementById('newsletterForm');
        const newsletterSuccess = document.getElementById('newsletterSuccess');
        newsletterForm?.addEventListener('submit', (e) => {
          e.preventDefault();
          newsletterForm.reset();
          showMsg(newsletterSuccess);
        });
      })();

      // Repair incomplete WhatsApp anchor if needed (ensures proper structure)
      (function () {
        const wa = document.querySelector('a[href^="https://wa.me/"][class*="fixed"]');
        if (!wa) return;
        // If the anchor accidentally wraps extra nodes, rebuild it cleanly
        const children = Array.from(wa.childNodes);
        if (children.length > 2) {
          const parent = wa.parentNode;
          // New proper anchor
          const newWa = wa.cloneNode(false);
          const icon = document.createElement('i');
          icon.setAttribute('data-lucide', 'message-circle');
          icon.className = 'w-5 h-5';
          newWa.appendChild(icon);
          newWa.appendChild(document.createTextNode(' Chat'));
          parent.insertBefore(newWa, wa);

          // Move only element nodes (skip stray text like "a>") after new anchor
          const frag = document.createDocumentFragment();
          children.slice(2).forEach(n => {
            if (n.nodeType === 1) frag.appendChild(n);
          });
          parent.insertBefore(frag, wa.nextSibling);
          parent.removeChild(wa);
          if (window.lucide) window.lucide.createIcons();
        }
      })();

      // Smooth hash navigation offset (account for fixed header)
      (function () {
        const OFFSET = 120;
        document.querySelectorAll('a[href^="#"]').forEach(a => {
          a.addEventListener('click', (e) => {
            const id = a.getAttribute('href');
            if (!id || id === '#' || id.length < 2) return;
            const target = document.querySelector(id);
            if (!target) return;
            e.preventDefault();
            const top = target.getBoundingClientRect().top + window.pageYOffset - OFFSET;
            window.scrollTo({ top, behavior: 'smooth' });
          });
        });
      })();
    </script>
  
</a></body></html>