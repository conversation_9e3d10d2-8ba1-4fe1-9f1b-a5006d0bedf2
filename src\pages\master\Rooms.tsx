import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash2, Search, Eye, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import RoomsForm from "@/components/master-tables/RoomsForm";
import { motion } from "framer-motion";

interface Room {
  id: string;
  property_id: string;
  room_type_id: string;
  room_number: string;
  floor?: number;
  wing?: string;
  view_type?: string;
  status: string;
  key_code?: string;
  tax_percentages?: string;
  is_active: boolean;
  properties_admin?: {
    name: string;
  };
  room_types?: {
    name: string;
    code: string;
  };
}

const Rooms = () => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showForm, setShowForm] = useState(false);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [viewingRoom, setViewingRoom] = useState<Room | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchRooms();
  }, []);

  const fetchRooms = async () => {
    try {
      const { data, error } = await supabase
        .from("rooms")
        .select(
          `
          *,
          properties_admin (
            name
          ),
          room_types (
            name,
            code
          )
        `
        )
        .eq("is_deleted", false)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setRooms(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch rooms",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this room?")) return;

    try {
      const { error } = await supabase
        .from("rooms")
        .update({ is_deleted: true })
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Room deleted successfully",
      });
      fetchRooms();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete room",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "bg-success text-success-foreground";
      case "OCCUPIED":
        return "bg-destructive text-destructive-foreground";
      case "MAINTENANCE":
        return "bg-warning text-warning-foreground";
      case "OUT_OF_ORDER":
        return "bg-destructive text-destructive-foreground";
      case "CLEANING":
        return "bg-primary text-primary-foreground";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "✓";
      case "OCCUPIED":
        return "🚪";
      case "MAINTENANCE":
        return "🔧";
      case "OUT_OF_ORDER":
        return "⚠";
      case "CLEANING":
        return "🧹";
      default:
        return "?";
    }
  };

  const filteredRooms = rooms.filter((room) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      room.room_number.toLowerCase().includes(searchLower) ||
      room.properties_admin?.name?.toLowerCase().includes(searchLower) ||
      room.room_types?.name?.toLowerCase().includes(searchLower) ||
      room.room_types?.code?.toLowerCase().includes(searchLower) ||
      room.wing?.toLowerCase().includes(searchLower) ||
      room.view_type?.toLowerCase().includes(searchLower) ||
      room.status.toLowerCase().includes(searchLower)
    );
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-80" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-64" />
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
          Rooms Master
        </h1>
        <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Room
        </Button>
      </motion.div>

      <motion.div
        className="flex items-center space-x-4"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search rooms..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </motion.div>

      <motion.div
        className="grid gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {filteredRooms.map((room, index) => (
          <motion.div
            key={room.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            whileHover={{ scale: 1.01, y: -2 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant hover:shadow-card transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                  <div className="flex-1">
                    <CardTitle className="flex flex-wrap items-center gap-2 text-lg">
                      Room {room.room_number}
                      <Badge
                        variant={room.is_active ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {room.is_active ? "Active" : "Inactive"}
                      </Badge>
                      <Badge
                        className={`text-xs ${getStatusColor(room.status)}`}
                      >
                        {getStatusIcon(room.status)} {room.status}
                      </Badge>
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Property: {room.properties_admin?.name} | Type:{" "}
                      {room.room_types?.name} ({room.room_types?.code})
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewingRoom(room)}
                      className="hover:bg-primary/10 hover:border-primary/20"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingRoom(room);
                        setShowForm(true);
                      }}
                      className="hover:bg-warning/10 hover:border-warning/20"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(room.id)}
                      className="hover:bg-destructive/10 hover:border-destructive/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                  {room.floor !== null && room.floor !== undefined && (
                    <div className="flex flex-col">
                      <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                        Floor
                      </span>
                      <span className="text-foreground">{room.floor}</span>
                    </div>
                  )}
                  {room.wing && (
                    <div className="flex flex-col">
                      <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                        Wing
                      </span>
                      <span className="text-foreground">{room.wing}</span>
                    </div>
                  )}
                  {room.view_type && (
                    <div className="flex flex-col">
                      <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                        View
                      </span>
                      <span className="text-foreground">{room.view_type}</span>
                    </div>
                  )}
                  {room.key_code && (
                    <div className="flex flex-col">
                      <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                        Key Code
                      </span>
                      <span className="text-foreground font-mono">
                        {room.key_code}
                      </span>
                    </div>
                  )}
                  {room.tax_percentages && (
                    <div className="flex flex-col">
                      <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                        Tax %
                      </span>
                      <span className="text-foreground">
                        {room.tax_percentages}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {showForm && (
        <RoomsForm
          room={editingRoom}
          onClose={() => {
            setShowForm(false);
            setEditingRoom(null);
          }}
          onSuccess={() => {
            fetchRooms();
            setShowForm(false);
            setEditingRoom(null);
          }}
        />
      )}

      {viewingRoom && (
        <Dialog open={!!viewingRoom} onOpenChange={() => setViewingRoom(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Room Details</DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Room Number
                  </label>
                  <p className="text-base font-medium">
                    {viewingRoom.room_number}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Room Status
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        viewingRoom.status === "AVAILABLE"
                          ? "bg-green-500"
                          : viewingRoom.status === "OCCUPIED"
                          ? "bg-red-500"
                          : viewingRoom.status === "MAINTENANCE"
                          ? "bg-yellow-500"
                          : viewingRoom.status === "OUT_OF_ORDER"
                          ? "bg-red-600"
                          : viewingRoom.status === "CLEANING"
                          ? "bg-blue-500"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <Badge
                      variant={getStatusColor(viewingRoom.status)}
                      className="font-medium"
                    >
                      {viewingRoom.status === "AVAILABLE"
                        ? "✓ Available"
                        : viewingRoom.status === "OCCUPIED"
                        ? "🚪 Occupied"
                        : viewingRoom.status === "MAINTENANCE"
                        ? "🔧 Maintenance"
                        : viewingRoom.status === "OUT_OF_ORDER"
                        ? "⚠ Out of Order"
                        : viewingRoom.status === "CLEANING"
                        ? "🧹 Cleaning"
                        : viewingRoom.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Property
                  </label>
                  <p className="text-base">
                    {viewingRoom.properties_admin?.name || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Room Type
                  </label>
                  <p className="text-base">
                    {viewingRoom.room_types?.name} (
                    {viewingRoom.room_types?.code})
                  </p>
                </div>
                {viewingRoom.floor !== null &&
                  viewingRoom.floor !== undefined && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Floor
                      </label>
                      <p className="text-base">{viewingRoom.floor}</p>
                    </div>
                  )}
                {viewingRoom.wing && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Wing
                    </label>
                    <p className="text-base">{viewingRoom.wing}</p>
                  </div>
                )}
                {viewingRoom.view_type && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      View Type
                    </label>
                    <p className="text-base">{viewingRoom.view_type}</p>
                  </div>
                )}
                {viewingRoom.key_code && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Key Code
                    </label>
                    <p className="text-base">{viewingRoom.key_code}</p>
                  </div>
                )}
                {viewingRoom.tax_percentages && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Tax Percentages
                    </label>
                    <p className="text-base">{viewingRoom.tax_percentages}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Active Status
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        viewingRoom.is_active ? "bg-green-500" : "bg-gray-400"
                      }`}
                    ></div>
                    <Badge
                      variant={viewingRoom.is_active ? "default" : "secondary"}
                      className="font-medium"
                    >
                      {viewingRoom.is_active ? "✓ Active" : "✕ Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Rooms;
