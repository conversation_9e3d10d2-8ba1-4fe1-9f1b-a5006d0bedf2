import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { Shield, Lock, Eye, Users, FileText } from 'lucide-react';

interface CompanySetting {
  setting_key: string;
  setting_value: string;
}

const PrivacyPolicyTest: React.FC = () => {
  const [settings, setSettings] = useState<CompanySetting[]>([]);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data } = await supabase
        .from('company_settings')
        .select('setting_key, setting_value')
        .eq('is_active', true)
        .eq('is_deleted', false);

      setSettings(data || []);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const getSetting = (key: string, defaultValue: string = '') => {
    const setting = settings.find(s => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-emerald-700 pt-24 pb-16">
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="container mx-auto px-4 text-center"
        >
          <Shield className="w-16 h-16 text-white mx-auto mb-6" />
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">Privacy Policy</h1>
          <p className="text-xl text-emerald-100 max-w-2xl mx-auto">
            Your privacy and data protection are our top priorities
          </p>
        </motion.div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-emerald-50 rounded-2xl p-8 mb-12">
            <p className="text-lg text-emerald-900 text-center">
              At <span className="font-semibold">{getSetting('company_name', 'Your Hotel Name')}</span>, we respect your privacy and are committed to protecting your personal information.
            </p>
          </div>

          <div className="space-y-8">
            {[
              { icon: Eye, title: "Information We Collect", content: "We collect personal information, usage data, and marketing preferences." },
              { icon: Users, title: "How We Use Your Information", content: "We use your data to process bookings, provide customer service, and improve our services." },
              { icon: Lock, title: "Data Security", content: "We implement appropriate security measures to protect your data." },
              { icon: Shield, title: "Your Rights", content: "You have the right to access, update, or delete your personal information." }
            ].map((section, index) => {
              const IconComponent = section.icon;
              return (
                <div key={index} className="bg-white rounded-2xl border p-8 shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                      <IconComponent className="w-6 h-6 text-emerald-700" />
                    </div>
                    <h2 className="text-2xl font-bold text-emerald-900">{index + 1}. {section.title}</h2>
                  </div>
                  <p className="text-gray-700">{section.content}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Simple Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <p>© {new Date().getFullYear()} {getSetting('company_name', 'Your Hotel Name')}. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default PrivacyPolicyTest;