import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp,
  Download,
  Calendar,
  DollarSign,
  Users,
  Bed,
  Utensils,
  Star,
  Filter,
  FileSpreadsheet
} from 'lucide-react';

interface ReportData {
  period: string;
  occupancyRate: number;
  totalRevenue: number;
  averageDailyRate: number;
  totalBookings: number;
  guestSatisfaction: number;
  roomServiceOrders: number;
}

const mockReportData: ReportData[] = [
  {
    period: 'January 2024',
    occupancyRate: 85,
    totalRevenue: 284500,
    averageDailyRate: 185,
    totalBookings: 456,
    guestSatisfaction: 4.7,
    roomServiceOrders: 123
  },
  {
    period: 'December 2023',
    occupancyRate: 78,
    totalRevenue: 245800,
    averageDailyRate: 175,
    totalBookings: 398,
    guestSatisfaction: 4.5,
    roomServiceOrders: 98
  },
  {
    period: 'November 2023',
    occupancyRate: 72,
    totalRevenue: 198600,
    averageDailyRate: 165,
    totalBookings: 342,
    guestSatisfaction: 4.6,
    roomServiceOrders: 87
  }
];

const ReportMetric: React.FC<{
  title: string;
  value: string | number;
  icon: React.ElementType;
  trend?: number;
  format?: 'currency' | 'percentage' | 'number' | 'rating';
  color?: string;
}> = ({ title, value, icon: Icon, trend, format = 'number', color = 'text-primary' }) => {
  const formatValue = () => {
    switch (format) {
      case 'currency':
        return `$${Number(value).toLocaleString()}`;
      case 'percentage':
        return `${value}%`;
      case 'rating':
        return `${value}/5.0`;
      default:
        return value.toString();
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className={`text-2xl font-bold ${color}`}>{formatValue()}</p>
            {trend !== undefined && (
              <div className={`flex items-center text-sm ${trend >= 0 ? 'text-success' : 'text-destructive'}`}>
                <TrendingUp className={`mr-1 h-3 w-3 ${trend < 0 ? 'rotate-180' : ''}`} />
                {Math.abs(trend)}% vs last period
              </div>
            )}
          </div>
          <div className={`p-3 rounded-lg bg-primary/10`}>
            <Icon className={`h-6 w-6 ${color}`} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const PerformanceChart: React.FC = () => {
  const months = ['Nov', 'Dec', 'Jan'];
  const occupancyData = [72, 78, 85];
  const revenueData = [198.6, 245.8, 284.5]; // in thousands

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Performance Trends
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Occupancy Trend */}
          <div>
            <h4 className="text-sm font-medium mb-3">Occupancy Rate (%)</h4>
            <div className="flex items-end space-x-4 h-32">
              {occupancyData.map((value, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div 
                    className="bg-primary rounded-t w-full"
                    style={{ height: `${(value / 100) * 100}%` }}
                  ></div>
                  <div className="text-xs mt-2">{months[index]}</div>
                  <div className="text-xs font-semibold">{value}%</div>
                </div>
              ))}
            </div>
          </div>

          {/* Revenue Trend */}
          <div>
            <h4 className="text-sm font-medium mb-3">Revenue ($K)</h4>
            <div className="flex items-end space-x-4 h-32">
              {revenueData.map((value, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div 
                    className="bg-gold rounded-t w-full"
                    style={{ height: `${(value / 300) * 100}%` }}
                  ></div>
                  <div className="text-xs mt-2">{months[index]}</div>
                  <div className="text-xs font-semibold">${value}K</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Reports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('January 2024');
  const [reportType, setReportType] = useState('overview');

  const currentData = mockReportData.find(data => data.period === selectedPeriod) || mockReportData[0];
  const previousData = mockReportData[1]; // For comparison

  const getTrend = (current: number, previous: number) => {
    return ((current - previous) / previous) * 100;
  };

  const reportTypes = [
    { value: 'overview', label: 'Overview Report' },
    { value: 'revenue', label: 'Revenue Report' },
    { value: 'occupancy', label: 'Occupancy Report' },
    { value: 'guest-satisfaction', label: 'Guest Satisfaction Report' },
    { value: 'operations', label: 'Operations Report' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground">Comprehensive insights into your hotel performance</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline">
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger className="w-full md:w-64">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-full md:w-48">
                <Calendar className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                {mockReportData.map((data) => (
                  <SelectItem key={data.period} value={data.period}>
                    {data.period}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <ReportMetric
          title="Occupancy Rate"
          value={currentData.occupancyRate}
          icon={Bed}
          trend={getTrend(currentData.occupancyRate, previousData.occupancyRate)}
          format="percentage"
          color="text-primary"
        />
        <ReportMetric
          title="Total Revenue"
          value={currentData.totalRevenue}
          icon={DollarSign}
          trend={getTrend(currentData.totalRevenue, previousData.totalRevenue)}
          format="currency"
          color="text-success"
        />
        <ReportMetric
          title="Average Daily Rate"
          value={currentData.averageDailyRate}
          icon={TrendingUp}
          trend={getTrend(currentData.averageDailyRate, previousData.averageDailyRate)}
          format="currency"
          color="text-gold"
        />
        <ReportMetric
          title="Guest Satisfaction"
          value={currentData.guestSatisfaction}
          icon={Star}
          trend={getTrend(currentData.guestSatisfaction, previousData.guestSatisfaction)}
          format="rating"
          color="text-warning"
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <ReportMetric
          title="Total Bookings"
          value={currentData.totalBookings}
          icon={Users}
          trend={getTrend(currentData.totalBookings, previousData.totalBookings)}
          color="text-primary"
        />
        <ReportMetric
          title="Room Service Orders"
          value={currentData.roomServiceOrders}
          icon={Utensils}
          trend={getTrend(currentData.roomServiceOrders, previousData.roomServiceOrders)}
          color="text-blue-600"
        />
        <ReportMetric
          title="Revenue Per Available Room"
          value={Math.round(currentData.totalRevenue / 120)} // Assuming 120 rooms
          icon={Bed}
          trend={5.2} // Mock trend
          format="currency"
          color="text-purple-600"
        />
      </div>

      {/* Charts and Detailed Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <PerformanceChart />

        {/* Top Performing Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-primary rounded"></div>
                  <span className="text-sm">Room Revenue</span>
                </div>
                <div className="text-sm font-semibold">$245,600 (86%)</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-gold rounded"></div>
                  <span className="text-sm">Food & Beverage</span>
                </div>
                <div className="text-sm font-semibold">$28,450 (10%)</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span className="text-sm">Other Services</span>
                </div>
                <div className="text-sm font-semibold">$10,450 (4%)</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Performance Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4 font-semibold">Period</th>
                  <th className="text-left py-2 px-4 font-semibold">Occupancy</th>
                  <th className="text-left py-2 px-4 font-semibold">Revenue</th>
                  <th className="text-left py-2 px-4 font-semibold">ADR</th>
                  <th className="text-left py-2 px-4 font-semibold">Bookings</th>
                  <th className="text-left py-2 px-4 font-semibold">Satisfaction</th>
                </tr>
              </thead>
              <tbody>
                {mockReportData.map((data, index) => (
                  <tr key={index} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-4">{data.period}</td>
                    <td className="py-3 px-4">{data.occupancyRate}%</td>
                    <td className="py-3 px-4">${data.totalRevenue.toLocaleString()}</td>
                    <td className="py-3 px-4">${data.averageDailyRate}</td>
                    <td className="py-3 px-4">{data.totalBookings}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-500 mr-1" />
                        {data.guestSatisfaction}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Insights and Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Key Insights & Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-semibold text-success">Positive Trends</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-success rounded-full mt-1.5"></div>
                  <span>Occupancy rate increased by 7% month-over-month</span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-success rounded-full mt-1.5"></div>
                  <span>Guest satisfaction scores are above industry average</span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-success rounded-full mt-1.5"></div>
                  <span>Room service revenue grew by 25%</span>
                </li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold text-warning">Areas for Improvement</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-warning rounded-full mt-1.5"></div>
                  <span>Average daily rate could be optimized during peak periods</span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-warning rounded-full mt-1.5"></div>
                  <span>Weekend occupancy is lower than weekdays</span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-warning rounded-full mt-1.5"></div>
                  <span>Food & beverage revenue has potential for growth</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Reports;