import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Star,
  Bed,
  CreditCard,
  Settings,
  Bell,
  Shield,
  Save,
  Edit,
  Gift,
  Heart
} from 'lucide-react';

interface GuestProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  dateOfBirth: string;
  nationality: string;
  passportNumber: string;
  vipStatus: 'regular' | 'silver' | 'gold' | 'platinum';
  loyaltyPoints: number;
  totalStays: number;
  totalSpent: number;
  memberSince: string;
  preferences: {
    roomType: string;
    floorLevel: string;
    bedType: string;
    smokingPreference: string;
    specialRequests: string[];
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    promotions: boolean;
    bookingReminders: boolean;
  };
}

const mockProfile: GuestProfile = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  address: '123 Main Street, New York, NY 10001',
  dateOfBirth: '1985-06-15',
  nationality: 'United States',
  passportNumber: 'US123456789',
  vipStatus: 'gold',
  loyaltyPoints: 2450,
  totalStays: 12,
  totalSpent: 4500,
  memberSince: '2020-03-15',
  preferences: {
    roomType: 'Deluxe',
    floorLevel: 'High floor (10+)',
    bedType: 'King bed',
    smokingPreference: 'Non-smoking',
    specialRequests: ['Late checkout', 'Extra towels', 'Quiet room']
  },
  emergencyContact: {
    name: 'Jane Smith',
    relationship: 'Spouse',
    phone: '+****************'
  },
  notifications: {
    email: true,
    sms: true,
    promotions: false,
    bookingReminders: true
  }
};

const vipColors = {
  regular: 'bg-muted text-muted-foreground',
  silver: 'bg-gray-100 text-gray-800 border border-gray-300',
  gold: 'bg-yellow-100 text-yellow-800 border border-yellow-300',
  platinum: 'bg-purple-100 text-purple-800 border border-purple-300'
};

const vipLabels = {
  regular: 'Regular',
  silver: 'Silver',
  gold: 'Gold',
  platinum: 'Platinum'
};

export const Profile: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [profile, setProfile] = useState(mockProfile);
  const [activeTab, setActiveTab] = useState<'profile' | 'preferences' | 'security' | 'notifications'>('profile');

  const handleSave = () => {
    setIsEditing(false);
    // In a real app, this would save to the backend
  };

  const updateProfile = (updates: Partial<GuestProfile>) => {
    setProfile(prev => ({ ...prev, ...updates }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
          <p className="text-muted-foreground">Manage your account information and preferences</p>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button className="btn-primary" onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </>
          ) : (
            <Button variant="outline" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      {/* Profile Summary */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="md:col-span-1">
          <CardContent className="p-6 text-center">
            <div className="p-4 bg-primary/10 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <User className="h-10 w-10 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">{profile.name}</h3>
            <p className="text-muted-foreground">{profile.email}</p>
            <Badge className={`mt-2 ${vipColors[profile.vipStatus]}`}>
              <Star className="h-3 w-3 mr-1" />
              {vipLabels[profile.vipStatus]} Member
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-gold">{profile.loyaltyPoints}</div>
            <div className="text-sm text-muted-foreground">Loyalty Points</div>
            <Button size="sm" variant="outline" className="mt-2">
              <Gift className="h-4 w-4 mr-1" />
              Redeem
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-primary">{profile.totalStays}</div>
            <div className="text-sm text-muted-foreground">Total Stays</div>
            <div className="text-xs text-muted-foreground mt-1">
              Member since {new Date(profile.memberSince).getFullYear()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-success">${profile.totalSpent}</div>
            <div className="text-sm text-muted-foreground">Total Spent</div>
            <div className="text-xs text-muted-foreground mt-1">
              Lifetime value
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b">
        {[
          { key: 'profile', label: 'Personal Info', icon: User },
          { key: 'preferences', label: 'Preferences', icon: Heart },
          { key: 'security', label: 'Security', icon: Shield },
          { key: 'notifications', label: 'Notifications', icon: Bell }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as any)}
            className={`pb-2 px-1 border-b-2 transition-colors flex items-center space-x-2 ${
              activeTab === key 
                ? 'border-primary text-primary' 
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            <Icon className="h-4 w-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'profile' && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={profile.name}
                    onChange={(e) => updateProfile({ name: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
                
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => updateProfile({ email: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={profile.phone}
                    onChange={(e) => updateProfile({ phone: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <Label htmlFor="dob">Date of Birth</Label>
                  <Input
                    id="dob"
                    type="date"
                    value={profile.dateOfBirth}
                    onChange={(e) => updateProfile({ dateOfBirth: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <Label htmlFor="nationality">Nationality</Label>
                  <Input
                    id="nationality"
                    value={profile.nationality}
                    onChange={(e) => updateProfile({ nationality: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <Label htmlFor="passport">Passport Number</Label>
                  <Input
                    id="passport"
                    value={profile.passportNumber}
                    onChange={(e) => updateProfile({ passportNumber: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Address & Emergency Contact</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="address">Home Address</Label>
                <Textarea
                  id="address"
                  value={profile.address}
                  onChange={(e) => updateProfile({ address: e.target.value })}
                  disabled={!isEditing}
                  rows={3}
                />
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Emergency Contact</h4>
                <div>
                  <Label htmlFor="emergencyName">Name</Label>
                  <Input
                    id="emergencyName"
                    value={profile.emergencyContact.name}
                    onChange={(e) => updateProfile({
                      emergencyContact: { ...profile.emergencyContact, name: e.target.value }
                    })}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="emergencyRelationship">Relationship</Label>
                  <Input
                    id="emergencyRelationship"
                    value={profile.emergencyContact.relationship}
                    onChange={(e) => updateProfile({
                      emergencyContact: { ...profile.emergencyContact, relationship: e.target.value }
                    })}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="emergencyPhone">Phone Number</Label>
                  <Input
                    id="emergencyPhone"
                    value={profile.emergencyContact.phone}
                    onChange={(e) => updateProfile({
                      emergencyContact: { ...profile.emergencyContact, phone: e.target.value }
                    })}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'preferences' && (
        <Card>
          <CardHeader>
            <CardTitle>Stay Preferences</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="roomType">Preferred Room Type</Label>
                <Input
                  id="roomType"
                  value={profile.preferences.roomType}
                  onChange={(e) => updateProfile({
                    preferences: { ...profile.preferences, roomType: e.target.value }
                  })}
                  disabled={!isEditing}
                />
              </div>

              <div>
                <Label htmlFor="floorLevel">Floor Preference</Label>
                <Input
                  id="floorLevel"
                  value={profile.preferences.floorLevel}
                  onChange={(e) => updateProfile({
                    preferences: { ...profile.preferences, floorLevel: e.target.value }
                  })}
                  disabled={!isEditing}
                />
              </div>

              <div>
                <Label htmlFor="bedType">Bed Type</Label>
                <Input
                  id="bedType"
                  value={profile.preferences.bedType}
                  onChange={(e) => updateProfile({
                    preferences: { ...profile.preferences, bedType: e.target.value }
                  })}
                  disabled={!isEditing}
                />
              </div>

              <div>
                <Label htmlFor="smoking">Smoking Preference</Label>
                <Input
                  id="smoking"
                  value={profile.preferences.smokingPreference}
                  onChange={(e) => updateProfile({
                    preferences: { ...profile.preferences, smokingPreference: e.target.value }
                  })}
                  disabled={!isEditing}
                />
              </div>
            </div>

            <div>
              <Label>Special Requests</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {profile.preferences.specialRequests.map((request, index) => (
                  <Badge key={index} variant="outline">
                    {request}
                  </Badge>
                ))}
              </div>
              {isEditing && (
                <Button size="sm" variant="outline" className="mt-2">
                  Edit Requests
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'security' && (
        <Card>
          <CardHeader>
            <CardTitle>Security Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Password</h4>
                  <p className="text-sm text-muted-foreground">Last changed 3 months ago</p>
                </div>
                <Button variant="outline">Change Password</Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Two-Factor Authentication</h4>
                  <p className="text-sm text-muted-foreground">Add an extra layer of security</p>
                </div>
                <Button variant="outline">Enable 2FA</Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Login Activity</h4>
                  <p className="text-sm text-muted-foreground">View recent login sessions</p>
                </div>
                <Button variant="outline">View Activity</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'notifications' && (
        <Card>
          <CardHeader>
            <CardTitle>Notification Preferences</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              {[
                { key: 'email', label: 'Email Notifications', description: 'Receive booking confirmations and updates via email' },
                { key: 'sms', label: 'SMS Notifications', description: 'Get text messages for important updates' },
                { key: 'promotions', label: 'Promotional Offers', description: 'Receive special deals and offers' },
                { key: 'bookingReminders', label: 'Booking Reminders', description: 'Get reminded about upcoming stays' }
              ].map(({ key, label, description }) => (
                <div key={key} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{label}</h4>
                    <p className="text-sm text-muted-foreground">{description}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={profile.notifications[key as keyof typeof profile.notifications]}
                      onChange={(e) => updateProfile({
                        notifications: {
                          ...profile.notifications,
                          [key]: e.target.checked
                        }
                      })}
                      className="sr-only peer"
                      disabled={!isEditing}
                    />
                    <div className="w-11 h-6 bg-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                  </label>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Profile;