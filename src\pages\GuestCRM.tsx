import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Users, Target, Mail, Gift, Star, MessageCircle, Shield, BarChart3, Link } from 'lucide-react';

const GuestCRM = () => {
  const [selectedTab, setSelectedTab] = useState('contacts');

  const contacts = [
    { id: 'G001', name: '<PERSON>', email: '<EMAIL>', phone: '+1234567890', segment: 'VIP', lastStay: '2024-01-10', totalSpent: '$2,500', preferences: 'Ocean view, Late checkout' },
    { id: 'G002', name: '<PERSON>', email: '<EMAIL>', phone: '+1234567891', segment: 'Corporate', lastStay: '2024-01-08', totalSpent: '$1,800', preferences: 'Ground floor, Quiet room' },
    { id: 'G003', name: 'Mike Johnson', email: '<EMAIL>', phone: '+1234567892', segment: 'Leisure', lastStay: '2024-01-05', totalSpent: '$1,200', preferences: 'Pool access, Late breakfast' },
  ];

  const campaigns = [
    { id: 'C001', name: 'Summer Special 2024', type: 'Email', status: 'Active', sent: 2500, opened: 850, clicked: 127, conversions: 15 },
    { id: 'C002', name: 'Weekend Getaway', type: 'SMS', status: 'Scheduled', sent: 1200, opened: 960, clicked: 95, conversions: 8 },
    { id: 'C003', name: 'Corporate Package', type: 'WhatsApp', status: 'Completed', sent: 500, opened: 475, clicked: 48, conversions: 12 },
  ];

  const offers = [
    { id: 'O001', title: '20% Off Weekend Stay', code: 'WEEKEND20', type: 'Percentage', usage: '45/100', validity: '2024-02-28', status: 'Active' },
    { id: 'O002', title: 'Free Breakfast Upgrade', code: 'BREAKFAST', type: 'Service', usage: '12/50', validity: '2024-03-15', status: 'Active' },
    { id: 'O003', title: '$50 Off Spa Package', code: 'SPA50', type: 'Fixed', usage: '28/75', validity: '2024-02-20', status: 'Expiring' },
  ];

  const loyaltyTiers = [
    { tier: 'Bronze', members: 1250, benefits: 'Early check-in, 5% discount', pointsRequired: 0 },
    { tier: 'Silver', members: 320, benefits: 'Room upgrade, 10% discount, Free WiFi', pointsRequired: 1000 },
    { tier: 'Gold', members: 85, benefits: 'Suite upgrade, 15% discount, Spa access', pointsRequired: 2500 },
    { tier: 'Platinum', members: 25, benefits: 'Penthouse access, 20% discount, Personal concierge', pointsRequired: 5000 },
  ];

  const getSegmentColor = (segment: string) => {
    switch (segment) {
      case 'VIP': return 'default';
      case 'Corporate': return 'secondary';
      case 'Leisure': return 'outline';
      default: return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'default';
      case 'Scheduled': return 'secondary';
      case 'Completed': return 'outline';
      case 'Expiring': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Guest CRM & Loyalty</h1>
          <p className="text-muted-foreground">Manage guest relationships and loyalty programs</p>
        </div>
        <Button>
          <Users className="mr-2 h-4 w-4" />
          Add Guest
        </Button>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-9">
          <TabsTrigger value="contacts">Contacts</TabsTrigger>
          <TabsTrigger value="segmentation">Segments</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="offers">Offers</TabsTrigger>
          <TabsTrigger value="loyalty">Loyalty</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="consent">Consent</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="contacts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Guest Profiles
              </CardTitle>
              <CardDescription>Manage guest information and preferences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <Input placeholder="Search guests..." className="max-w-sm" />
                <Button variant="outline">Filter</Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Guest ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Segment</TableHead>
                    <TableHead>Last Stay</TableHead>
                    <TableHead>Total Spent</TableHead>
                    <TableHead>Preferences</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {contacts.map((contact) => (
                    <TableRow key={contact.id}>
                      <TableCell className="font-medium">{contact.id}</TableCell>
                      <TableCell>{contact.name}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{contact.email}</div>
                          <div className="text-muted-foreground">{contact.phone}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getSegmentColor(contact.segment)}>{contact.segment}</Badge>
                      </TableCell>
                      <TableCell>{contact.lastStay}</TableCell>
                      <TableCell>{contact.totalSpent}</TableCell>
                      <TableCell className="max-w-xs truncate">{contact.preferences}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="segmentation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Guest Segmentation
              </CardTitle>
              <CardDescription>Create and manage guest segments for targeted marketing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">High Spenders</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">156</div>
                    <p className="text-xs text-muted-foreground">Guests spending &gt;$2000</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Frequent Visitors</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">89</div>
                    <p className="text-xs text-muted-foreground">5+ visits this year</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Corporate Clients</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">234</div>
                    <p className="text-xs text-muted-foreground">Business travelers</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Marketing Campaigns
              </CardTitle>
              <CardDescription>Manage email, SMS, and WhatsApp marketing campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Campaign</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Sent</TableHead>
                    <TableHead>Opened</TableHead>
                    <TableHead>Clicked</TableHead>
                    <TableHead>Conversions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {campaigns.map((campaign) => (
                    <TableRow key={campaign.id}>
                      <TableCell className="font-medium">{campaign.name}</TableCell>
                      <TableCell>{campaign.type}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(campaign.status)}>{campaign.status}</Badge>
                      </TableCell>
                      <TableCell>{campaign.sent}</TableCell>
                      <TableCell>{campaign.opened}</TableCell>
                      <TableCell>{campaign.clicked}</TableCell>
                      <TableCell>{campaign.conversions}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="offers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5" />
                Offers & Vouchers
              </CardTitle>
              <CardDescription>Create and manage promotional offers and discount codes</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Offer</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Valid Until</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {offers.map((offer) => (
                    <TableRow key={offer.id}>
                      <TableCell className="font-medium">{offer.title}</TableCell>
                      <TableCell>
                        <code className="bg-muted px-2 py-1 rounded text-sm">{offer.code}</code>
                      </TableCell>
                      <TableCell>{offer.type}</TableCell>
                      <TableCell>{offer.usage}</TableCell>
                      <TableCell>{offer.validity}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(offer.status)}>{offer.status}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="loyalty" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Loyalty Program
              </CardTitle>
              <CardDescription>Manage loyalty tiers and reward points</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tier</TableHead>
                    <TableHead>Members</TableHead>
                    <TableHead>Benefits</TableHead>
                    <TableHead>Points Required</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loyaltyTiers.map((tier, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{tier.tier}</TableCell>
                      <TableCell>{tier.members}</TableCell>
                      <TableCell className="max-w-xs">{tier.benefits}</TableCell>
                      <TableCell>{tier.pointsRequired}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="feedback" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Feedback & NPS
              </CardTitle>
              <CardDescription>Guest feedback and Net Promoter Score tracking</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">NPS Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">68</div>
                    <p className="text-xs text-muted-foreground">Net Promoter Score</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Satisfaction</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">4.2/5</div>
                    <p className="text-xs text-muted-foreground">Average rating</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Responses</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">1,247</div>
                    <p className="text-xs text-muted-foreground">Total feedback</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Response Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">34%</div>
                    <p className="text-xs text-muted-foreground">Survey completion</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Consent & Privacy
              </CardTitle>
              <CardDescription>Track guest consent and privacy preferences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Guest consent tracking and privacy management</p>
                <Button className="mt-4">View Consent Log</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                CRM Analytics
              </CardTitle>
              <CardDescription>Guest lifetime value and retention analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Avg LTV</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">$2,340</div>
                    <p className="text-xs text-muted-foreground">Lifetime value</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Repeat Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">42%</div>
                    <p className="text-xs text-muted-foreground">Return guests</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Campaign ROI</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">320%</div>
                    <p className="text-xs text-muted-foreground">Marketing ROI</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Active Members</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">1,680</div>
                    <p className="text-xs text-muted-foreground">Loyalty members</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Integrations
              </CardTitle>
              <CardDescription>PMS and POS system integration logs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Link className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Integration logs with PMS, POS, and external systems</p>
                <Button className="mt-4">View Webhook Logs</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default GuestCRM;