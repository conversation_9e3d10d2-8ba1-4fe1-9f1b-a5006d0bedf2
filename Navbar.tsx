import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { WrapButton } from "@/components/ui/wrap-button";
import { Menu, X } from "lucide-react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Floating Modern Navbar */}
      <nav className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ${
        isScrolled 
          ? 'w-[95%] sm:w-[85%] lg:w-[70%] xl:w-[60%]' 
          : 'w-[95%] sm:w-[90%] lg:w-[80%] xl:w-[70%]'
      }`}>
        <div className={`relative bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl transition-all duration-500 ${
          isScrolled ? 'shadow-xl' : 'shadow-2xl'
        }`}>
          {/* Gradient Border Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#113F67]/20 via-[#40aca3]/20 to-[#113F67]/20 rounded-2xl blur-sm opacity-50"></div>
          
          <div className="relative px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <div className="flex items-center space-x-3 group">
                <div className="relative">
                  <img src="./l1.png" alt="" className="h-8 sm:h-10 transition-transform group-hover:scale-110" />
                  <div className="absolute inset-0 bg-gradient-to-r from-[#113F67]/20 to-[#40aca3]/20 rounded-lg blur-lg opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </div>
                <img src="./l2.png" alt="" className="h-6 sm:h-7 transition-transform group-hover:scale-110" />
              </div>

              {/* Desktop Navigation */}
              <div className="hidden lg:flex items-center space-x-1">
                {['Home', 'Features', 'Pricing', 'Testimonials', 'FAQ'].map((item, index) => (
                  <button
                    key={item}
                    onClick={() => scrollToSection(item.toLowerCase() === 'home' ? 'hero' : item.toLowerCase())}
                    className="relative px-4 py-2 text-sm font-medium text-gray-700 hover:text-[#113F67] transition-all duration-300 rounded-xl group overflow-hidden"
                  >
                    <span className="relative z-10">{item}</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#113F67]/10 to-[#40aca3]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                    <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#113F67] to-[#40aca3] group-hover:w-full transition-all duration-300"></div>
                  </button>
                ))}
              </div>

              {/* CTA Button */}
              <div className="hidden lg:block">
                <WrapButton onClick={() => scrollToSection('pricing')}>
                  Request Demo
                </WrapButton>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 group"
              >
                <div className="relative">
                  {isMenuOpen ? 
                    <X size={20} className="text-gray-600 group-hover:text-[#113F67] transition-colors" /> : 
                    <Menu size={20} className="text-gray-600 group-hover:text-[#113F67] transition-colors" />
                  }
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 mt-2 bg-white/90 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl overflow-hidden">
            <div className="p-6">
              <div className="flex flex-col space-y-3">
                {['Home', 'Features', 'Pricing', 'Testimonials', 'FAQ'].map((item) => (
                  <button
                    key={item}
                    onClick={() => scrollToSection(item.toLowerCase() === 'home' ? 'hero' : item.toLowerCase())}
                    className="text-left text-gray-700 hover:text-[#113F67] hover:bg-gradient-to-r hover:from-[#113F67]/5 hover:to-[#40aca3]/5 transition-all duration-300 py-3 px-4 rounded-xl font-medium group"
                  >
                    <span className="relative z-10">{item}</span>
                  </button>
                ))}
                <div className="pt-4 border-t border-gray-200/50">
                  <WrapButton onClick={() => scrollToSection('pricing')}>
                    Request Demo
                  </WrapButton>
                </div>
              </div>
            </div>
          </div>
        )}
      </nav>
    </>
  );
};

export default Navbar;