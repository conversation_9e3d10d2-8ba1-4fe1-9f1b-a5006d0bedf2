import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useFeatureFlags } from "@/hooks/useFeatureFlags";
import { FEATURE_FLAGS } from "@/utils/featureFlags";
import { useScrollToTop } from "@/hooks/useScrollToTop";
import ScrollToTop from "@/components/ui/ScrollToTop";
import { motion, AnimatePresence } from "framer-motion";

interface CompanySetting {
  setting_key: string;
  setting_value: string;
  setting_type: string;
  category: string;
}

interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
  display_order: number;
  is_active: boolean;
}

interface WebsiteLayoutProps {
  children: React.ReactNode;
}

export const WebsiteLayout: React.FC<WebsiteLayoutProps> = ({ children }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [settings, setSettings] = useState<CompanySetting[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const location = useLocation();
  const { isEnabled } = useFeatureFlags();
  useScrollToTop();

  useEffect(() => {
    fetchSettings();
    fetchServices();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from("company_settings")
        .select("setting_key, setting_value, setting_type, category")
        .eq("is_active", true)
        .eq("is_deleted", false);

      if (error) throw error;
      setSettings(data || []);
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };

  const fetchServices = async () => {
    try {
      const { data } = await supabase
        .from("services")
        .select("*")
        .eq("is_active", true)
        .order("display_order");

      setServices(data || []);
    } catch (error) {
      console.error("Error fetching services:", error);
    }
  };

  const getSetting = (key: string, defaultValue: string = "") => {
    const setting = settings.find((s) => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  const navigation = [
    { name: "Home", href: "/" },
    {
      name: "About Us",
      href: "/about",
      flag: FEATURE_FLAGS.PAGE_ABOUT_VISIBLE,
    },
    {
      name: "Services",
      href: "/services",
      flag: FEATURE_FLAGS.PAGE_SERVICES_VISIBLE,
    },
    {
      name: "Properties",
      href: "/properties",
      flag: FEATURE_FLAGS.PAGE_PROPERTIES_VISIBLE,
    },
    {
      name: "Events & Venues",
      href: "/events",
      flag: FEATURE_FLAGS.PAGE_EVENTS_VISIBLE,
    },
    { name: "Contact", href: "/contact" },
  ].filter((item) => !item.flag || isEnabled(item.flag));

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen bg-background">
      {/* Modern Floating Navbar */}
      <motion.nav
        initial={{ y: -100, opacity: 0, x: "-50%" }}
        animate={{ y: 0, opacity: 1, x: "-50%" }}
        transition={{ duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="fixed top-4 left-1/2 z-50 transition-all duration-500 w-[95%] sm:w-[85%] laptop-sm:w-[75%] laptop:w-[70%] xl:w-[60%]"
        style={{ transform: "translateX(-50%)" }}
      >
        <motion.div
          whileHover={{ scale: 1.01 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
          className="relative bg-[#1e2a23] backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl"
        >
          {/* Gradient Border Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#81928d]/20 via-[#81928d]/10 to-[#81928d]/20 rounded-2xl blur-sm opacity-50"></div>

          <div className="relative px-4 sm:px-6 py-3 sm:py-4">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <Link to="/" className="flex items-center space-x-3 group">
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                  className="relative w-full"
                >
                  <img
                    src="/logo2.png"
                    alt="Meadow De Jalsa"
                    className="w-full h-8 sm:h-12 rounded-full object-contain "
                  />
                </motion.div>
              </Link>

              {/* Desktop Navigation */}
              <div className="hidden laptop-sm:flex items-center space-x-1">
                {navigation.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ y: -10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                      delay: index * 0.05,
                      duration: 0.3,
                      ease: "easeOut",
                    }}
                  >
                    <Link
                      to={item.href}
                      className="relative px-3 laptop:px-4 py-2 text-xs laptop:text-sm font-medium text-white hover:text-[#81928d] transition-all duration-300 rounded-xl group overflow-hidden"
                    >
                      <span className="relative z-10">{item.name}</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-[#81928d]/10 to-[#81928d]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                      <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#81928d] to-[#81928d] group-hover:w-full transition-all duration-300"></div>
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* CTA Button */}
              <motion.div
                initial={{ x: 20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.3, ease: "easeOut" }}
                className="hidden laptop-sm:block"
              >
                <Link to="/admin">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button className="bg-[#81928d] hover:bg-[#6d7d78] text-white px-4 laptop:px-6 py-2 rounded-full text-xs laptop:text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300">
                      Admin Portal
                    </Button>
                  </motion.div>
                </Link>
              </motion.div>

              {/* Mobile Menu Button */}
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="laptop-sm:hidden p-2 rounded-xl hover:bg-white/10 transition-all duration-300 group"
              >
                <AnimatePresence mode="wait">
                  {isMobileMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ rotate: -90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: 90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <X
                        size={20}
                        className="text-white group-hover:text-[#81928d] transition-colors"
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ rotate: 90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: -90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Menu
                        size={20}
                        className="text-white group-hover:text-[#81928d] transition-colors"
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.98 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.98 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="laptop-sm:hidden absolute top-full left-0 right-0 mt-2 bg-white/90 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl overflow-hidden"
            >
              <div className="p-6">
                <div className="flex flex-col space-y-3">
                  {navigation.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ x: -10, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{
                        delay: index * 0.03,
                        duration: 0.2,
                        ease: "easeOut",
                      }}
                    >
                      <Link
                        to={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="text-left text-gray-700 hover:text-[#81928d] hover:bg-gradient-to-r hover:from-[#81928d]/5 hover:to-[#81928d]/5 transition-all duration-300 py-3 px-4 rounded-xl font-medium group block"
                      >
                        <span className="relative z-10">{item.name}</span>
                      </Link>
                    </motion.div>
                  ))}
                  <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{
                      delay: navigation.length * 0.03,
                      duration: 0.2,
                      ease: "easeOut",
                    }}
                    className="pt-4 border-t border-gray-200/50"
                  >
                    <Link to="/admin">
                      <Button className="w-full bg-[#81928d] hover:bg-[#6d7d78] text-white px-6 py-2 rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300">
                        Admin Portal
                      </Button>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Add top padding to account for floating navbar */}
      <div className="">
        {/* Main Content */}
        <main className="flex-1">{children}</main>
      </div>

      {/* Footer */}
      <motion.footer
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true, margin: "-100px" }}
        className="bg-secondary text-secondary-foreground"
      >
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand Info */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <img
                  src="/kogo.jpeg"
                  alt={getSetting("company_name", "Meadow De Jalsa")}
                  className="h-12 w-12 object-contain rounded-full"
                />
                <div>
                  <h3 className="text-lg font-bold">
                    {getSetting("company_name", "Meadow De Jalsa")}
                  </h3>
                  <p className="text-xs opacity-80">
                    {getSetting("company_tagline", "Luxury Stay & Events")}
                  </p>
                </div>
              </div>
              <p className="text-sm opacity-80">
                {getSetting(
                  "company_description",
                  "Experience luxury hospitality with our premium accommodations, world-class event venues, and exceptional service."
                )}
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-sm">
                {navigation.map((item) => (
                  <li key={item.name}>
                    <Link
                      to={item.href}
                      className="opacity-80 hover:opacity-100 transition-opacity"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h4 className="font-semibold mb-4">Our Services</h4>
              <ul className="space-y-2 text-sm opacity-80">
                {services.slice(0, 6).map((service) => (
                  <li key={service.id}>{service.title}</li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="font-semibold mb-4">Contact Info</h4>
              <div className="space-y-3 text-sm">
                {getSetting("address_full") && (
                  <div className="flex items-start space-x-3">
                    <svg
                      className="h-6 w-6 mt-0.5 opacity-80 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      strokeWidth={2}
                      style={{
                        filter: "none",
                        transform: "none",
                        backfaceVisibility: "hidden",
                      }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <span className="opacity-80">
                      {getSetting(
                        "address_full",
                        "Bardoli–Vyara Highway, Bardoli"
                      )}
                    </span>
                  </div>
                )}
                {getSetting("phone_primary") && (
                  <div className="flex items-center space-x-3">
                    <svg
                      className="h-6 w-6 opacity-80 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      strokeWidth={2}
                      style={{
                        filter: "none",
                        transform: "none",
                        backfaceVisibility: "hidden",
                      }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                    <span className="opacity-80">
                      {getSetting("phone_primary")}
                    </span>
                  </div>
                )}
                {getSetting("email_primary") && (
                  <div className="flex items-center space-x-3">
                    <svg
                      className="h-6 w-6 opacity-80 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      strokeWidth={2}
                      style={{
                        filter: "none",
                        transform: "none",
                        backfaceVisibility: "hidden",
                      }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                    <span className="opacity-80">
                      {getSetting("email_primary")}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="border-t border-secondary-foreground/20 mt-8 pt-8 text-center">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <p className="text-sm opacity-80">
                © {new Date().getFullYear()}{" "}
                {getSetting("company_name", "Meadow De Jalsa")}. All rights
                reserved.
              </p>
              <div className="flex space-x-6 text-sm">
                <Link
                  to="/privacy-policy"
                  className="opacity-80 hover:opacity-100 transition-opacity"
                >
                  Privacy Policy
                </Link>
                <Link
                  to="/terms-conditions"
                  className="opacity-80 hover:opacity-100 transition-opacity"
                >
                  Terms & Conditions
                </Link>
                <Link
                  to="/cancellation-policy"
                  className="opacity-80 hover:opacity-100 transition-opacity"
                >
                  Cancellation Policy
                </Link>
              </div>
            </div>
          </div>
        </div>
      </motion.footer>

      <ScrollToTop />
    </div>
  );
};
