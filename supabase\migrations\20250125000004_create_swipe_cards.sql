-- Create swipe_cards table for dynamic swipe card content
CREATE TABLE IF NOT EXISTS public.swipe_cards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create RLS policies
ALTER TABLE public.swipe_cards ENABLE ROW LEVEL SECURITY;

-- Allow public read access for active cards
CREATE POLICY "Allow public read access for active swipe cards" ON public.swipe_cards
    FOR SELECT USING (is_active = true);

-- Allow authenticated users to manage swipe cards
CREATE POLICY "Allow authenticated users to manage swipe cards" ON public.swipe_cards
    FOR ALL USING (auth.role() = 'authenticated');

-- <PERSON>reate updated_at trigger
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_swipe_cards_updated_at
    BEFORE UPDATE ON public.swipe_cards
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample data
INSERT INTO public.swipe_cards (title, description, image_url, display_order, is_active) VALUES
('Luxury Resort Experience', 'Experience world-class hospitality in our premium resorts', 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 1, true),
('Mountain View Suites', 'Wake up to breathtaking mountain views every morning', 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 2, true),
('Beachfront Paradise', 'Relax by the pristine beaches with crystal clear waters', 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 3, true);