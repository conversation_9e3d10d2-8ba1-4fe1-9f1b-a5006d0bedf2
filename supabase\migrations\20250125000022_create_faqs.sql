-- Create faqs table
CREATE TABLE faqs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX idx_faqs_display_order ON faqs(display_order);
CREATE INDEX idx_faqs_is_active ON faqs(is_active);

-- Enable RLS
ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can view active faqs" ON faqs
  FOR SELECT USING (is_active = true);

CREATE POLICY "Authenticated users can manage faqs" ON faqs
  FOR ALL USING (auth.role() = 'authenticated');

-- Insert default FAQs
INSERT INTO faqs (question, answer, display_order) VALUES
('What are your check-in and check-out times?', 'Check-in is at 2:00 PM and check-out is at 12:00 PM. Early check-in and late check-out may be available upon request.', 1),
('Do you provide event planning services?', 'Yes, we have a dedicated event planning team to help you organize weddings, corporate events, and celebrations.', 2),
('Is parking available?', 'Yes, we provide complimentary valet parking for all our guests with ample and secure parking space.', 3),
('Do you have vegetarian and vegan options?', 'Absolutely! Our restaurant offers extensive vegetarian and vegan options with dedicated preparation areas.', 4);

-- Create trigger for updated_at
CREATE TRIGGER update_faqs_updated_at 
  BEFORE UPDATE ON faqs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();