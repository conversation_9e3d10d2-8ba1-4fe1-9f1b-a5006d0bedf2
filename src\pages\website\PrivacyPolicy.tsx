import React, { useState, useEffect } from 'react';
import { WebsiteLayout } from '@/components/website/WebsiteLayout';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { Shield, Lock, Eye, Users, FileText, Phone, Mail, MapPin } from 'lucide-react';

interface CompanySetting {
  setting_key: string;
  setting_value: string;
}

const PrivacyPolicy: React.FC = () => {
  const [settings, setSettings] = useState<CompanySetting[]>([]);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data } = await supabase
        .from('company_settings')
        .select('setting_key, setting_value')
        .eq('is_active', true)
        .eq('is_deleted', false);

      setSettings(data || []);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const getSetting = (key: string, defaultValue: string = '') => {
    const setting = settings.find(s => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  return (
    <WebsiteLayout>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-emerald-700 pt-24 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 bg-[url('/hero1.png')] bg-cover bg-center opacity-10"></div>
        
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="relative container mx-auto px-4 text-center"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full mb-6"
          >
            <Shield className="w-10 h-10 text-white" />
          </motion.div>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Privacy Policy
          </h1>
          <p className="text-xl text-emerald-100 max-w-2xl mx-auto">
            Your privacy and data protection are our top priorities
          </p>
        </motion.div>
      </section>

      {/* Main Content */}
      <section className="py-16 bg-white mb-0">
        <div className="container mx-auto px-4 max-w-4xl">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-2xl p-8 mb-12"
          >
            <p className="text-lg text-emerald-900 leading-relaxed text-center">
              At <span className="font-semibold">{getSetting('company_name', '[Your Hotel Name]')}</span>, we respect your privacy and are committed to protecting the personal information you share with us. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or use our services.
            </p>
          </motion.div>

          <div className="space-y-12">
            <motion.section 
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl border border-emerald-100 p-8 shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                  <Eye className="w-6 h-6 text-emerald-700" />
                </div>
                <h2 className="text-2xl font-bold text-emerald-900" style={{ fontFamily: "'Cormorant Garamond', serif" }}>1. Information We Collect</h2>
              </div>
              <p className="text-gray-700 mb-6 text-lg">We may collect the following information:</p>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-emerald-50 rounded-xl p-6">
                  <h4 className="font-semibold text-emerald-900 mb-3">Personal Information</h4>
                  <p className="text-gray-700 text-sm">Name, email, phone number, address, payment details, ID proof, and booking details.</p>
                </div>
                <div className="bg-emerald-50 rounded-xl p-6">
                  <h4 className="font-semibold text-emerald-900 mb-3">Usage Data</h4>
                  <p className="text-gray-700 text-sm">IP address, browser type, operating system, pages visited, and time spent on the site.</p>
                </div>
                <div className="bg-emerald-50 rounded-xl p-6">
                  <h4 className="font-semibold text-emerald-900 mb-3">Marketing Data</h4>
                  <p className="text-gray-700 text-sm">Preferences, reviews, and communication history with us.</p>
                </div>
              </div>
            </motion.section>

            {[2,3,4,5,6,7,8,9].map((num, index) => {
              const sections = [
                { icon: Users, title: "How We Use Your Information", content: [
                  "Process and confirm hotel bookings and reservations.",
                  "Provide customer service and respond to inquiries.", 
                  "Send promotional offers, newsletters, and marketing communications (with your consent).",
                  "Improve our website, services, and customer experience.",
                  "Ensure compliance with legal and regulatory requirements."
                ]},
                { icon: Users, title: "Sharing of Information", content: [
                  "Service Providers: Payment gateways, booking engines, IT support, and marketing partners.",
                  "Business Partners: Travel agencies, tour operators, or third-party platforms you book through.",
                  "Legal Authorities: If required by law, court order, or government regulations."
                ], note: "We do not sell or rent your personal information to third parties."},
                { icon: Eye, title: "Cookies & Tracking", content: [
                  "Remember your preferences and login details.",
                  "Provide a personalized browsing experience.",
                  "Track website analytics and performance."
                ], note: "You can disable cookies in your browser settings, but some features may not work properly."},
                { icon: Lock, title: "Data Retention & Security", content: [
                  "We retain your personal data only as long as necessary for bookings, marketing, or legal compliance.",
                  "We implement appropriate security measures (encryption, access control, firewalls) to protect your data."
                ]},
                { icon: Shield, title: "Your Rights", content: [
                  "Access, update, or correct your personal information.",
                  "Request deletion of your data (subject to legal obligations).",
                  "Opt-out of marketing communications at any time.",
                  "Restrict or object to data processing."
                ], note: `To exercise these rights, please contact us at ${getSetting('email_primary', '[Insert Contact Email]')}.`},
                { icon: FileText, title: "Third-Party Links", content: [
                  "Our website may contain links to external sites (e.g., booking platforms, travel partners). We are not responsible for their privacy practices, and we encourage you to review their policies."
                ]},
                { icon: Shield, title: "Children's Privacy", content: [
                  "Our services are not directed to individuals under 18. We do not knowingly collect data from minors."
                ]},
                { icon: FileText, title: "Updates to This Policy", content: [
                  "We may update this Privacy Policy from time to time. Any changes will be posted on this page with the updated effective date."
                ]}
              ];
              
              const section = sections[index];
              const IconComponent = section.icon;
              
              return (
                <motion.section 
                  key={num}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl border border-emerald-100 p-8 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                      <IconComponent className="w-6 h-6 text-emerald-700" />
                    </div>
                    <h2 className="text-2xl font-bold text-emerald-900" style={{ fontFamily: "'Cormorant Garamond', serif" }}>{num}. {section.title}</h2>
                  </div>
                  <ul className="space-y-3">
                    {section.content.map((item, i) => (
                      <li key={i} className="flex items-start">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                  {section.note && (
                    <div className="mt-4 p-4 bg-emerald-50 rounded-lg">
                      <p className="text-emerald-800 font-medium">{section.note}</p>
                    </div>
                  )}
                </motion.section>
              );
            })}
          </div>
        </div>
      </section>
    </WebsiteLayout>
  );
};

export default PrivacyPolicy;