import React, { useState } from 'react';
import { Plus, X, Star, <PERSON>Off, MoveUp, MoveDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card } from '@/components/ui/card';

interface ImageData {
  url: string;
  title?: string;
  type?: string;
  is_primary: boolean;
  order: number;
}

interface JsonImageManagerProps {
  images: ImageData[];
  onImagesChange: (images: ImageData[]) => void;
  imageTypes: { value: string; label: string }[];
  multiple?: boolean;
}

const JsonImageManager: React.FC<JsonImageManagerProps> = ({
  images,
  onImagesChange,
  imageTypes,
  multiple = true
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newImage, setNewImage] = useState<ImageData>({
    url: '',
    title: '',
    type: imageTypes[0]?.value || '',
    is_primary: images.length === 0,
    order: images.length + 1
  });

  const addImage = () => {
    if (!newImage.url.trim()) return;

    const imageToAdd = {
      ...newImage,
      order: images.length + 1,
      is_primary: images.length === 0 || newImage.is_primary
    };

    const updatedImages = [...images, imageToAdd];
    if (imageToAdd.is_primary) {
      updatedImages.forEach((img, i) => {
        if (i !== updatedImages.length - 1) img.is_primary = false;
      });
    }

    onImagesChange(updatedImages);
    setNewImage({
      url: '',
      title: '',
      type: imageTypes[0]?.value || '',
      is_primary: false,
      order: updatedImages.length + 1
    });
    setShowAddForm(false);
  };

  const removeImage = (index: number) => {
    const updatedImages = images.filter((_, i) => i !== index);
    updatedImages.forEach((img, i) => {
      img.order = i + 1;
    });
    onImagesChange(updatedImages);
  };

  const setPrimary = (index: number) => {
    const updatedImages = images.map((img, i) => ({
      ...img,
      is_primary: i === index
    }));
    onImagesChange(updatedImages);
  };

  const moveImage = (index: number, direction: 'up' | 'down') => {
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= images.length) return;

    const updatedImages = [...images];
    [updatedImages[index], updatedImages[newIndex]] = [updatedImages[newIndex], updatedImages[index]];
    
    updatedImages.forEach((img, i) => {
      img.order = i + 1;
    });

    onImagesChange(updatedImages);
  };

  if (!multiple) {
    return (
      <div className="space-y-4">
        <Label className="text-base font-medium">Image</Label>
        <div className="flex gap-4">
          <Input
            placeholder="Image URL"
            value={images[0]?.url || ''}
            onChange={(e) => onImagesChange([{
              url: e.target.value,
              title: '',
              type: '',
              is_primary: true,
              order: 1
            }])}
          />
          {images[0]?.url && (
            <img
              src={images[0].url}
              alt="Preview"
              className="w-16 h-16 object-cover rounded"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder.svg';
              }}
            />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Label className="text-base font-medium">Images</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowAddForm(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Image
        </Button>
      </div>

      <div className="grid gap-4">
        {images.map((image, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-start gap-4">
              <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={image.url}
                  alt={image.title || 'Image'}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder.svg';
                  }}
                />
              </div>
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{image.title || 'Untitled'}</span>
                  {image.is_primary && (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  )}
                </div>
                <p className="text-sm text-gray-600">{image.type}</p>
              </div>
              <div className="flex flex-col gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setPrimary(index)}
                  disabled={image.is_primary}
                >
                  {image.is_primary ? (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  ) : (
                    <StarOff className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => moveImage(index, 'up')}
                  disabled={index === 0}
                >
                  <MoveUp className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => moveImage(index, 'down')}
                  disabled={index === images.length - 1}
                >
                  <MoveDown className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeImage(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {showAddForm && (
        <Card className="p-4 border-dashed">
          <div className="space-y-4">
            <div>
              <Label htmlFor="image_url">Image URL *</Label>
              <Input
                id="image_url"
                value={newImage.url}
                onChange={(e) => setNewImage({ ...newImage, url: e.target.value })}
                placeholder="Enter image URL or path"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="image_title">Title</Label>
                <Input
                  id="image_title"
                  value={newImage.title}
                  onChange={(e) => setNewImage({ ...newImage, title: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="image_type">Type</Label>
                <Select
                  value={newImage.type}
                  onValueChange={(value) => setNewImage({ ...newImage, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {imageTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
              >
                Cancel
              </Button>
              <Button type="button" onClick={addImage}>
                Add Image
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default JsonImageManager;