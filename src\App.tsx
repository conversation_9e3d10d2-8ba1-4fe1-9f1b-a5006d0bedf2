import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useAuth } from "./hooks/useAuth";
import LoginForm from "./components/auth/LoginForm";
import MainLayout from "./components/layout/MainLayout";
import { WebsiteLayout } from "./components/website/WebsiteLayout";
import Homepage from "./pages/website/Homepage";
import AboutUs from "./pages/website/AboutUs";
import Services from "./pages/website/Services";
import WebsiteProperties from "./pages/website/Properties";
import Events from "./pages/website/Events";
import Contact from "./pages/website/Contact";
import FAQ from "./pages/website/FAQ";
import PrivacyPolicy from "./pages/website/PrivacyPolicy";
import PrivacyPolicyTest from "./pages/website/PrivacyPolicyTest";
import TermsConditions from "./pages/website/TermsConditions";
import CancellationPolicy from "./pages/website/CancellationPolicy";
import PreviewDesign from "./pages/website/PreviewDesign";
import Dashboard from "./pages/Dashboard";
import GuestDashboard from "./pages/GuestDashboard";
import ReceptionistDashboard from "./pages/ReceptionistDashboard";
import HousekeepingDashboard from "./pages/HousekeepingDashboard";
import Rooms from "./pages/Rooms";
import Bookings from "./pages/Bookings";
import Guests from "./pages/Guests";
import Housekeeping from "./pages/Housekeeping";
import Staff from "./pages/Staff";
import Restaurant from "./pages/Restaurant";
import Payments from "./pages/Payments";
import Reports from "./pages/Reports";
import Tasks from "./pages/Tasks";
import Profile from "./pages/Profile";
import Maintenance from "./pages/Maintenance";
import GuestCRM from "./pages/GuestCRM";
import MultiProperty from "./pages/MultiProperty";
import OTAManager from "./pages/OTAManager";
import NotFound from "./pages/NotFound";
import ContentManager from "./components/admin/ContentManager";
import { ProtectedPage } from "./components/ProtectedPage";
import { FEATURE_FLAGS } from "./utils/featureFlags";

// Master Tables
import Properties from "./pages/master/Properties";
import RoomTypes from "./pages/master/RoomTypes";
import Amenities from "./pages/master/Amenities";
import MasterRooms from "./pages/master/Rooms";
import CelebrationEvents from "./pages/master/CelebrationEvents";
import MasterTablesView from "./pages/master/MasterTablesView";

const queryClient = new QueryClient();

const App = () => {
  const { user, loading, signOut } = useAuth();
  const [userRole, setUserRole] = useState<string>('');
  
  // Set default role for authenticated users
  useEffect(() => {
    if (user?.email) {
      const role = 'hotel-manager';
      setUserRole(role);
      localStorage.setItem('hotel-role', role);
    } else {
      setUserRole('');
      localStorage.removeItem('hotel-role');
    }
  }, [user]);

  const handleLogin = (email: string, role: string) => {
    // Authentication is handled by Supabase, just set the role
    setUserRole(role);
    localStorage.setItem('hotel-role', role);
  };

  const handleLogout = async () => {
    await signOut();
    setUserRole('');
    localStorage.removeItem('hotel-role');
  };

  const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
    if (loading) {
      return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
    }
    return user ? children : <Navigate to="/admin" replace />;
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Public Website Routes */}
            <Route path="/" element={<WebsiteLayout><Homepage /></WebsiteLayout>} />
            <Route path="/about" element={
              <ProtectedPage flagKey={FEATURE_FLAGS.PAGE_ABOUT_VISIBLE}>
                <WebsiteLayout><AboutUs /></WebsiteLayout>
              </ProtectedPage>
            } />
            <Route path="/services" element={
              <ProtectedPage flagKey={FEATURE_FLAGS.PAGE_SERVICES_VISIBLE}>
                <WebsiteLayout><Services /></WebsiteLayout>
              </ProtectedPage>
            } />
            <Route path="/properties" element={
              <ProtectedPage flagKey={FEATURE_FLAGS.PAGE_PROPERTIES_VISIBLE}>
                <WebsiteLayout><WebsiteProperties /></WebsiteLayout>
              </ProtectedPage>
            } />
            <Route path="/events" element={
              <ProtectedPage flagKey={FEATURE_FLAGS.PAGE_EVENTS_VISIBLE}>
                <WebsiteLayout><Events /></WebsiteLayout>
              </ProtectedPage>
            } />
            <Route path="/contact" element={<WebsiteLayout><Contact /></WebsiteLayout>} />
            <Route path="/faq" element={<WebsiteLayout><FAQ /></WebsiteLayout>} />
            <Route path="/privacy-policy" element={<PrivacyPolicy />} />
            <Route path="/terms-conditions" element={<TermsConditions />} />
            <Route path="/cancellation-policy" element={<CancellationPolicy />} />
            <Route path="/privacy-test" element={<PrivacyPolicyTest />} />
            <Route path="/preview" element={<PreviewDesign />} />
            
            {/* Admin Login Route */}
            <Route path="/admin" element={
              loading ? (
                <div className="flex items-center justify-center min-h-screen">Loading...</div>
              ) : user ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginForm onLogin={handleLogin} />
              )
            } />
            
            {/* Protected Admin Routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <MainLayout userRole={userRole} onLogout={handleLogout}>
                  {userRole === 'guest' ? <GuestDashboard /> :
                   userRole === 'receptionist' ? <ReceptionistDashboard /> :
                   userRole === 'housekeeping' ? <HousekeepingDashboard /> :
                   <Dashboard userRole={userRole} />}
                </MainLayout>
              </ProtectedRoute>
            } />
            <Route path="/rooms" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Rooms /></MainLayout></ProtectedRoute>} />
            <Route path="/bookings" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Bookings /></MainLayout></ProtectedRoute>} />
            <Route path="/guests" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Guests /></MainLayout></ProtectedRoute>} />
            <Route path="/housekeeping" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Housekeeping /></MainLayout></ProtectedRoute>} />
            <Route path="/tasks" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Tasks /></MainLayout></ProtectedRoute>} />
            <Route path="/staff" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Staff /></MainLayout></ProtectedRoute>} />
            <Route path="/maintenance" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Maintenance /></MainLayout></ProtectedRoute>} />
            <Route path="/guest-crm" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><GuestCRM /></MainLayout></ProtectedRoute>} />
            <Route path="/multi-property" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><MultiProperty /></MainLayout></ProtectedRoute>} />
            <Route path="/ota-manager" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><OTAManager /></MainLayout></ProtectedRoute>} />
            <Route path="/restaurant" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Restaurant /></MainLayout></ProtectedRoute>} />
            <Route path="/reports" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Reports /></MainLayout></ProtectedRoute>} />
            <Route path="/payments" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Payments /></MainLayout></ProtectedRoute>} />
            <Route path="/profile" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Profile /></MainLayout></ProtectedRoute>} />
            <Route path="/website-management" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><ContentManager /></MainLayout></ProtectedRoute>} />
            
            {/* Master Tables Routes */}
            <Route path="/master" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><MasterTablesView /></MainLayout></ProtectedRoute>} />
            <Route path="/master/properties" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Properties /></MainLayout></ProtectedRoute>} />
            <Route path="/master/room-types" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><RoomTypes /></MainLayout></ProtectedRoute>} />
            <Route path="/master/amenities" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><Amenities /></MainLayout></ProtectedRoute>} />
            <Route path="/master/rooms" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><MasterRooms /></MainLayout></ProtectedRoute>} />
            <Route path="/master/celebration-events" element={<ProtectedRoute><MainLayout userRole={userRole} onLogout={handleLogout}><CelebrationEvents /></MainLayout></ProtectedRoute>} />
            
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
