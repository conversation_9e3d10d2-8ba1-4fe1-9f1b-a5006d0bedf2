import React, { useState, useEffect } from 'react';
import { WebsiteLayout } from '@/components/website/WebsiteLayout';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { Calendar, X, Clock, Users, Edit, RefreshCw, AlertTriangle, Phone, Mail, MapPin } from 'lucide-react';

interface CompanySetting {
  setting_key: string;
  setting_value: string;
}

const CancellationPolicy: React.FC = () => {
  const [settings, setSettings] = useState<CompanySetting[]>([]);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data } = await supabase
        .from('company_settings')
        .select('setting_key, setting_value')
        .eq('is_active', true)
        .eq('is_deleted', false);

      setSettings(data || []);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const getSetting = (key: string, defaultValue: string = '') => {
    const setting = settings.find(s => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  return (
    <WebsiteLayout>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-emerald-700 pt-24 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 bg-[url('/hero1.png')] bg-cover bg-center opacity-10"></div>
        
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="relative container mx-auto px-4 text-center"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full mb-6"
          >
            <Calendar className="w-10 h-10 text-white" />
          </motion.div>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Cancellation Policy
          </h1>
          <p className="text-xl text-emerald-100 max-w-2xl mx-auto">
            Flexible booking terms designed with our guests in mind
          </p>
        </motion.div>
      </section>

      {/* Main Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-2xl p-8 mb-12"
          >
            <p className="text-lg text-emerald-900 leading-relaxed text-center">
              At <span className="font-semibold">{getSetting('company_name', '[Your Hotel Name]')}</span>, we value our guests and aim to provide flexibility with reservations. This Cancellation Policy outlines the terms for cancellations, changes, refunds, and no-shows.
            </p>
          </motion.div>

          <div className="space-y-12">
            {[
              {
                icon: Calendar,
                title: "Standard Cancellations",
                content: [
                  "Reservations canceled 24 hours before check-in are eligible for a full refund.",
                  "Reservations canceled within 24 hours of check-in may be charged a cancellation fee of 50% or one night's stay."
                ]
              },
              {
                icon: X,
                title: "No-Show Policy",
                content: [
                  "If a guest does not arrive on the scheduled check-in date without prior notice, the booking will be marked as a No-Show.",
                  "No-shows will be charged the full booking amount and will not be eligible for refunds."
                ]
              },
              {
                icon: Clock,
                title: "Early Departure",
                content: [
                  "Guests who check out before the confirmed departure date may be charged for the remaining nights as per the booking terms."
                ]
              },
              {
                icon: X,
                title: "Non-Refundable Bookings",
                content: [
                  "Certain promotional rates, peak-season reservations, and discounted bookings may be non-refundable.",
                  "These terms will be clearly stated at the time of booking."
                ]
              },
              {
                icon: Users,
                title: "Group & Long-Stay Bookings",
                content: [
                  "Special cancellation rules may apply for group reservations (more than 5 rooms) or long-stay bookings.",
                  "These terms will be communicated during booking confirmation."
                ]
              },
              {
                icon: Edit,
                title: "Modifications to Bookings",
                content: [
                  "Date changes or modifications are subject to availability and may incur additional charges.",
                  "Any modification is treated as a new booking and may follow different cancellation terms."
                ]
              },
              {
                icon: RefreshCw,
                title: "Refund Process",
                content: [
                  "Refunds (if applicable) will be processed within 7-10 working days via the original payment method.",
                  "Bank charges, service fees, or payment gateway fees may be deducted from the refund amount."
                ]
              },
              {
                icon: AlertTriangle,
                title: "Force Majeure / Unforeseen Circumstances",
                content: [
                  "In case of natural disasters, government restrictions, pandemics, or unforeseen events, cancellation terms may be relaxed at the discretion of the hotel."
                ]
              }
            ].map((section, index) => {
              const IconComponent = section.icon;
              
              return (
                <motion.section 
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl border border-emerald-100 p-8 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                      <IconComponent className="w-6 h-6 text-emerald-700" />
                    </div>
                    <h2 className="text-2xl font-bold text-emerald-900" style={{ fontFamily: "'Cormorant Garamond', serif" }}>{index + 1}. {section.title}</h2>
                  </div>
                  <ul className="space-y-3">
                    {section.content.map((item, i) => (
                      <li key={i} className="flex items-start">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </motion.section>
              );
            })}

            {/* Contact Section */}
            <motion.section 
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-emerald-900 to-emerald-800 rounded-2xl p-8 text-white"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4">
                  <Phone className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white" style={{ fontFamily: "'Cormorant Garamond', serif" }}>9. Contact for Cancellations</h2>
              </div>
              <p className="text-emerald-100 mb-6">To request a cancellation or modification, please contact us at:</p>
              <div className="bg-white/10 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-4">{getSetting('company_name', '[Your Hotel Name]')}</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="flex items-center">
                    <Mail className="w-5 h-5 text-emerald-200 mr-2" />
                    <span className="text-emerald-100">{getSetting('email_primary', '[Insert Email]')}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="w-5 h-5 text-emerald-200 mr-2" />
                    <span className="text-emerald-100">{getSetting('phone_primary', '[Insert Phone]')}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-emerald-200 mr-2" />
                    <span className="text-emerald-100">{getSetting('address_full', '[Insert Address]')}</span>
                  </div>
                </div>
              </div>
            </motion.section>
          </div>
        </div>
      </section>
    </WebsiteLayout>
  );
};

export default CancellationPolicy;