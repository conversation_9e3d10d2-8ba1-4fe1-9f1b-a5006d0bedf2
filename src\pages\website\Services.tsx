import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import DiningSection from '@/components/website/DiningSection';
import AccommodationSection from '@/components/website/AccommodationSection';
import EventsSection from '@/components/website/EventsSection';
import AmenitiesSection from '@/components/website/AmenitiesSection';
import OperatingHoursSection from '@/components/website/OperatingHoursSection';
import { 
  ChefHat, 
  Bed, 
  PartyPopper, 
  Wine, 
  Users, 
  Clock, 
  Star,
  Utensils,
  Coffee,
  Car,
  Wifi,
  Phone,
  MapPin,
  CheckCircle,
  ArrowRight,
  Calendar,
  Building2,
  Flame,
  Check,
  PhoneCall,
  Images,
  Snowflake,
  Tv2,
  ShowerHead,
  Bath,
  Crown,
  Sparkles,
  Shield,
  Heart
} from 'lucide-react';

const Services = () => {

  return (
    <div className="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900 min-h-screen" style={{ fontFamily: "'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'", scrollBehavior: 'smooth' }}>
      {/* Hero Section */}
      <section className="relative">
        <div className="relative h-[70vh] w-full overflow-hidden">
          <motion.img
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 w-full h-full object-cover"
            src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=1760&auto=format&fit=crop"
            alt="Restaurant Services"
          />
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute inset-0 bg-gradient-to-b from-emerald-950/70 via-emerald-950/50 to-emerald-950/70"
          ></motion.div>
          <div className="relative z-10 h-full">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-6">
                  <motion.div 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm ring-1 ring-white/20"
                  >
                    <Utensils className="w-4 h-4 text-amber-300" />
                    <span className="text-white/90 text-sm font-medium">Our Services</span>
                  </motion.div>
                  <motion.h1 
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 1, delay: 1, ease: "easeOut" }}
                    className="text-4xl sm:text-5xl lg:text-6xl font-semibold leading-[1.1] tracking-tight text-white" 
                    style={{ fontFamily: "'Cormorant Garamond', serif" }}
                  >
                    Complete <span className="text-amber-300">Hospitality</span> Solutions
                  </motion.h1>
                  <motion.p 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 1.3, ease: "easeOut" }}
                    className="text-lg sm:text-xl text-white/90 leading-relaxed max-w-3xl mx-auto"
                  >
                    From signature dining and luxury accommodation to memorable events - experience excellence at every touchpoint
                  </motion.p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Restaurant Menu */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <DiningSection />
      </motion.div>

      {/* Accommodation Services */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <AccommodationSection />
      </motion.div>

      {/* Event Services */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <EventsSection />
      </motion.div>

      {/* Premium Amenities */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <AmenitiesSection />
      </motion.div>

      {/* Operating Hours */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <OperatingHoursSection />
      </motion.div>

      {/* Contact CTA */}
      <section className="bg-emerald-950">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center"
        >
          <motion.h2 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-3xl sm:text-4xl tracking-tight text-white font-semibold" 
            style={{ fontFamily: "'Cormorant Garamond', serif" }}
          >
            Ready to Experience Our Services?
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-3 text-white/85 max-w-2xl mx-auto"
          >
            Contact us today to reserve your table, book your stay, or plan your event. Our team is ready to make your experience exceptional.
          </motion.p>
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4"
          >
            <motion.a 
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              href="tel:+918740027008" 
              className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500"
            >
              <Phone className="w-5 h-5" />
              Call: +91 87400-27008
            </motion.a>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link to="/contact" className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                <MapPin className="w-5 h-5" />
                Visit Us Today
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>
    </div>
  );
};

export default Services;