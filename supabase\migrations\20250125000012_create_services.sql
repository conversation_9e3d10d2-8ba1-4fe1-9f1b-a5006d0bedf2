-- Create services table for "What We Offer" section
CREATE TABLE IF NOT EXISTS public.services (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text NOT NULL,
  icon text NOT NULL,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate policies
CREATE POLICY "Allow public read access to services" ON public.services
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage services" ON public.services
  FOR ALL USING (auth.role() = 'authenticated');

-- Create updated_at trigger
CREATE TRIGGER handle_services_updated_at
  BEFORE UPDATE ON public.services
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample services
INSERT INTO public.services (title, description, icon, display_order, is_active) VALUES
('Signature Dining', 'Multi-cuisine restaurant with live grill, extensive vegan options, and curated seasonal menus served in elegant surroundings.', 'Utensils', 1, true),
('Luxury Accommodation', 'Premium rooms and suites with modern amenities, personalized service, and stunning views for business and leisure travelers.', 'Building2', 2, true),
('Event Venues', 'Elegant banquet halls and outdoor spaces for weddings, corporate events, and celebrations with full-service event management.', 'PartyPopper', 3, true);