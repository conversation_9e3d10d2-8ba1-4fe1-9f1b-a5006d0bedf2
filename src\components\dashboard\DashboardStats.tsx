import React, { useMemo, useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Bed,
  CalendarCheck,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
} from "lucide-react";
import { motion } from "framer-motion";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ElementType;
  trend?: string;
  trendUp?: boolean;
  color?: "primary" | "gold" | "success" | "warning";
  index?: number;
}

// Move colorClasses outside component to prevent recreation
const COLOR_CLASSES = {
  primary:
    "bg-gradient-to-br from-primary to-primary-light text-primary-foreground shadow-lg",
  gold: "bg-gradient-to-br from-gold to-gold-light text-gold-foreground shadow-gold",
  success:
    "bg-gradient-to-br from-success to-green-600 text-success-foreground shadow-lg",
  warning:
    "bg-gradient-to-br from-warning to-orange-500 text-warning-foreground shadow-lg",
} as const;

// Improved color classes for better contrast and accessibility
const IMPROVED_COLOR_CLASSES = {
  primary: "bg-primary text-primary-foreground shadow-lg",
  gold: "bg-gold text-gold-foreground shadow-gold",
  success: "bg-success text-success-foreground shadow-lg",
  warning: "bg-warning text-warning-foreground shadow-lg",
} as const;

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon: Icon,
  trend,
  trendUp,
  color = "primary",
  index = 0,
}) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        delay: index * 0.08,
        ease: [0.4, 0, 0.2, 1], // Custom cubic-bezier for smoother animation
      },
    },
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={{
        scale: 1.02,
        y: -4,
        transition: {
          duration: 0.2,
          ease: [0.4, 0, 0.2, 1],
        },
      }}
    >
      <Card className="relative overflow-hidden border-0 bg-white/80 backdrop-blur-sm shadow-elegant hover:shadow-card transition-all duration-300 h-full">
        {/* Background gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent" />

        <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
          <CardTitle className="text-xs sm:text-sm font-semibold text-muted-foreground tracking-wide uppercase leading-tight">
            {title}
          </CardTitle>
          <div
            className={`p-2 sm:p-3 rounded-lg sm:rounded-xl ${IMPROVED_COLOR_CLASSES[color]} transform transition-transform hover:scale-110 flex-shrink-0`}
          >
            <Icon className="h-4 w-4 sm:h-5 sm:w-5 icon-crisp" />
          </div>
        </CardHeader>

        <CardContent className="relative pt-0 flex flex-col justify-between flex-1">
          <div className="text-2xl sm:text-3xl font-bold text-foreground mb-1 sm:mb-2 tracking-tight leading-tight">
            {value}
          </div>
          {trend && (
            <div
              className={`flex items-center text-xs sm:text-sm font-medium ${
                trendUp ? "text-success" : "text-destructive"
              }`}
            >
              {trendUp ? (
                <ArrowUpRight className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
              ) : (
                <ArrowDownRight className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
              )}
              <span className="truncate">{trend}</span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

interface DashboardStatsProps {
  userRole: string;
}

// Loading skeleton component
const StatCardSkeleton: React.FC = () => (
  <Card className="relative overflow-hidden border-0 bg-white/80 backdrop-blur-sm shadow-elegant h-full">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
      <Skeleton className="h-3 w-20" />
      <Skeleton className="h-8 w-8 rounded-lg" />
    </CardHeader>
    <CardContent className="pt-0">
      <Skeleton className="h-8 w-16 mb-2" />
      <Skeleton className="h-4 w-12" />
    </CardContent>
  </Card>
);

export const DashboardStats: React.FC<DashboardStatsProps> = ({ userRole }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simulate data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const stats = useMemo(() => {
    switch (userRole) {
      case "hotel-manager":
        return [
          {
            title: "Total Rooms",
            value: 120,
            icon: Bed,
            trend: "+2 this month",
            trendUp: true,
          },
          {
            title: "Occupancy Rate",
            value: "85%",
            icon: CalendarCheck,
            trend: "+12%",
            trendUp: true,
            color: "success" as const,
          },
          {
            title: "Active Guests",
            value: 98,
            icon: Users,
            trend: "+5 today",
            trendUp: true,
          },
          {
            title: "Revenue Today",
            value: "$8,450",
            icon: DollarSign,
            trend: "+23%",
            trendUp: true,
            color: "gold" as const,
          },
        ];

      case "receptionist":
        return [
          {
            title: "Check-ins Today",
            value: 12,
            icon: CalendarCheck,
            color: "success" as const,
          },
          {
            title: "Check-outs Today",
            value: 8,
            icon: Clock,
            color: "warning" as const,
          },
          {
            title: "Pending Payments",
            value: 3,
            icon: DollarSign,
            color: "gold" as const,
          },
          { title: "Available Rooms", value: 22, icon: Bed },
        ];

      case "housekeeping":
        return [
          {
            title: "Rooms to Clean",
            value: 15,
            icon: AlertCircle,
            color: "warning" as const,
          },
          {
            title: "Completed Today",
            value: 8,
            icon: CheckCircle,
            color: "success" as const,
          },
          { title: "In Progress", value: 3, icon: Clock },
          {
            title: "Maintenance Requests",
            value: 2,
            icon: Bed,
            color: "gold" as const,
          },
        ];

      case "guest":
        return [
          {
            title: "Current Booking",
            value: "Room 205",
            icon: Bed,
            color: "success" as const,
          },
          {
            title: "Check-out",
            value: "Tomorrow",
            icon: Clock,
            color: "warning" as const,
          },
          {
            title: "Loyalty Points",
            value: 1250,
            icon: TrendingUp,
            color: "gold" as const,
          },
          { title: "Total Stays", value: 8, icon: CalendarCheck },
        ];

      default:
        return [
          {
            title: "Total Hotels",
            value: 25,
            icon: Bed,
            trend: "+3 this month",
            trendUp: true,
          },
          {
            title: "Active Users",
            value: 1840,
            icon: Users,
            trend: "+12%",
            trendUp: true,
            color: "success" as const,
          },
          {
            title: "Monthly Revenue",
            value: "$284K",
            icon: DollarSign,
            trend: "+8%",
            trendUp: true,
            color: "gold" as const,
          },
          {
            title: "Avg Occupancy",
            value: "78%",
            icon: CalendarCheck,
            trend: "+5%",
            trendUp: true,
          },
        ];
    }
  }, [userRole]);

  if (error) {
    return (
      <motion.div
        className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <Card className="col-span-full bg-destructive/10 border-destructive/20">
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-destructive mb-2">
                Failed to load data
              </h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <button
                onClick={() => {
                  setError(null);
                  setIsLoading(true);
                  setTimeout(() => setIsLoading(false), 1000);
                }}
                className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {isLoading
        ? Array.from({ length: 4 }).map((_, index) => (
            <StatCardSkeleton key={index} />
          ))
        : stats.map((stat, index) => (
            <StatCard key={stat.title} {...stat} index={index} />
          ))}
    </motion.div>
  );
};

export default DashboardStats;
