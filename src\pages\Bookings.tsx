import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  CalendarCheck, 
  Search, 
  Filter,
  Plus,
  Eye,
  Edit,
  Calendar,
  User,
  Phone,
  Mail,
  Bed
} from 'lucide-react';

interface Booking {
  id: string;
  confirmationNumber: string;
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  roomNumber: string;
  roomType: string;
  checkIn: string;
  checkOut: string;
  nights: number;
  guests: number;
  totalAmount: number;
  status: 'confirmed' | 'checked-in' | 'checked-out' | 'cancelled' | 'no-show';
  bookingDate: string;
}

const mockBookings: Booking[] = [
  {
    id: '1',
    confirmationNumber: 'HTL001234',
    guestName: '<PERSON>',
    guestEmail: '<EMAIL>',
    guestPhone: '+****************',
    roomNumber: '205',
    roomType: 'Deluxe',
    checkIn: '2024-01-15',
    checkOut: '2024-01-18',
    nights: 3,
    guests: 2,
    totalAmount: 540,
    status: 'confirmed',
    bookingDate: '2024-01-10'
  },
  {
    id: '2',
    confirmationNumber: 'HTL001235',
    guestName: 'Emma Wilson',
    guestEmail: '<EMAIL>',
    guestPhone: '+****************',
    roomNumber: '108',
    roomType: 'Suite',
    checkIn: '2024-01-14',
    checkOut: '2024-01-16',
    nights: 2,
    guests: 4,
    totalAmount: 700,
    status: 'checked-in',
    bookingDate: '2024-01-08'
  },
  {
    id: '3',
    confirmationNumber: 'HTL001236',
    guestName: 'Michael Brown',
    guestEmail: '<EMAIL>',
    guestPhone: '+****************',
    roomNumber: '103',
    roomType: 'Standard',
    checkIn: '2024-01-12',
    checkOut: '2024-01-14',
    nights: 2,
    guests: 1,
    totalAmount: 240,
    status: 'checked-out',
    bookingDate: '2024-01-05'
  }
];

const statusColors = {
  confirmed: 'bg-primary text-primary-foreground',
  'checked-in': 'bg-success text-success-foreground',
  'checked-out': 'bg-muted text-muted-foreground',
  cancelled: 'bg-destructive text-destructive-foreground',
  'no-show': 'bg-warning text-warning-foreground'
};

const statusLabels = {
  confirmed: 'Confirmed',
  'checked-in': 'Checked In',
  'checked-out': 'Checked Out',
  cancelled: 'Cancelled',
  'no-show': 'No Show'
};

const BookingCard: React.FC<{ booking: Booking }> = ({ booking }) => {
  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{booking.confirmationNumber}</CardTitle>
            <p className="text-muted-foreground">{booking.guestName}</p>
          </div>
          <Badge className={statusColors[booking.status]}>
            {statusLabels[booking.status]}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Guest Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm truncate">{booking.guestEmail}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{booking.guestPhone}</span>
            </div>
          </div>

          {/* Room Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Bed className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Room {booking.roomNumber} - {booking.roomType}</span>
            </div>
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{booking.guests} guest(s)</span>
            </div>
          </div>

          {/* Dates */}
          <div className="bg-accent p-3 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-muted-foreground">Check-in</p>
                <p className="text-sm font-medium">{new Date(booking.checkIn).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Check-out</p>
                <p className="text-sm font-medium">{new Date(booking.checkOut).toLocaleDateString()}</p>
              </div>
            </div>
            <div className="mt-2 flex justify-between items-center">
              <span className="text-xs text-muted-foreground">{booking.nights} nights</span>
              <span className="text-sm font-semibold">${booking.totalAmount}</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-2">
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
            <Button size="sm" variant="outline" className="flex-1">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            {booking.status === 'confirmed' && (
              <Button size="sm" className="flex-1 btn-primary">
                Check In
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Bookings: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredBookings = mockBookings.filter(booking => {
    const matchesSearch = 
      booking.guestName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.confirmationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.roomNumber.includes(searchTerm);
    
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const statusCounts = {
    total: mockBookings.length,
    confirmed: mockBookings.filter(b => b.status === 'confirmed').length,
    checkedIn: mockBookings.filter(b => b.status === 'checked-in').length,
    checkedOut: mockBookings.filter(b => b.status === 'checked-out').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Booking Management</h1>
          <p className="text-muted-foreground">Manage hotel reservations and guest bookings</p>
        </div>
        <Button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          New Booking
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{statusCounts.total}</div>
            <div className="text-sm text-muted-foreground">Total Bookings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{statusCounts.confirmed}</div>
            <div className="text-sm text-muted-foreground">Confirmed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-success">{statusCounts.checkedIn}</div>
            <div className="text-sm text-muted-foreground">Checked In</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-muted-foreground">{statusCounts.checkedOut}</div>
            <div className="text-sm text-muted-foreground">Checked Out</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search bookings, guests, rooms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Bookings</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="checked-in">Checked In</SelectItem>
                <SelectItem value="checked-out">Checked Out</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="no-show">No Show</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Date Range
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bookings Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {filteredBookings.map((booking) => (
          <BookingCard key={booking.id} booking={booking} />
        ))}
      </div>

      {filteredBookings.length === 0 && (
        <div className="text-center py-12">
          <CalendarCheck className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No bookings found</h3>
          <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Bookings;