import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import SimpleImageManager from './SimpleImageManager';

interface Room {
  id?: string;
  property_id: string;
  room_type_id: string;
  room_number: string;
  floor?: number;
  wing?: string;
  view_type?: string;
  status: string;
  key_code?: string;
  tax_percentages?: string;
  is_active: boolean;
}

interface Property {
  id: string;
  name: string;
}

interface RoomType {
  id: string;
  name: string;
  code: string;
  property_id: string;
}

interface RoomsFormProps {
  room?: Room | null;
  onClose: () => void;
  onSuccess: () => void;
}

const RoomsForm: React.FC<RoomsFormProps> = ({ room, onClose, onSuccess }) => {
  const [formData, setFormData] = useState<Room>({
    property_id: '',
    room_type_id: '',
    room_number: '',
    floor: 0,
    wing: '',
    view_type: '',
    status: 'AVAILABLE',
    key_code: '',
    tax_percentages: '',
    is_active: true,
  });
  const [properties, setProperties] = useState<Property[]>([]);
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [images, setImages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const roomImageTypes = [
    { value: 'BEDROOM', label: 'Bedroom' },
    { value: 'BATHROOM', label: 'Bathroom' },
    { value: 'BALCONY', label: 'Balcony' },
    { value: 'VIEW', label: 'View' },
    { value: 'AMENITY', label: 'Amenity' },
    { value: 'ENTRANCE', label: 'Entrance' },
    { value: 'OTHER', label: 'Other' }
  ];

  useEffect(() => {
    fetchProperties();
    if (room) {
      setFormData(room);
      fetchRoomTypes(room.property_id);
      fetchRoomImages(room.id!);
    }
  }, [room]);

  const fetchRoomImages = async (roomId: string) => {
    try {
      const { data, error } = await supabase
        .from('rooms')
        .select('images')
        .eq('id', roomId)
        .single();

      if (error) throw error;
      setImages(data?.images || []);
    } catch (error) {
      console.error('Error fetching room images:', error);
    }
  };

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from('properties_admin')
        .select('id, name')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setProperties(data || []);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch properties',
        variant: 'destructive',
      });
    }
  };

  const fetchRoomTypes = async (propertyId: string) => {
    if (!propertyId) {
      setRoomTypes([]);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('room_types')
        .select('id, name, code, property_id')
        .eq('property_id', propertyId)
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setRoomTypes(data || []);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch room types',
        variant: 'destructive',
      });
    }
  };

  const handlePropertyChange = (propertyId: string) => {
    setFormData({ ...formData, property_id: propertyId, room_type_id: '' });
    fetchRoomTypes(propertyId);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      const submitData = {
        ...formData,
        floor: formData.floor || null,
        wing: formData.wing || null,
        view_type: formData.view_type || null,
        key_code: formData.key_code || null,
        tax_percentages: formData.tax_percentages || null,
        images: images,
        created_by: user?.id,
        updated_by: user?.id,
      };

      if (room?.id) {
        const { error } = await supabase
          .from('rooms')
          .update(submitData)
          .eq('id', room.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('rooms')
          .insert([submitData]);
        if (error) throw error;
      }

      toast({
        title: 'Success',
        description: `Room ${room?.id ? 'updated' : 'created'} successfully`,
      });
      onSuccess();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save room',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>{room?.id ? 'Edit Room' : 'Add New Room'}</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Property and Room Type Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="property_id">Property *</Label>
                <Select value={formData.property_id} onValueChange={handlePropertyChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a property" />
                  </SelectTrigger>
                  <SelectContent>
                    {properties.map((property) => (
                      <SelectItem key={property.id} value={property.id}>
                        {property.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="room_type_id">Room Type *</Label>
                <Select value={formData.room_type_id} onValueChange={(value) => setFormData({ ...formData, room_type_id: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select room type" />
                  </SelectTrigger>
                  <SelectContent>
                    {roomTypes.map((roomType) => (
                      <SelectItem key={roomType.id} value={roomType.id}>
                        {roomType.name} ({roomType.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Room Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="room_number">Room Number *</Label>
                <Input
                  id="room_number"
                  value={formData.room_number}
                  onChange={(e) => setFormData({ ...formData, room_number: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="floor">Floor</Label>
                <Input
                  id="floor"
                  type="number"
                  min="0"
                  value={formData.floor || ''}
                  onChange={(e) => setFormData({ ...formData, floor: e.target.value ? parseInt(e.target.value) : undefined })}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="wing">Wing</Label>
                <Input
                  id="wing"
                  value={formData.wing}
                  onChange={(e) => setFormData({ ...formData, wing: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="view_type">View Type</Label>
                <Select value={formData.view_type || 'none'} onValueChange={(value) => setFormData({ ...formData, view_type: value === 'none' ? undefined : value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select view type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No specific view</SelectItem>
                    <SelectItem value="Sea View">Sea View</SelectItem>
                    <SelectItem value="Garden View">Garden View</SelectItem>
                    <SelectItem value="City View">City View</SelectItem>
                    <SelectItem value="Mountain View">Mountain View</SelectItem>
                    <SelectItem value="Pool View">Pool View</SelectItem>
                    <SelectItem value="Courtyard View">Courtyard View</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Status and Additional Info */}
            <div>
              <Label htmlFor="status">Room Status *</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="AVAILABLE">Available</SelectItem>
                  <SelectItem value="OCCUPIED">Occupied</SelectItem>
                  <SelectItem value="MAINTENANCE">Maintenance</SelectItem>
                  <SelectItem value="OUT_OF_ORDER">Out of Order</SelectItem>
                  <SelectItem value="CLEANING">Cleaning</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="key_code">Key Code</Label>
                <Input
                  id="key_code"
                  value={formData.key_code}
                  onChange={(e) => setFormData({ ...formData, key_code: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="tax_percentages">Tax Percentages</Label>
                <Input
                  id="tax_percentages"
                  value={formData.tax_percentages}
                  onChange={(e) => setFormData({ ...formData, tax_percentages: e.target.value })}
                  placeholder="e.g., GST: 18%, Service: 10%"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
              />
              <Label htmlFor="is_active">Active</Label>
            </div>

            {/* Images Section */}
            <SimpleImageManager
              images={images}
              onImagesChange={setImages}
              multiple={true}
            />

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : room?.id ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoomsForm;