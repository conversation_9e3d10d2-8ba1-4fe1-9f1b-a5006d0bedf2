-- Create content management tables for dynamic website content

-- Table for carousel images
CREATE TABLE public.carousel_images (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  display_order INTEGER NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table for "Why Choose" features
CREATE TABLE public.features (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  icon TEXT NOT NULL,
  display_order INTEGER NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table for featured properties
CREATE TABLE public.properties (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT NOT NULL,
  location TEXT NOT NULL,
  price_per_night DECIMAL(10,2),
  rating DECIMAL(2,1) DEFAULT 5.0,
  amenities TEXT[],
  is_featured BOOLEAN NOT NULL DEFAULT false,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.carousel_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.features ENABLE ROW LEVEL SECURITY;  
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access (for website visitors)
CREATE POLICY "Anyone can view active carousel images" 
ON public.carousel_images 
FOR SELECT 
USING (is_active = true);

CREATE POLICY "Anyone can view active features" 
ON public.features 
FOR SELECT 
USING (is_active = true);

CREATE POLICY "Anyone can view active properties" 
ON public.properties 
FOR SELECT 
USING (is_active = true);

-- Create admin policies (we'll use email check for admin access)
CREATE POLICY "Admins can manage carousel images" 
ON public.carousel_images 
FOR ALL
USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admins can manage features" 
ON public.features 
FOR ALL
USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admins can manage properties" 
ON public.properties 
FOR ALL
USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Insert default data
INSERT INTO public.carousel_images (title, description, image_url, display_order) VALUES
('Luxury Accommodation', 'Experience world-class hospitality in our premium rooms', '/src/assets/hotel-hero.jpg', 1),
('Fine Dining', 'Savor exquisite cuisine at our award-winning restaurant', '/src/assets/hotel-hero.jpg', 2),
('Spa & Wellness', 'Rejuvenate your mind and body at our luxury spa', '/src/assets/hotel-hero.jpg', 3);

INSERT INTO public.features (title, description, icon, display_order) VALUES
('24/7 Concierge Service', 'Our dedicated team is available round the clock to assist you', 'Clock', 1),
('Luxury Amenities', 'Enjoy premium facilities including spa, gym, and fine dining', 'Crown', 2),
('Prime Locations', 'Strategically located properties in the heart of major cities', 'MapPin', 3),
('Exceptional Service', 'Personalized service that exceeds expectations every time', 'Star', 4);

INSERT INTO public.properties (name, description, image_url, location, price_per_night, rating, amenities, is_featured) VALUES
('Red Chili Grand Hotel', 'Luxury hotel in the heart of the city with premium amenities', '/src/assets/hotel-hero.jpg', 'Downtown City Center', 299.99, 4.8, ARRAY['Wi-Fi', 'Pool', 'Spa', 'Restaurant', 'Gym'], true),
('Red Chili Resort', 'Beachfront resort perfect for relaxation and recreation', '/src/assets/hotel-hero.jpg', 'Coastal Paradise', 399.99, 4.9, ARRAY['Beach Access', 'Water Sports', 'Kids Club', 'Multiple Restaurants'], true),
('Red Chili Business Hotel', 'Modern business hotel with conference facilities', '/src/assets/hotel-hero.jpg', 'Business District', 199.99, 4.7, ARRAY['Business Center', 'Meeting Rooms', 'Airport Shuttle', 'High-Speed Internet'], true);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_carousel_images_updated_at
  BEFORE UPDATE ON public.carousel_images
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_features_updated_at
  BEFORE UPDATE ON public.features
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_properties_updated_at
  BEFORE UPDATE ON public.properties
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();