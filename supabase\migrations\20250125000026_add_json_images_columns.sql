-- Add JSON image columns to existing tables

-- Add images column to properties_admin table
ALTER TABLE public.properties_admin 
ADD COLUMN images JSONB DEFAULT '[]'::jsonb;

-- Add images column to rooms table  
ALTER TABLE public.rooms 
ADD COLUMN images JSONB DEFAULT '[]'::jsonb;

-- Add single image column to room_types table
ALTER TABLE public.room_types 
ADD COLUMN image_url TEXT;

-- Add comments for new columns
COMMENT ON COLUMN properties_admin.images IS 'JSON array of property image URLs';
COMMENT ON COLUMN rooms.images IS 'JSON array of room image URLs';
COMMENT ON COLUMN room_types.image_url IS 'Single representative image URL for room type';

-- Insert sample image data for existing records
UPDATE public.properties_admin 
SET images = CASE 
    WHEN code = 'MDJ001' THEN '["/p/583A8268 - Copy.JPG", "/p/583A8274 - Copy.JPG", "/p/e1.JPG"]'::jsonb
    WHEN code = 'RCH001' THEN '["/p/e2.jpg"]'::jsonb
    WHEN code = 'CVR001' THEN '["/p/e3.jpg"]'::jsonb
    ELSE '[]'::jsonb
END;

-- Update room_types with sample images
UPDATE public.room_types 
SET image_url = CASE
    WHEN code = 'DLX' THEN '/p/e10.jpg'
    WHEN code = 'STE' THEN '/p/e15.jpg'  
    WHEN code = 'STD' THEN '/p/e11.jpg'
    WHEN code = 'EXE' THEN '/p/e12.jpg'
    WHEN code = 'BVL' THEN '/p/e16.jpg'
    ELSE NULL
END;

-- Update rooms with sample images
UPDATE public.rooms 
SET images = CASE
    WHEN room_number = '101' THEN '["/p/e10.jpg"]'::jsonb
    WHEN room_number = '201' THEN '["/p/e15.jpg", "/p/e16.jpg"]'::jsonb
    ELSE '[]'::jsonb
END;