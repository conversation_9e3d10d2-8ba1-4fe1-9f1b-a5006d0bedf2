-- Create events_showcase table for dynamic events carousel
CREATE TABLE IF NOT EXISTS public.events_showcase (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create RLS policies
ALTER TABLE public.events_showcase ENABLE ROW LEVEL SECURITY;

-- Allow public read access for active events
CREATE POLICY "Allow public read access for active events" ON public.events_showcase
    FOR SELECT USING (is_active = true);

-- Allow authenticated users to manage events
CREATE POLICY "Allow authenticated users to manage events" ON public.events_showcase
    FOR ALL USING (auth.role() = 'authenticated');

-- <PERSON><PERSON> updated_at trigger
CREATE TRIGGER handle_events_showcase_updated_at
    BEFORE UPDATE ON public.events_showcase
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample data
INSERT INTO public.events_showcase (title, description, image_url, category, display_order, is_active) VALUES
('Weddings', 'Grand celebrations tailored to your story.', 'https://images.unsplash.com/photo-1522673607200-164d1b6ce486?q=80&w=1760&auto=format&fit=crop', 'Weddings', 1, true),
('Corporate Events', 'Professional settings for conferences and meets.', 'https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&w=1760&auto=format&fit=crop', 'Corporate', 2, true),
('Parties', 'Birthdays and anniversaries with flair.', 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&w=1760&auto=format&fit=crop', 'Parties', 3, true),
('Exhibitions', 'Versatile layouts for shows and expos.', 'https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&w=1760&auto=format&fit=crop', 'Exhibitions', 4, true);