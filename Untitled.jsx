var MyClass = React.createClass({
  render: function() {
    return (
      <div>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Meadow De Jalsa — Luxury Stay &amp; Events</title>
        <meta name="description" content="Meadow De Jalsa — Luxury stay, premium dining, and memorable events on Bardoli–Vyara Highway." />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin />
        <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;500;600&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet" />
        <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 100 100%27%3E%3Crect width=%27100%27 height=%27100%27 rx=%2718%27 fill=%27%230a1f1f%27/%3E%3Ctext x=%2750%27 y=%2758%27 font-family=%27Georgia%27 font-size=%2750%27 text-anchor=%27middle%27 fill=%27%23fbbf24%27%3EMJ%3C/text%3E%3C/svg%3E" />
        {/* Sticky Utility Bar */}
        <div className="fixed inset-x-0 top-0 z-50">
          <div className="w-full bg-white/80 backdrop-blur border-b border-slate-200">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-2 flex items-center justify-between">
              <div className="flex items-center gap-5 text-[13px]">
                <a href="tel:+918740027008" className="inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
                  <i data-lucide="phone" className="w-4 h-4" />
                  <span>+91 87400-27008</span>
                </a>
                <a href="mailto:<EMAIL>" className="hidden sm:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
                  <i data-lucide="mail" className="w-4 h-4" />
                  <span><EMAIL></span>
                </a>
                <a href="#contact" className="hidden md:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
                  <i data-lucide="map-pin" className="w-4 h-4" />
                  <span>Bardoli–Vyara Highway</span>
                </a>
              </div>
              <div className="flex items-center gap-3">
                <a href="https://instagram.com" target="_blank" rel="noopener" className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Instagram">
                  <i data-lucide="instagram" className="w-4 h-4" />
                </a>
                <a href="https://facebook.com" target="_blank" rel="noopener" className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Facebook">
                  <i data-lucide="facebook" className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>
          {/* Main Nav */}
          <nav className="w-full bg-white/90 backdrop-blur border-b border-slate-200">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
              <a href="#home" className="flex items-center gap-3">
                <div className="h-9 w-9 rounded-lg bg-emerald-950 text-amber-400 grid place-items-center text-sm tracking-tight font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>MJ</div>
                <div className="flex flex-col leading-tight">
                  <span className="text-[15px] sm:text-[16px] text-emerald-950 font-semibold tracking-tight" style={{fontFamily: '"Cormorant Garamond", serif'}}>Meadow De Jalsa</span>
                  <span className="text-[11px] text-slate-500">Luxury Stay &amp; Events</span>
                </div>
              </a>
              <button id="navToggle" className="lg:hidden inline-flex items-center justify-center rounded-md p-2 hover:bg-slate-100 text-slate-700 hover:text-emerald-900 transition" aria-label="Toggle navigation">
                <i data-lucide="menu" className="w-5 h-5" />
              </button>
              <div id="navMenu" className="hidden lg:flex items-center gap-1">
                <a href="#home" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Home</a>
                <a href="#about" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">About</a>
                <a href="#rooms" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Rooms</a>
                <a href="#restaurant" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Restaurant</a>
                <a href="#events" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Events</a>
                <a href="#gallery" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Gallery</a>
                <a href="#contact" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Contact</a>
                <button id="bookNowTop" className="ml-2 inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium tracking-tight shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-900/30">
                  <i data-lucide="calendar" className="w-4 h-4" />
                  Book Now
                </button>
              </div>
            </div>
            {/* Mobile Menu */}
            <div id="mobileMenu" className="hidden lg:hidden border-t border-slate-200 bg-white">
              <div className="px-4 py-3 flex flex-col gap-1">
                <a href="#home" className="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Home</a>
                <a href="#about" className="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">About</a>
                <a href="#rooms" className="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Rooms</a>
                <a href="#restaurant" className="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Restaurant</a>
                <a href="#events" className="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Events</a>
                <a href="#gallery" className="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Gallery</a>
                <a href="#contact" className="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Contact</a>
                <button id="bookNowTopMobile" className="mt-2 inline-flex items-center justify-center gap-2 rounded-full px-4 py-2 text-[15px] text-emerald-950 font-medium tracking-tight shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                  <i data-lucide="calendar" className="w-4 h-4" />
                  Book Now
                </button>
              </div>
            </div>
          </nav>
        </div>
        <main className="pt-[128px]">
          {/* HOME */}
          <section id="home" className="relative">
            {/* Hero */}
            <div className="relative h-[72vh] sm:h-[80vh] w-full overflow-hidden">
              <video className="absolute inset-0 w-full h-full object-cover" src="https://videos.pexels.com/video-files/856130/856130-hd_1920_1080_25fps.mp4" autoPlay loop muted playsInline poster="https://images.unsplash.com/photo-1501117716987-c8e2a3a67a0b?q=80&w=1760&auto=format&fit=crop" aria-label="Aerial view of property" />
              <div className="absolute inset-0 bg-gradient-to-b from-emerald-950/60 via-emerald-950/40 to-emerald-950/60" />
              <div className="relative z-10 h-full">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full grid">
                  <div className="m-auto text-center">
                    <div className="inline-flex items-center gap-3 px-3 py-1.5 rounded-full bg-white/10 backdrop-blur ring-1 ring-white/20 mb-5">
                      <i data-lucide="map-pin" className="w-4 h-4 text-amber-300" />
                      <span className="text-white/90 text-[12px] sm:text-[13px]">Bardoli–Vyara Highway</span>
                    </div>
                    <h1 className="text-white tracking-tight text-4xl sm:text-5xl lg:text-6xl font-semibold leading-[1.1]" style={{fontFamily: '"Cormorant Garamond", serif'}}>
                      Where Luxury Meets Tradition
                    </h1>
                    <p className="mt-4 text-white/85 text-[15px] sm:text-[16px] max-w-2xl mx-auto">
                      Experience elegant stays, gourmet dining, and unforgettable celebrations at Meadow De Jalsa.
                    </p>
                    <div className="mt-8 flex items-center justify-center gap-3">
                      <button className="group inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/50" id="bookNowHero">
                        <i data-lucide="calendar-range" className="w-5 h-5" />
                        Book Your Stay
                      </button>
                      <a href="#events" className="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white/90 ring-1 ring-white/30 hover:bg-white/10 transition">
                        <i data-lucide="sparkles" className="w-5 h-5" />
                        Plan Your Event
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Quick Info Strip */}
            <div className="bg-white border-y border-slate-200">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-5 grid grid-cols-2 sm:grid-cols-4 gap-3">
                <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="key-round" className="w-5 h-5" />
                  </div>
                  <div>
                    <div className="text-[12px] text-slate-500">Check-in</div>
                    <div className="text-sm font-medium">2:00 PM</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="clock-3" className="w-5 h-5" />
                  </div>
                  <div>
                    <div className="text-[12px] text-slate-500">Restaurant</div>
                    <div className="text-sm font-medium">11 AM – 11 PM</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="wifi" className="w-5 h-5" />
                  </div>
                  <div>
                    <div className="text-[12px] text-slate-500">Connectivity</div>
                    <div className="text-sm font-medium">High-speed WiFi</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="car" className="w-5 h-5" />
                  </div>
                  <div>
                    <div className="text-[12px] text-slate-500">Parking</div>
                    <div className="text-sm font-medium">Ample &amp; Secure</div>
                  </div>
                </div>
              </div>
            </div>
            {/* About Snapshot */}
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
              <div className="text-center">
                <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>
                  Luxury Stay &amp; Memorable Events on Bardoli–Vyara Highway
                </h2>
                <p className="mt-3 text-slate-600 max-w-2xl mx-auto">
                  A modern retreat offering refined rooms, curated dining, and bespoke event experiences.
                </p>
              </div>
            </div>
            {/* Highlights */}
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-14">
              <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Stay */}
                <a href="#rooms" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1760&auto=format&fit=crop" alt="Rooms & Suites" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                  <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent" />
                  <div className="absolute inset-x-0 bottom-0 p-4">
                    <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                      <i data-lucide="building-2" className="w-4 h-4 text-emerald-900" />
                      <span className="text-[13px] font-medium text-emerald-950">Stay</span>
                    </div>
                    <p className="mt-3 text-white/90 text-sm">Rooms &amp; suites designed for comfort and elegance.</p>
                  </div>
                </a>
                {/* Dine */}
                <a href="#restaurant" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=1760&auto=format&fit=crop" alt="Restaurant" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                  <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent" />
                  <div className="absolute inset-x-0 bottom-0 p-4">
                    <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                      <i data-lucide="utensils" className="w-4 h-4 text-emerald-900" />
                      <span className="text-[13px] font-medium text-emerald-950">Dine</span>
                    </div>
                    <p className="mt-3 text-white/90 text-sm">Taste luxury, taste tradition with curated cuisine.</p>
                  </div>
                </a>
                {/* Celebrate */}
                <a href="#events" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&w=1760&auto=format&fit=crop" alt="Events & Banquets" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                  <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent" />
                  <div className="absolute inset-x-0 bottom-0 p-4">
                    <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                      <i data-lucide="party-popper" className="w-4 h-4 text-emerald-900" />
                      <span className="text-[13px] font-medium text-emerald-950">Celebrate</span>
                    </div>
                    <p className="mt-3 text-white/90 text-sm">Weddings, corporate events, parties, and more.</p>
                  </div>
                </a>
                {/* Explore */}
                <a href="#about" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80" alt="Nearby Attractions" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                  <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent" />
                  <div className="absolute inset-x-0 bottom-0 p-4">
                    <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                      <i data-lucide="map" className="w-4 h-4 text-emerald-900" />
                      <span className="text-[13px] font-medium text-emerald-950">Explore</span>
                    </div>
                    <p className="mt-3 text-white/90 text-sm">Discover the best of Bardoli &amp; Vyara nearby.</p>
                  </div>
                </a>
              </div>
            </div>
            {/* Events Showcase Carousel */}
            <div className="bg-emerald-950">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-white tracking-tight text-2xl sm:text-3xl font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Events Showcase</h3>
                  <div className="flex items-center gap-2">
                    <button id="eventsPrev" className="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Previous">
                      <i data-lucide="chevron-left" className="w-5 h-5" />
                    </button>
                    <button id="eventsNext" className="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Next">
                      <i data-lucide="chevron-right" className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                <div className="relative">
                  <div id="eventsTrack" className="flex gap-4 overflow-hidden scroll-smooth">
                    {/* Slides */}
                    <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                      <img src="https://images.unsplash.com/photo-1522673607200-164d1b6ce486?q=80&w=1760&auto=format&fit=crop" alt="Weddings" className="h-44 w-full object-cover" />
                      <div className="p-4">
                        <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Weddings</div>
                        <p className="mt-3 text-sm text-slate-600">Grand celebrations tailored to your story.</p>
                      </div>
                    </div>
                    <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                      <img src="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&w=1760&auto=format&fit=crop" alt="Corporate Events" className="h-44 w-full object-cover" />
                      <div className="p-4">
                        <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Corporate</div>
                        <p className="mt-3 text-sm text-slate-600">Professional settings for conferences and meets.</p>
                      </div>
                    </div>
                    <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                      <img src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&w=1760&auto=format&fit=crop" alt="Parties" className="h-44 w-full object-cover" />
                      <div className="p-4">
                        <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Parties</div>
                        <p className="mt-3 text-sm text-slate-600">Birthdays and anniversaries with flair.</p>
                      </div>
                    </div>
                    <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                      <img src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&w=1760&auto=format&fit=crop" alt="Exhibitions" className="h-44 w-full object-cover" />
                      <div className="p-4">
                        <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Exhibitions</div>
                        <p className="mt-3 text-sm text-slate-600">Versatile layouts for shows and expos.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Social Live Feed */}
            <div className="bg-white">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl tracking-tight text-emerald-950 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Live Feed</h3>
                  <div className="flex items-center gap-3">
                    <a href="https://instagram.com" target="_blank" className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                      <i data-lucide="instagram" className="w-4 h-4 text-emerald-900" />
                      <span className="text-[13px]">Instagram</span>
                    </a>
                    <a href="https://facebook.com" target="_blank" className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                      <i data-lucide="facebook" className="w-4 h-4 text-emerald-900" />
                      <span className="text-[13px]">Facebook</span>
                    </a>
                  </div>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
                  {/* 6 feed items */}
                  <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&q=80" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                    <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition" />
                  </a>
                  <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&w=1200&auto=format&fit=crop" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                    <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition" />
                  </a>
                  <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&q=80" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                    <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition" />
                  </a>
                  <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <img src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?q=80&w=1200&auto=format&fit=crop" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                    <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition" />
                  </a>
                  <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                    <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition" />
                  </a>
                  <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&w=1200&auto=format&fit=crop" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                    <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition" />
                  </a>
                </div>
              </div>
            </div>
            {/* CTA */}
            <div className="bg-[url('https://images.unsplash.com/photo-1518277850984-94e89c892b26?q=80&w=1760&auto=format&fit=crop')] bg-cover bg-center bg-fixed">
              <div className="bg-emerald-950/80">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
                  <h3 className="text-white tracking-tight text-3xl sm:text-4xl font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Ready to make memories?</h3>
                  <p className="mt-3 text-white/85">Reserve your stay or plan a bespoke event with our expert team.</p>
                  <div className="mt-7 flex items-center justify-center gap-3">
                    <button id="bookNowCTA" className="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                      <i data-lucide="calendar" className="w-5 h-5" />
                      Book Now
                    </button>
                    <a href="#contact" className="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                      <i data-lucide="messages-square" className="w-5 h-5" />
                      Talk to Us
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </section>
          {/* ABOUT */}
          <section id="about" className="bg-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
              <div className="grid lg:grid-cols-2 gap-10 items-center">
                <div>
                  <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold mb-3" style={{fontFamily: '"Cormorant Garamond", serif'}}>Meadow De Jalsa — where luxury meets tradition.</h2>
                  <p className="text-slate-600">
                    From a humble vision to a captivating destination, our story is woven with heartfelt hospitality, contemporary design, and timeless values.
                  </p>
                  {/* Timeline */}
                  <div className="mt-8 space-y-6">
                    <div className="flex gap-4">
                      <div className="flex flex-col items-center">
                        <div className="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200" />
                        <div className="flex-1 w-px bg-slate-200" />
                      </div>
                      <div>
                        <div className="text-sm text-slate-500">Est. 2016</div>
                        <div className="text-[18px] font-medium text-emerald-950">Foundations</div>
                        <p className="text-sm text-slate-600">The idea of a luxury stopover on the Bardoli–Vyara highway comes alive.</p>
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <div className="flex flex-col items-center">
                        <div className="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200" />
                        <div className="flex-1 w-px bg-slate-200" />
                      </div>
                      <div>
                        <div className="text-sm text-slate-500">2018–2021</div>
                        <div className="text-[18px] font-medium text-emerald-950">Growth</div>
                        <p className="text-sm text-slate-600">Rooms, banquet spaces, and a signature restaurant take shape.</p>
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <div className="flex flex-col items-center">
                        <div className="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200" />
                        <div className="flex-1 w-px bg-slate-200" />
                      </div>
                      <div>
                        <div className="text-sm text-slate-500">Today</div>
                        <div className="text-[18px] font-medium text-emerald-950">Destination</div>
                        <p className="text-sm text-slate-600">A preferred venue for luxury stays and memorable events.</p>
                      </div>
                    </div>
                  </div>
                  {/* Highlights */}
                  <div className="mt-8 grid sm:grid-cols-3 gap-3">
                    <div className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                      <div className="flex items-center gap-2 text-emerald-950">
                        <i data-lucide="leaf" className="w-4 h-4" />
                        <span className="text-sm font-medium">Eco-friendly</span>
                      </div>
                      <p className="text-[13px] text-slate-600 mt-1.5">Energy-efficient lighting, water-saving fixtures, greenery.</p>
                    </div>
                    <div className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                      <div className="flex items-center gap-2 text-emerald-950">
                        <i data-lucide="route" className="w-4 h-4" />
                        <span className="text-sm font-medium">Highway Location</span>
                      </div>
                      <p className="text-[13px] text-slate-600 mt-1.5">Seamless access and ample secure parking.</p>
                    </div>
                    <div className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                      <div className="flex items-center gap-2 text-emerald-950">
                        <i data-lucide="medal" className="w-4 h-4" />
                        <span className="text-sm font-medium">Premium Hospitality</span>
                      </div>
                      <p className="text-[13px] text-slate-600 mt-1.5">Attentive service and bespoke experiences.</p>
                    </div>
                  </div>
                </div>
                {/* Gallery Masonry */}
                <div className="columns-2 gap-3">
                  <img src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&w=1600&auto=format&fit=crop" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Lobby" />
                  <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&q=80" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Banquet" />
                  <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&w=1600&auto=format&fit=crop" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Dining" />
                  <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1600&auto=format&fit=crop" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Room" />
                </div>
              </div>
              {/* Mission & Vision */}
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                    <h4 className="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style={{fontFamily: '"Cormorant Garamond", serif'}}>Our Mission</h4>
                    <p className="text-slate-600 text-[15px]">To craft refined stays and memorable events through attentive service, culinary excellence, and thoughtful design.</p>
                  </div>
                  <div className="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                    <h4 className="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style={{fontFamily: '"Cormorant Garamond", serif'}}>Our Vision</h4>
                    <p className="text-slate-600 text-[15px]">To be the region’s most loved destination for celebrations and serene getaways.</p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          {/* ROOMS & SUITES */}
          <section id="rooms" className="bg-slate-50">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
              <div className="flex items-end justify-between gap-4">
                <div>
                  <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Rooms &amp; Suites</h2>
                  <p className="text-slate-600 mt-1">Choose your perfect stay with immersive visuals and amenities.</p>
                </div>
                <div className="flex items-center gap-2">
                  <button data-filter="all" className="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">All</button>
                  <button data-filter="Standard" className="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Standard</button>
                  <button data-filter="Deluxe" className="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Deluxe</button>
                  <button data-filter="Suite" className="room-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Suite</button>
                </div>
              </div>
              <div id="roomsGrid" className="mt-8 grid md:grid-cols-2 lg:grid-cols-3 gap-5">
                {/* Room Card */}
                <div className="room-card" data-category="Standard">
                  <div className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    {/* Slider */}
                    <div className="relative">
                      <div className="overflow-hidden">
                        <div className="flex transition-transform duration-500" data-slider={1} data-index={0}>
                          <img src="https://images.unsplash.com/photo-1562790351-d273a961e0e9?q=80&w=1600&auto=format&fit=crop" className="h-48 w-full object-cover flex-shrink-0" alt="Standard Room 1" />
                          <img src="https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=1600&auto=format&fit=crop" className="h-48 w-full object-cover flex-shrink-0" alt="Standard Room 2" />
                          <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&q=80" className="h-48 w-full object-cover flex-shrink-0" alt="Standard Room 3" />
                        </div>
                      </div>
                      <button className="abs-btn left-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover              :bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-prev={1} aria-label="Previous image">
                        <i data-lucide="chevron-left" className="w-4 h-4" />
                      </button>
                      <button className="abs-btn right-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-next={1} aria-label="Next image">
                        <i data-lucide="chevron-right" className="w-4 h-4" />
                      </button>
                      <div className="absolute inset-x-0 bottom-2 flex items-center justify-center gap-1.5">
                        <button className="h-1.5 w-4 rounded-full bg-white/70 data-[active=true]:bg-amber-400 transition" data-dot={1} data-to={0} aria-label="Slide 1" />
                        <button className="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot={1} data-to1" aria-label="Slide 2" />
                        <button className="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot={1} data-to={2} aria-label="Slide 3" />
                      </div>
                    </div>
                    {/* Content */}
                    <div className="p-4">
                      <div className="flex items-start justify-between gap-3">
                        <div>
                          <h3 className="text-[18px] font-semibold text-emerald-950">Standard Room</h3>
                          <p className="text-[13px] text-slate-500">Cozy comfort, perfect for solo travelers or couples.</p>
                        </div>
                        <div className="text-right">
                          <div className="text-[11px] text-slate-500">From</div>
                          <div className="text-[15px] font-semibold text-emerald-900">₹2,999</div>
                        </div>
                      </div>
                      <div className="mt-3 flex flex-wrap items-center gap-2 text-[12px] text-slate-600">
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="wifi" className="w-3.5 h-3.5" /> WiFi</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="snowflake" className="w-3.5 h-3.5" /> AC</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="tv-2" className="w-3.5 h-3.5" /> Smart TV</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="cup-soda" className="w-3.5 h-3.5" /> Minibar</span>
                      </div>
                      <div className="mt-4 flex items-center justify-between">
                        <button className="inline-flex items-center gap-2 text-[14px] text-emerald-900 hover:text-emerald-700 transition" data-open="bookingModal" data-room="Standard Room">
                          <i data-lucide="calendar" className="w-4 h-4" />
                          Book
                        </button>
                        <a href="#gallery" className="inline-flex items-center gap-2 text-[14px] text-slate-700 hover:text-emerald-900 transition">
                          <i data-lucide="image" className="w-4 h-4" />
                          Gallery
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Room Card */}
                <div className="room-card" data-category="Deluxe">
                  <div className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    {/* Slider */}
                    <div className="relative">
                      <div className="overflow-hidden">
                        <div className="flex transition-transform duration-500" data-slider={2} data-index={0}>
                          <img src="https://images.unsplash.com/photo-1554995207-c18c203602cb?q=80&w=1600&auto=format&fit=crop" className="h-48 w-full object-cover flex-shrink-0" alt="Deluxe Room 1" />
                          <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80" className="h-48 w-full object-cover flex-shrink-0" alt="Deluxe Room 2" />
                          <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&q=80" className="h-48 w-full object-cover flex-shrink-0" alt="Deluxe Room 3" />
                        </div>
                      </div>
                      <button className="abs-btn left-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-prev={2} aria-label="Previous image">
                        <i data-lucide="chevron-left" className="w-4 h-4" />
                      </button>
                      <button className="abs-btn right-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-next={2} aria-label="Next image">
                        <i data-lucide="chevron-right" className="w-4 h-4" />
                      </button>
                      <div className="absolute inset-x-0 bottom-2 flex items-center justify-center gap-1.5">
                        <button className="h-1.5 w-4 rounded-full bg-white/70 data-[active=true]:bg-amber-400 transition" data-dot={2} data-to={0} aria-label="Slide 1" />
                        <button className="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot={2} data-to={1} aria-label="Slide 2" />
                        <button className="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot={2} data-to={2} aria-label="Slide 3" />
                      </div>
                    </div>
                    {/* Content */}
                    <div className="p-4">
                      <div className="flex items-start justify-between gap-3">
                        <div>
                          <h3 className="text-[18px] font-semibold text-emerald-950">Deluxe Room</h3>
                          <p className="text-[13px] text-slate-500">Spacious elegance with premium amenities.</p>
                        </div>
                        <div className="text-right">
                          <div className="text-[11px] text-slate-500">From</div>
                          <div className="text-[15px] font-semibold text-emerald-900">₹4,499</div>
                        </div>
                      </div>
                      <div className="mt-3 flex flex-wrap items-center gap-2 text-[12px] text-slate-600">
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="wifi" className="w-3.5 h-3.5" /> WiFi 200 Mbps</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="coffee" className="w-3.5 h-3.5" /> Tea/Coffee</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="bed-double" className="w-3.5 h-3.5" /> King Bed</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="shower-head" className="w-3.5 h-3.5" /> Rain Shower</span>
                      </div>
                      <div className="mt-4 flex items-center justify-between">
                        <button className="inline-flex items-center gap-2 text-[14px] text-emerald-900 hover:text-emerald-700 transition" data-open="bookingModal" data-room="Deluxe Room">
                          <i data-lucide="calendar" className="w-4 h-4" />
                          Book
                        </button>
                        <a href="#gallery" className="inline-flex items-center gap-2 text-[14px] text-slate-700 hover:text-emerald-900 transition">
                          <i data-lucide="image" className="w-4 h-4" />
                          Gallery
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Room Card */}
                <div className="room-card" data-category="Suite">
                  <div className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    {/* Slider */}
                    <div className="relative">
                      <div className="overflow-hidden">
                        <div className="flex transition-transform duration-500" data-slider={3} data-index={0}>
                          <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&q=80" className="h-48 w-full object-cover flex-shrink-0" alt="Suite 1" />
                          <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80" className="h-48 w-full object-cover flex-shrink-0" alt="Suite 2" />
                          <img src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?q=80&w=1600&auto=format&fit=crop" className="h-48 w-full object-cover flex-shrink-0" alt="Suite 3" />
                        </div>
                      </div>
                      <button className="abs-btn left-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-prev={3} aria-label="Previous image">
                        <i data-lucide="chevron-left" className="w-4 h-4" />
                      </button>
                      <button className="abs-btn right-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition absolute" data-next={3} aria-label="Next image">
                        <i data-lucide="chevron-right" className="w-4 h-4" />
                      </button>
                      <div className="absolute inset-x-0 bottom-2 flex items-center justify-center gap-1.5">
                        <button className="h-1.5 w-4 rounded-full bg-white/70 data-[active=true]:bg-amber-400 transition" data-dot={3} data-to={0} aria-label="Slide 1" />
                        <button className="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot={3} data-to={1} aria-label="Slide 2" />
                        <button className="h-1.5 w-1.5 rounded-full bg-white/50 data-[active=true]:bg-amber-400 transition" data-dot={3} data-to={2} aria-label="Slide 3" />
                      </div>
                    </div>
                    {/* Content */}
                    <div className="p-4">
                      <div className="flex items-start justify-between gap-3">
                        <div>
                          <h3 className="text-[18px] font-semibold text-emerald-950">Luxury Suite</h3>
                          <p className="text-[13px] text-slate-500">Separate living, curated interiors, premium comforts.</p>
                        </div>
                        <div className="text-right">
                          <div className="text-[11px] text-slate-500">From</div>
                          <div className="text-[15px] font-semibold text-emerald-900">₹6,999</div>
                        </div>
                      </div>
                      <div className="mt-3 flex flex-wrap items-center gap-2 text-[12px] text-slate-600">
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="concierge-bell" className="w-3.5 h-3.5" /> Butler on-call</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="sofa" className="w-3.5 h-3.5" /> Lounge</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="sparkles" className="w-3.5 h-3.5" /> Luxury Linen</span>
                        <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><i data-lucide="bath" className="w-3.5 h-3.5" /> Soaking Tub</span>
                      </div>
                      <div className="mt-4 flex items-center justify-between">
                        <button className="inline-flex items-center gap-2 text-[14px] text-emerald-900 hover:text-emerald-700 transition" data-open="bookingModal" data-room="Luxury Suite">
                          <i data-lucide="calendar" className="w-4 h-4" />
                          Book
                        </button>
                        <a href="#gallery" className="inline-flex items-center gap-2 text-[14px] text-slate-700 hover:text-emerald-900 transition">
                          <i data-lucide="image" className="w-4 h-4" />
                          Gallery
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Amenities CTA */}
              <div className="mt-10 p-5 rounded-2xl bg-gradient-to-br from-emerald-50 to-amber-50 ring-1 ring-emerald-100/60">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-lg bg-emerald-900 text-amber-300 grid place-items-center">
                      <i data-lucide="check-circle-2" className="w-5 h-5" />
                    </div>
                    <p className="text-slate-700 text-[15px]">All stays include complimentary breakfast, free WiFi, and secure parking.</p>
                  </div>
                  <button className="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500" data-open="bookingModal">
                    <i data-lucide="calendar-range" className="w-4 h-4" />
                    Check Availability
                  </button>
                </div>
              </div>
            </div>
          </section>
          {/* RESTAURANT */}
          <section id="restaurant" className="bg-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
              <div className="grid lg:grid-cols-2 gap-10 items-center">
                <div className="space-y-4">
                  <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Signature Restaurant</h2>
                  <p className="text-slate-600">Savor a blend of Indian classics and global favorites in a refined setting. Fresh ingredients, handcrafted spices, and chef-led presentations.</p>
                  <div className="grid sm:grid-cols-2 gap-3">
                    <div className="p-4 rounded-xl ring-1 ring-slate-200">
                      <div className="flex items-center gap-2 text-emerald-950">
                        <i data-lucide="chef-hat" className="w-4 h-4" />
                        <span className="text-sm font-medium">Chef’s Specials</span>
                      </div>
                      <p className="text-[13px] text-slate-600 mt-1.5">Seasonal menus, farm-fresh produce.</p>
                    </div>
                    <div className="p-4 rounded-xl ring-1 ring-slate-200">
                      <div className="flex items-center gap-2 text-emerald-950">
                        <i data-lucide="cocktail" className="w-4 h-4" />
                        <span className="text-sm font-medium">Mocktails &amp; Brews</span>
                      </div>
                      <p className="text-[13px] text-slate-600 mt-1.5">Creative sips crafted at the bar.</p>
                    </div>
                    <div className="p-4 rounded-xl ring-1 ring-slate-200">
                      <div className="flex items-center gap-2 text-emerald-950">
                        <i data-lucide="leaf" className="w-4 h-4" />
                        <span className="text-sm font-medium">Veg-forward</span>
                      </div>
                      <p className="text-[13px] text-slate-600 mt-1.5">Plenty of vegetarian choices.</p>
                    </div>
                    <div className="p-4 rounded-xl ring-1 ring-slate-200">
                      <div className="flex items-center gap-2 text-emerald-950">
                        <i data-lucide="clock-3" className="w-4 h-4" />
                        <span className="text-sm font-medium">11 AM – 11 PM</span>
                      </div>
                      <p className="text-[13px] text-slate-600 mt-1.5">Lunch, hi-tea, and dinner daily.</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 pt-2">
                    <button className="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500" data-open="enquiryModal" data-type="Table Reservation">
                      <i data-lucide="calendar-check" className="w-4 h-4" />
                      Reserve a Table
                    </button>
                    <a href="#gallery" className="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-900 ring-1 ring-slate-200 hover:bg-slate-50 transition">
                      <i data-lucide="image" className="w-4 h-4" />
                      View Ambience
                    </a>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <img src="https://images.unsplash.com/photo-1473093226795-af9932fe5856?q=80&w=1600&auto=format&fit=crop" className="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Dish 1" />
                  <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&q=80" className="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Dish 2" />
                  <img src="https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=1600&auto=format&fit=crop" className="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Dining Room" />
                  <img src="https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1600&auto=format&fit=crop" className="h-40 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200" alt="Bar Counter" />
                </div>
              </div>
            </div>
          </section>
          {/* EVENTS */}
          <section id="events" className="bg-slate-50">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
              <div className="grid lg:grid-cols-3 gap-8 items-start">
                <div className="lg:col-span-2">
                  <img src="https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?q=80&w=1760&auto=format&fit=crop" alt="Banquet Hall" className="w-full h-64 sm:h-96 object-cover rounded-2xl ring-1 ring-slate-200" />
                </div>
                <div className="space-y-4">
                  <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Events &amp; Banquets</h2>
                  <p className="text-slate-600">From intimate gatherings to grand weddings, our versatile venues, expert planners, and curated menus ensure unforgettable celebrations.</p>
                  <ul className="space-y-2 text-[14px] text-slate-700">
                    <li className="flex items-center gap-2"><i data-lucide="check" className="w-4 h-4 text-emerald-700" /> Grand ballroom up to 600 guests</li>
                    <li className="flex items-center gap-2"><i data-lucide="check" className="w-4 h-4 text-emerald-700" /> Lawn space for outdoor ceremonies</li>
                    <li className="flex items-center gap-2"><i data-lucide="check" className="w-4 h-4 text-emerald-700" /> Themed decor and lighting packages</li>
                    <li className="flex items-center gap-2"><i data-lucide="check" className="w-4 h-4 text-emerald-700" /> Dedicated bridal and green rooms</li>
                  </ul>
                  <div className="pt-2">
                    <button className="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500" data-open="enquiryModal" data-type="Event Enquiry">
                      <i data-lucide="messages-square" className="w-4 h-4" />
                      Get a Quote
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </section>
          {/* GALLERY */}
          <section id="gallery" className="bg-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
              <div className="flex items-end justify-between gap-4">
                <div>
                  <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Gallery</h2>
                  <p className="text-slate-600 mt-1">Explore moments from stays, dining, and celebrations.</p>
                </div>
                <div className="flex items-center gap-2">
                  <button className="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="all">All</button>
                  <button className="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="rooms">Rooms</button>
                  <button className="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="dining">Dining</button>
                  <button className="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events">Events</button>
                  <button className="gallery-filter px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="outdoor">Outdoors</button>
                </div>
              </div>
              <div id="galleryGrid" className="mt-8 columns-2 md:columns-3 lg:columns-4 gap-3 [column-fill:_balance]">
                {/* Items */}
                <a href="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="rooms" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1200&auto=format&fit=crop" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Room Interior" />
                </a>
                <a href="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="dining" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&w=1200&auto=format&fit=crop" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Dining Table" />
                </a>
                <a href="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&w=1200&auto=format&fit=crop" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Wedding Decor" />
                </a>
                <a href="https://images.unsplash.com/photo-1549877452-9c387954fbc6?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="outdoor" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&q=80" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Outdoors" />
                </a>
                <a href="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="dining" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=1200&auto=format&fit=crop" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Restaurant Interior" />
                </a>
                <a href="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&w=1200&auto=format&fit=crop" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Exhibition" />
                </a>
                <a href="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="rooms" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&w=1200&auto=format&fit=crop" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Lobby" />
                </a>
                <a href="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&w=2000&auto=format&fit=crop" className="group mb-3 block break-inside-avoid rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition" data-group="events" data-lightbox>
                  <img src="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&w=1200&auto=format&fit=crop" className="w-full object-cover group-hover:scale-[1.03] transition" alt="Corporate" />
                </a>
              </div>
            </div>
          </section>
          {/* CONTACT */}
          <section id="contact" className="bg-slate-50">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
              <div className="grid lg:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>Contact Us</h2>
                  <p className="text-slate-600">We’d love to host you. Reach out for bookings, events, or dining reservations.</p>
                  <div className="grid sm:grid-cols-2 gap-3">
                    <a href="tel:+918740027008" className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition flex items-start gap-3">
                      <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                        <i data-lucide="phone" className="w-4 h-4" />
                      </div>
                      <div>
                        <div className="text-[12px] text-slate-500">Call</div>
                        <div className="text-[15px] font-medium text-emerald-950">+91 87400-27008</div>
                      </div>
                    </a>
                    <a href="mailto:<EMAIL>" className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition flex items-start gap-3">
                      <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                        <i data-lucide="mail" className="w-4 h-4" />
                      </div>
                      <div>
                        <div className="text-[12px] text-slate-500">Email</div>
                        <div className="text-[15px] font-medium text-emerald-950"><EMAIL></div>
                      </div>
                    </a>
                    <div className="p-4 rounded-xl ring-1 ring-slate-200 flex items-start gap-3">
                      <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                        <i data-lucide="map-pin" className="w-4 h-4" />
                      </div>
                      <div>
                        <div className="text-[12px] text-slate-500">Address</div>
                        <div className="text-[15px] font-medium text-emerald-950">Bardoli–Vyara Highway, Bardoli</div>
                      </div>
                    </div>
                    <div className="p-4 rounded-xl ring-1 ring-slate-200 flex items-start gap-3">
                      <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                        <i data-lucide="clock-4" className="w-4 h-4" />
                      </div>
                      <div>
                        <div className="text-[12px] text-slate-500">Timings</div>
                        <div className="text-[15px] font-medium text-emerald-950">Hotel: 24x7 • Restaurant: 11 AM – 11 PM</div>
                      </div>
                    </div>
                  </div>
                  <div className="rounded-2xl overflow-hidden ring-1 ring-slate-200">
                    <iframe title="Map to Meadow De Jalsa" src="https://maps.google.com/maps?q=Bardoli-Vyara%20Highway&t=&z=12&ie=UTF8&iwloc=&output=embed" className="w-full h-64 border-0" />
                  </div>
                </div>
                <form id="contactForm" className="bg-white p-6 rounded-2xl ring-1 ring-slate-200">
                  <h3 className="text-xl font-semibold text-emerald-950">Send us a message</h3>
                  <div className="mt-4 grid sm:grid-cols-2 gap-3">
                    <div>
                      <label className="text-[12px] text-slate-500">Full Name</label>
                      <input type="text" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Your name" />
                    </div>
                    <div>
                      <label className="text-[12px] text-slate-500">Phone</label>
                      <input type="tel" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder={+91} />
                    </div>
                    <div className="sm:col-span-2">
                      <label className="text-[12px] text-slate-500">Email</label>
                      <input type="email" className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="<EMAIL>" />
                    </div>
                    <div className="sm:col-span-2">
                      <label className="text-[12px] text-slate-500">Message</label>
                      <textarea rows={4} required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Tell us about your plan..." defaultValue={""} />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <label className="inline-flex items-center gap-2 text-[12px] text-slate-600">
                      <input type="checkbox" className="rounded border-slate-300 text-emerald-700 focus:ring-emerald-300" required />
                      I agree to be contacted.
                    </label>
                    <button type="submit" className="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                      <i data-lucide="send" className="w-4 h-4" />
                      Send
                    </button>
                  </div>
                  <p id="contactSuccess" className="mt-3 hidden text-[13px] text-emerald-700">Thanks! We’ll get back to you shortly.</p>
                </form>
              </div>
            </div>
          </section>
        </main>
        {/* Footer */}
        <footer className="bg-emerald-950 text-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-10">
            <div className="grid md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center gap-3">
                  <div className="h-9 w-9 rounded-lg bg-white/10 grid place-items-center text-amber-300 font-semibold" style={{fontFamily: '"Cormorant Garamond", serif'}}>MJ</div>
                  <div className="leading-tight">
                    <div className="text-lg font-semibold">Meadow De Jalsa</div>
                    <div className="text-[12px] text-emerald-200/80">Luxury Stay &amp; Events</div>
                  </div>
                </div>
                <p className="mt-3 text-sm text-emerald-100/80">Elegant stays, curated dining, and memorable events on the Bardoli–Vyara Highway.</p>
                <div className="mt-4 flex items-center gap-3">
                  <a href="https://instagram.com" className="p-2 rounded-md bg-white/10 hover:bg-white/15"><i data-lucide="instagram" className="w-4 h-4" /></a>
                  <a href="https://facebook.com" className="p-2 rounded-md bg-white/10 hover:bg-white/15"><i data-lucide="facebook" className="w-4 h-4" /></a>
                </div>
              </div>
              <div>
                <div className="text-sm font-semibold mb-3">Explore</div>
                <ul className="space-y-2 text-[14px] text-emerald-100/80">
                  <li><a href="#home" className="hover:text-white">Home</a></li>
                  <li><a href="#about" className="hover:text-white">About</a></li>
                  <li><a href="#rooms" className="hover:text-white">Rooms</a></li>
                  <li><a href="#restaurant" className="hover:text-white">Restaurant</a></li>
                  <li><a href="#events" className="hover:text-white">Events</a></li>
                  <li><a href="#gallery" className="hover:text-white">Gallery</a></li>
                  <li><a href="#contact" className="hover:text-white">Contact</a></li>
                </ul>
              </div>
              <div>
                <div className="text-sm font-semibold mb-3">Policies</div>
                <ul className="space-y-2 text-[14px] text-emerald-100/80">
                  <li>Check-in: 2 PM • Check-out: 11 AM</li>
                  <li>Non-smoking rooms</li>
                  <li>Pets not allowed</li>
                  <li>Free cancellation up to 48 hrs</li>
                </ul>
              </div>
              <div>
                <div className="text-sm font-semibold mb-3">Newsletter</div>
                <form id="newsletterForm" className="flex gap-2">
                  <input type="email" required placeholder="Your email" className="w-full rounded-lg bg-white/10 placeholder:text-emerald-100/60 text-white px-3 py-2 ring-1 ring-white/10 focus:outline-none focus:ring-white/30" />
                  <button className="rounded-lg px-3 py-2 bg-amber-400 text-emerald-950 text-sm font-medium hover:bg-amber-300">Join</button>
                </form>
                <p id="newsletterSuccess" className="mt-2 hidden text-[13px] text-emerald-200">Subscribed! Welcome aboard.</p>
              </div>
            </div>
            <div className="mt-8 pt-6 border-t border-white/10 text-[12px] text-emerald-100/70 flex items-center justify-between">
              <span>© <span id="year" /> Meadow De Jalsa. All rights reserved.</span>
              <a href="#home" className="inline-flex items-center gap-2 hover:text-white"><i data-lucide="arrow-up" className="w-4 h-4" /> Back to top</a>
            </div>
          </div>
        </footer>
        {/* Floating WhatsApp */}
        <a href="https://wa.me/918740027008?text=Hello%20Meadow%20De%20Jalsa%2C%20I'd%20like%20to%20enquire." target="_blank" rel="noopener" className="fixed bottom-5 right-5 z-40 inline-flex items-center gap-2 rounded-full px-4 py-2 bg-emerald-500 hover:bg-emerald-600 text-white shadow-lg">
          <i data-lucide="message-circle" className="w-5 h-5" />
          Chat
          a&gt;
          {/* Booking Modal */}
          <div id="bookingModal" className="hidden fixed inset-0 z-50">
            <div className="absolute inset-0 bg-slate-900/60 backdrop-blur-sm" />
            <div className="relative mx-auto max-w-lg mt-16 sm:mt-24 bg-white rounded-2xl ring-1 ring-slate-200 overflow-hidden">
              <div className="p-5 border-b border-slate-200 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <i data-lucide="calendar-range" className="w-5 h-5 text-emerald-900" />
                  <h3 className="text-[18px] font-semibold text-emerald-950">Book Your Stay</h3>
                </div>
                <button className="p-2 rounded-md hover:bg-slate-100" data-close="bookingModal" aria-label="Close booking modal">
                  <i data-lucide="x" className="w-4 h-4" />
                </button>
              </div>
              <form id="bookingForm" className="p-5 grid sm:grid-cols-2 gap-3">
                <div className="sm:col-span-2">
                  <label className="text-[12px] text-slate-500">Room Type</label>
                  <select id="bookingRoomType" className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]">
                    <option>Standard Room</option>
                    <option>Deluxe Room</option>
                    <option>Luxury Suite</option>
                  </select>
                </div>
                <div>
                  <label className="text-[12px] text-slate-500">Check-in</label>
                  <input type="date" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" />
                </div>
                <div>
                  <label className="text-[12px] text-slate-500">Check-out</label>
                  <input type="date" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" />
                </div>
                <div>
                  <label className="text-[12px] text-slate-500">Guests</label>
                  <input type="number" min={1} defaultValue={2} required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" />
                </div>
                <div>
                  <label className="text-[12px] text-slate-500">Name</label>
                  <input type="text" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Full name" />
                </div>
                <div className="sm:col-span-2">
                  <label className="text-[12px] text-slate-500">Phone</label>
                  <input type="tel" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder={+91} />
                </div>
                <div className="sm:col-span-2 flex items-center justify-between">
                  <label className="inline-flex items-center gap-2 text-[12px] text-slate-600">
                    <input type="checkbox" className="rounded border-slate-300 text-emerald-700 focus:ring-emerald-300" required />
                    I agree to the booking terms.
                  </label>
                  <button className="inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                    <i data-lucide="check-circle-2" className="w-4 h-4" />
                    Confirm
                  </button>
                </div>
                <p id="bookingSuccess" className="sm:col-span-2 hidden text-[13px] text-emerald-700">Request received! Our team will contact you to confirm.</p>
              </form>
            </div>
          </div>
          {/* Enquiry Modal */}
          <div id="enquiryModal" className="hidden fixed inset-0 z-50">
            <div className="absolute inset-0 bg-slate-900/60 backdrop-blur-sm" />
            <div className="relative mx-auto max-w-lg mt-16 sm:mt-24 bg-white rounded-2xl ring-1 ring-slate-200 overflow-hidden">
              <div className="p-5 border-b border-slate-200 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <i data-lucide="messages-square" className="w-5 h-5 text-emerald-900" />
                  <h3 className="text-[18px] font-semibold text-emerald-950">Quick Enquiry</h3>
                </div>
                <button className="p-2 rounded-md hover:bg-slate-100" data-close="enquiryModal" aria-label="Close enquiry modal">
                  <i data-lucide="x" className="w-4 h-4" />
                </button>
              </div>
              <form id="enquiryForm" className="p-5 grid sm:grid-cols-2 gap-3">
                <div className="sm:col-span-2">
                  <label className="text-[12px] text-slate-500">Enquiry Type</label>
                  <input id="enquiryType" type="text" className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" defaultValue="General" />
                </div>
                <div>
                  <label className="text-[12px] text-slate-500">Name</label>
                  <input type="text" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" />
                </div>
                <div>
                  <label className="text-[12px] text-slate-500">Phone</label>
                  <input type="tel" required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" />
                </div>
                <div className="sm:col-span-2">
                  <label className="text-[12px] text-slate-500">Message</label>
                  <textarea rows={4} required className="mt-1 w-full rounded-lg ring-1 ring-slate-200 focus:ring-emerald-300 focus:outline-none px-3 py-2 text-[14px]" placeholder="Tell us more..." defaultValue={""} />
                </div>
                <div className="sm:col-span-2 flex items-center justify-between">
                  <label className="inline-flex items-center gap-2 text-[12px] text-slate-600">
                    <input type="checkbox" className="rounded border-slate-300 text-emerald-700 focus:ring-emerald-300              required />
              I agree to be contacted.
            </label>
            <button type=" submit" />
                    <i data-lucide="send" className="w-4 h-4" />
                    Send
                  </label></div>
                <p id="enquirySuccess" className="sm:col-span-2 hidden text-[13px] text-emerald-700">Thanks! We’ll reach out shortly.</p>
              </form>
            </div>
          </div>
          {/* Lightbox */}
          <div id="lightbox" className="hidden fixed inset-0 z-50">
            <div className="absolute inset-0 bg-slate-900/70 backdrop-blur-sm" />
            <div className="relative mx-auto max-w-4xl mt-16 sm:mt-20 px-4">
              <div className="relative bg-white rounded-2xl ring-1 ring-slate-200 overflow-hidden">
                <button className="absolute top-2 right-2 z-10 p-2 rounded-md bg-white/90 hover:bg-white text-slate-700 ring-1 ring-slate-200" id="lightboxClose" aria-label="Close image">
                  <i data-lucide="x" className="w-4 h-4" />
                </button>
                <img id="lightboxImg" src alt="Gallery preview" className="w-full max-h-[80vh] object-contain bg-black/5" />
              </div>
            </div>
          </div>
          {/* Scripts */}
        </a>
      </div>
    );
  }
});
