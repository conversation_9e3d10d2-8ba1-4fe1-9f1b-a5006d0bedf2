-- Create team_members table
CREATE TABLE IF NOT EXISTS public.team_members (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  position text NOT NULL,
  experience text,
  specialty text,
  bio text,
  image_url text,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to team_members" ON public.team_members
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage team_members" ON public.team_members
  FOR ALL USING (auth.role() = 'authenticated');

-- <PERSON>reate updated_at trigger
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_team_members_updated_at
  BEFORE UPDATE ON public.team_members
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample team members
INSERT INTO public.team_members (name, position, experience, specialty, bio, image_url, display_order, is_active) VALUES
('Chef Rajesh Kumar', 'Executive Chef', '15+ years', 'Multi-cuisine & Continental', 'Master chef with expertise in Indian, Continental, and fusion cuisine. Known for innovative menu development and exceptional culinary presentations.', 'https://images.unsplash.com/photo-1583394838336-acd977736f90?q=80&w=400&auto=format&fit=crop', 1, true),
('Priya Patel', 'Hotel Manager', '12+ years', 'Hospitality Management', 'Experienced hospitality professional dedicated to ensuring exceptional guest experiences and smooth hotel operations.', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=400&auto=format&fit=crop', 2, true),
('Arjun Shah', 'Event Coordinator', '8+ years', 'Wedding & Corporate Events', 'Specialist in creating memorable events with attention to detail and personalized service for weddings and corporate functions.', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=400&auto=format&fit=crop', 3, true),
('Meera Joshi', 'Restaurant Manager', '10+ years', 'Customer Service Excellence', 'Passionate about delivering outstanding dining experiences with focus on service quality and customer satisfaction.', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=400&auto=format&fit=crop', 4, true);