import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { 
  CalendarCheck, 
  CreditCard, 
  Clock, 
  MapPin, 
  Phone,
  Utensils,
  Car,
  Wifi,
  Users,
  MessageSquare,
  Star,
  Gift,
  Bell,
  Settings
} from 'lucide-react';

// Move mock data outside component for better maintainability
const MOCK_BOOKINGS = [
  {
    id: 'RC001',
    roomNumber: '101',
    roomType: 'Deluxe Suite',
    checkIn: '2024-03-15',
    checkOut: '2024-03-18',
    status: 'confirmed',
    guests: 2,
    amount: 15000
  },
  {
    id: 'RC002', 
    roomNumber: '205',
    roomType: 'Standard Room',
    checkIn: '2024-04-20',
    checkOut: '2024-04-22',
    status: 'pending',
    guests: 1,
    amount: 8000
  }
] as const;

const HOTEL_SERVICES = [
  { id: 'restaurant', icon: Utensils, label: 'Restaurant', description: 'International cuisine & local delicacies' },
  { id: 'parking', icon: Car, label: 'Valet Parking', description: 'Complimentary parking service' },
  { id: 'wifi', icon: Wifi, label: 'Free WiFi', description: 'High-speed internet throughout' },
  { id: 'concierge', icon: Users, label: 'Concierge', description: '24/7 guest assistance' }
] as const;

export const GuestDashboard: React.FC = () => {
  const mockBookings = useMemo(() => MOCK_BOOKINGS, []);
  const hotelServices = useMemo(() => HOTEL_SERVICES, []);

  return (
    <div className="space-y-8">
      {/* Enhanced Welcome Header */}
      <motion.div 
        className="relative overflow-hidden rounded-2xl bg-gradient-hero p-8 text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute inset-0 bg-black/20" />
        <div className="absolute top-4 right-4">
          <Button variant="secondary" size="sm" className="bg-white/20 hover:bg-white/30 text-white border-white/30">
            <Bell className="h-4 w-4 mr-2" />
            <Badge variant="destructive" className="ml-1">2</Badge>
          </Button>
        </div>
        <div className="relative z-10">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <Users className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-4xl font-bold mb-2">Welcome to Red Chili</h1>
              <p className="text-primary-foreground/80 text-lg">Your comfort is our priority</p>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-gold" />
                  <span className="text-sm">VIP Guest</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Gift className="h-4 w-4 text-gold" />
                  <span className="text-sm">1,250 Points</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Enhanced Quick Actions */}
      <motion.div 
        className="grid grid-cols-2 md:grid-cols-4 gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button className="h-24 w-full flex flex-col space-y-2 bg-gradient-to-br from-primary to-primary-light hover:from-primary-light hover:to-primary shadow-elegant">
            <CalendarCheck className="h-7 w-7" />
            <span className="text-sm font-medium">Book Room</span>
          </Button>
        </motion.div>
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button variant="outline" className="h-24 w-full flex flex-col space-y-2 bg-white/80 backdrop-blur-sm hover:bg-primary/5 border-primary/20">
            <Utensils className="h-7 w-7 text-primary" />
            <span className="text-sm font-medium">Order Food</span>
          </Button>
        </motion.div>
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button variant="outline" className="h-24 w-full flex flex-col space-y-2 bg-white/80 backdrop-blur-sm hover:bg-primary/5 border-primary/20">
            <MessageSquare className="h-7 w-7 text-primary" />
            <span className="text-sm font-medium">Request Service</span>
          </Button>
        </motion.div>
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button variant="outline" className="h-24 w-full flex flex-col space-y-2 bg-white/80 backdrop-blur-sm hover:bg-primary/5 border-primary/20">
            <Phone className="h-7 w-7 text-primary" />
            <span className="text-sm font-medium">Contact Us</span>
          </Button>
        </motion.div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Enhanced Current Bookings */}
        <motion.div 
          className="lg:col-span-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CalendarCheck className="h-5 w-5 text-primary" />
                <span>My Bookings</span>
              </CardTitle>
              <CardDescription>View and manage your reservations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {mockBookings.map((booking, index) => (
                  <motion.div 
                    key={booking.id} 
                    className="p-6 border border-primary/10 rounded-xl bg-gradient-to-r from-white to-primary/5 hover:shadow-card transition-all duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-lg font-bold text-foreground">{booking.roomType}</h3>
                        <p className="text-muted-foreground font-medium">Room {booking.roomNumber}</p>
                      </div>
                      <Badge 
                        variant={booking.status === 'confirmed' ? 'default' : 'secondary'}
                        className={booking.status === 'confirmed' ? 'bg-success text-success-foreground' : ''}
                      >
                        {booking.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                      <div className="flex items-center space-x-2">
                        <CalendarCheck className="h-4 w-4 text-primary" />
                        <span className="font-medium">Check-in: {booking.checkIn}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-primary" />
                        <span className="font-medium">Check-out: {booking.checkOut}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-primary" />
                        <span className="font-medium">{booking.guests} Guest(s)</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CreditCard className="h-4 w-4 text-gold" />
                        <span className="font-bold text-gold">₹{booking.amount.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="flex space-x-3">
                      <Button size="sm" className="bg-primary hover:bg-primary-light">
                        View Details
                      </Button>
                      <Button size="sm" variant="outline" className="border-primary/30 hover:bg-primary/5">
                        Modify
                      </Button>
                      <Button size="sm" variant="outline" className="border-gold/30 hover:bg-gold/5 text-gold">
                        <Settings className="h-3 w-3 mr-1" />
                        Manage
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Enhanced Hotel Information */}
        <motion.div 
          className="space-y-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-gold" />
                <span>Hotel Services</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {hotelServices.map((service) => (
                  <motion.div 
                    key={service.id} 
                    className="flex items-start space-x-4 p-3 rounded-lg hover:bg-primary/5 transition-colors cursor-pointer"
                    whileHover={{ x: 4 }}
                  >
                    <div className="p-2 rounded-lg bg-primary/10">
                      <service.icon className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold text-foreground">{service.label}</p>
                      <p className="text-sm text-muted-foreground leading-relaxed">{service.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5 text-primary" />
                <span>Contact Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-primary/5">
                <Phone className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-semibold">090334 35279</p>
                  <p className="text-xs text-muted-foreground">24/7 Guest Services</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 rounded-lg bg-primary/5">
                <MapPin className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-semibold text-sm leading-relaxed">
                    u3 empire plaza, Station Rd, opp. vamdut petrol pump, Bardoli, 394601
                  </p>
                </div>
              </div>
              <div className="p-3 rounded-lg bg-gold/10 border border-gold/20">
                <div className="text-sm space-y-1">
                  <p className="font-semibold text-gold-foreground">Operating Hours</p>
                  <p className="text-muted-foreground">Closes 3 PM • Reopens 5:30 PM</p>
                  <p className="text-muted-foreground">Delivery: Until 10 PM</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default GuestDashboard;