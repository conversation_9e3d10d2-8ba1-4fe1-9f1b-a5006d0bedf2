-- Insert page visibility feature flags
-- Run this if the migration hasn't been applied yet

INSERT INTO feature_flags (flag_key, flag_name, description, is_enabled, category) VALUES
('page_about_visible', 'About Us Page', 'Show/hide About Us page in navigation and access', true, 'pages'),
('page_services_visible', 'Services Page', 'Show/hide Services page in navigation and access', true, 'pages'),
('page_properties_visible', 'Properties Page', 'Show/hide Properties page in navigation and access', true, 'pages'),
('page_events_visible', 'Events & Venues Page', 'Show/hide Events & Venues page in navigation and access', true, 'pages')
ON CONFLICT (flag_key) DO NOTHING;