-- Create event_venue_packages table
CREATE TABLE IF NOT EXISTS public.event_venue_packages (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text NOT NULL,
  icon text NOT NULL,
  price text NOT NULL,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.event_venue_packages ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to event_venue_packages" ON public.event_venue_packages
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage event_venue_packages" ON public.event_venue_packages
  FOR ALL USING (auth.role() = 'authenticated');

-- Create updated_at trigger
CREATE TRIGGER handle_event_venue_packages_updated_at
  BEFORE UPDATE ON public.event_venue_packages
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample event venue packages
INSERT INTO public.event_venue_packages (title, description, icon, price, display_order, is_active) VALUES
('Wedding Package', 'Complete wedding celebration with decoration, catering, and photography', 'Crown', 'From ₹1,500/person', 1, true),
('Corporate Package', 'Professional setup with AV equipment, catering, and accommodation', 'Building2', 'From ₹800/person', 2, true),
('Birthday Package', 'Themed celebrations with decoration, cake, and entertainment', 'PartyPopper', 'From ₹1,200/person', 3, true),
('Social Package', 'Intimate gatherings with flexible arrangements and catering', 'Sparkles', 'Customized pricing', 4, true);