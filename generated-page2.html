<html lang="en"><head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Meadow De Jalsa — Luxury Stay &amp; Events</title>
    <meta name="description" content="Meadow De Jalsa — Luxury stay, premium dining, and memorable events on Bardoli–Vyara Highway.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;500;600&amp;family=Inter:wght@300;400;500;600&amp;display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 100 100%27%3E%3Crect width=%27100%27 height=%27100%27 rx=%2718%27 fill=%27%230a1f1f%27/%3E%3Ctext x=%2750%27 y=%2758%27 font-family=%27Georgia%27 font-size=%2750%27 text-anchor=%27middle%27 fill=%27%23fbbf24%27%3EMJ%3C/text%3E%3C/svg%3E">
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "Meadow De Jalsa",
        "url": "https://www.meadowdejalsa.example",
        "telephone": "+91 87400-27008",
        "email": "<EMAIL>",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Bardoli–Vyara Highway",
          "addressLocality": "Bardoli",
          "addressRegion": "GJ",
          "postalCode": "394601",
          "addressCountry": "IN"
        },
        "amenityFeature": [
          { "@type": "LocationFeatureSpecification", "name": "Free WiFi", "value": true },
          { "@type": "LocationFeatureSpecification", "name": "Air Conditioning", "value": true },
          { "@type": "LocationFeatureSpecification", "name": "Room Service", "value": true }
        ],
        "checkinTime": "14:00",
        "checkoutTime": "11:00",
        "petsAllowed": false,
        "starRating": { "@type": "Rating", "ratingValue": "4.5" },
        "servesCuisine": [ "Indian", "Continental", "Chinese" ],
        "hasMap": "https://maps.google.com/?q=Bardoli-Vyara+Highway"
      }
    </script>
  </head>
  <body class="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900" style="font-family: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'; scroll-behavior: smooth;">
    <!-- Sticky Utility Bar -->
    <div class="fixed inset-x-0 top-0 z-50">
      <div class="w-full bg-white/80 backdrop-blur border-b border-slate-200">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-2 flex items-center justify-between">
          <div class="flex items-center gap-5 text-[13px]">
            <a href="tel:+************" class="inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
              <i data-lucide="phone" class="w-4 h-4"></i>
              <span>+91 87400-27008</span>
            </a>
            <a href="mailto:<EMAIL>" class="hidden sm:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
              <i data-lucide="mail" class="w-4 h-4"></i>
              <span><EMAIL></span>
            </a>
            <a href="#contact" class="hidden md:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
              <i data-lucide="map-pin" class="w-4 h-4"></i>
              <span>Bardoli–Vyara Highway</span>
            </a>
          </div>
          <div class="flex items-center gap-3">
            <a href="https://instagram.com" target="_blank" rel="noopener" class="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Instagram">
              <i data-lucide="instagram" class="w-4 h-4"></i>
            </a>
            <a href="https://facebook.com" target="_blank" rel="noopener" class="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Facebook">
              <i data-lucide="facebook" class="w-4 h-4"></i>
            </a>
          </div>
        </div>
      </div>

      <!-- Main Nav (Redesigned) -->
      <nav class="w-full bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 border-b border-slate-200">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
          <a href="#home" class="flex items-center gap-3">
            <div class="h-9 w-9 rounded-lg bg-emerald-950 text-amber-400 grid place-items-center text-sm tracking-tight font-semibold" style="font-family: 'Cormorant Garamond', serif;">MJ</div>
            <div class="flex flex-col leading-tight">
              <span class="text-[15px] sm:text-[16px] text-emerald-950 font-semibold tracking-tight" style="font-family: 'Cormorant Garamond', serif;">Meadow De Jalsa</span>
              <span class="text-[11px] text-slate-500">Luxury Stay &amp; Events</span>
            </div>
          </a>
          <button id="navToggle" aria-label="Toggle navigation" aria-controls="mobileMenu" aria-expanded="false" class="lg:hidden inline-flex items-center justify-center rounded-md p-2 ring-1 ring-slate-200 text-slate-700 hover:text-emerald-900 hover:ring-emerald-300 transition">
            <i data-lucide="menu" class="w-5 h-5"></i>
          </button>
          <div id="navMenu" class="hidden lg:flex items-center gap-1">
            <a href="#home" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 transition relative after:absolute after:left-3 after:right-3 after:-bottom-0.5 after:h-[2px] after:bg-emerald-900/0 hover:after:bg-emerald-900/80 after:transition-colors">Home</a>
            <a href="#about" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 transition relative after:absolute after:left-3 after:right-3 after:-bottom-0.5 after:h-[2px] after:bg-emerald-900/0 hover:after:bg-emerald-900/80 after:transition-colors">About</a>
            <a href="#rooms" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 transition relative after:absolute after:left-3 after:right-3 after:-bottom-0.5 after:h-[2px] after:bg-emerald-900/0 hover:after:bg-emerald-900/80 after:transition-colors">Rooms</a>
            <a href="#restaurant" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 transition relative after:absolute after:left-3 after:right-3 after:-bottom-0.5 after:h-[2px] after:bg-emerald-900/0 hover:after:bg-emerald-900/80 after:transition-colors">Restaurant</a>
            <a href="#events" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 transition relative after:absolute after:left-3 after:right-3 after:-bottom-0.5 after:h-[2px] after:bg-emerald-900/0 hover:after:bg-emerald-900/80 after:transition-colors">Events</a>
            <a href="#gallery" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 transition relative after:absolute after:left-3 after:right-3 after:-bottom-0.5 after:h-[2px] after:bg-emerald-900/0 hover:after:bg-emerald-900/80 after:transition-colors">Gallery</a>
            <a href="#contact" class="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 transition relative after:absolute after:left-3 after:right-3 after:-bottom-0.5 after:h-[2px] after:bg-emerald-900/0 hover:after:bg-emerald-900/80 after:transition-colors">Contact</a>
            <button id="bookNowTop" class="ml-2 inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium tracking-tight shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-900/30">
              <i data-lucide="calendar" class="w-4 h-4"></i>
              Book Now
            </button>
          </div>
        </div>
        <!-- Mobile Menu (Redesigned slide-down) -->
        <div id="mobileMenu" class="lg:hidden border-t border-slate-200 bg-white/95 backdrop-blur overflow-hidden max-h-0 opacity-0 transition-all duration-300">
          <div class="px-4 py-3 flex flex-col gap-1">
            <a href="#home" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Home</a>
            <a href="#about" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">About</a>
            <a href="#rooms" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Rooms</a>
            <a href="#restaurant" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Restaurant</a>
            <a href="#events" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Events</a>
            <a href="#gallery" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Gallery</a>
            <a href="#contact" class="px-3 py-2 rounded-md text-[15px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Contact</a>
            <button id="bookNowTopMobile" class="mt-2 inline-flex items-center justify-center gap-2 rounded-full px-4 py-2 text-[15px] text-emerald-950 font-medium tracking-tight shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
              <i data-lucide="calendar" class="w-4 h-4"></i>
              Book Now
            </button>
          </div>
        </div>
      </nav>
    </div>

    <main class="pt-[128px]">
      <!-- HOME -->
      <section id="home" class="relative">
        <!-- Hero (Redesigned) -->
        <div class="relative min-h-[76vh] sm:min-h-[80vh] w-full overflow-hidden">
          <video class="absolute inset-0 w-full h-full object-cover" src="https://videos.pexels.com/video-files/856130/856130-hd_1920_1080_25fps.mp4" autoplay="" loop="" muted="" playsinline="" poster="https://images.unsplash.com/photo-1501117716987-c8e2a3a67a0b?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" aria-label="Aerial view of property"></video>
          <!-- Layered gradients -->
          <div class="absolute inset-0 bg-gradient-to-b from-emerald-950/70 via-emerald-950/50 to-emerald-950/70"></div>
          <div class="pointer-events-none absolute -top-24 -left-24 h-80 w-80 rounded-full opacity-30 blur-3xl bg-[radial-gradient(circle_at_center,rgba(16,185,129,0.55),transparent_60%)] animate-pulse"></div>
          <div class="pointer-events-none absolute -bottom-24 -right-24 h-72 w-72 rounded-full opacity-25 blur-3xl bg-[radial-gradient(circle_at_center,rgba(251,191,36,0.55),transparent_60%)] animate-pulse"></div>

          <div class="relative z-10 h-full">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full grid lg:grid-cols-12">
              <!-- Content -->
              <div class="my-auto lg:col-span-7 text-left">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/10 backdrop-blur ring-1 ring-white/20">
                  <i data-lucide="map-pin" class="w-4 h-4 text-amber-300"></i>
                  <span class="text-white/90 text-[12px] sm:text-[13px]">Bardoli–Vyara Highway</span>
                </div>
                <h1 class="mt-4 text-white tracking-tight text-4xl sm:text-5xl lg:text-6xl font-semibold leading-[1.1]" style="font-family: 'Cormorant Garamond', serif;">
                  Luxury stays, curated dining, unforgettable events.
                </h1>
                <p class="mt-4 text-white/85 text-[15px] sm:text-[16px] max-w-2xl">
                  Discover a refined escape where elegance meets tradition—crafted for memorable moments.
                </p>
                <div class="mt-8 flex items-center gap-3">
                  <button class="group inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/50" id="bookNowHero">
                    <i data-lucide="calendar-range" class="w-5 h-5"></i>
                    Book Your Stay
                  </button>
                  <a href="#events" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white/90 ring-1 ring-white/30 hover:bg-white/10 transition">
                    <i data-lucide="sparkles" class="w-5 h-5"></i>
                    Plan Your Event
                  </a>
                </div>
                <!-- Feature chips -->
                <div class="mt-8 flex flex-wrap items-center gap-2">
                  <div class="inline-flex items-center gap-2 rounded-full px-3 py-1.5 bg-white/10 ring-1 ring-white/20 text-white/90">
                    <i data-lucide="wifi" class="w-4 h-4 text-amber-300"></i>
                    <span class="text-[12.5px]">High-speed WiFi</span>
                  </div>
                  <div class="inline-flex items-center gap-2 rounded-full px-3 py-1.5 bg-white/10 ring-1 ring-white/20 text-white/90">
                    <i data-lucide="car" class="w-4 h-4 text-amber-300"></i>
                    <span class="text-[12.5px]">Ample Parking</span>
                  </div>
                  <div class="inline-flex items-center gap-2 rounded-full px-3 py-1.5 bg-white/10 ring-1 ring-white/20 text-white/90">
                    <i data-lucide="concierge-bell" class="w-4 h-4 text-amber-300"></i>
                    <span class="text-[12.5px]">Premium Service</span>
                  </div>
                </div>
              </div>

              <!-- Accent card -->
              <div class="hidden lg:flex lg:col-span-5 items-center justify-end">
                <div class="w-full max-w-md rounded-2xl bg-white/95 backdrop-blur ring-1 ring-emerald-900/10 shadow-xl">
                  <div class="p-5">
                    <div class="flex items-center gap-2 text-emerald-900">
                      <i data-lucide="badge-check" class="w-5 h-5"></i>
                      <span class="text-[14px] font-medium">Trusted by 5k+ guests</span>
                    </div>
                    <div class="mt-4 grid grid-cols-3 gap-3 text-center">
                      <div class="p-3 rounded-xl bg-emerald-50 ring-1 ring-emerald-100">
                        <div class="text-xl font-semibold text-emerald-900 tracking-tight">4.8</div>
                        <div class="text-[11px] text-emerald-900/70">Avg. Rating</div>
                      </div>
                      <div class="p-3 rounded-xl bg-amber-50 ring-1 ring-amber-100">
                        <div class="text-xl font-semibold text-emerald-900 tracking-tight">24/7</div>
                        <div class="text-[11px] text-emerald-900/70">Front Desk</div>
                      </div>
                      <div class="p-3 rounded-xl bg-slate-50 ring-1 ring-slate-200">
                        <div class="text-xl font-semibold text-emerald-900 tracking-tight">3</div>
                        <div class="text-[11px] text-emerald-900/70">Venues</div>
                      </div>
                    </div>
                    <div class="mt-4 flex items-center justify-between">
                      <div class="flex -space-x-2">
                        <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&amp;w=200&amp;auto=format&amp;fit=crop" class="h-8 w-8 rounded-full ring-2 ring-white object-cover" alt="Guest 1">
                        <img src="https://images.unsplash.com/photo-1548142813-c348350df52b?q=80&amp;w=200&amp;auto=format&amp;fit=crop" class="h-8 w-8 rounded-full ring-2 ring-white object-cover" alt="Guest 2">
                        <img src="https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?q=80&amp;w=200&amp;auto=format&amp;fit=crop" class="h-8 w-8 rounded-full ring-2 ring-white object-cover" alt="Guest 3">
                      </div>
                      <a href="#rooms" class="inline-flex items-center gap-1.5 text-[13px] text-emerald-900 hover:text-emerald-700 transition">
                        Explore Rooms
                        <i data-lucide="arrow-right" class="w-4 h-4"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Scroll indicator -->
              <div class="pointer-events-none absolute inset-x-0 bottom-5 flex justify-center">
                <div class="inline-flex items-center gap-2 text-white/80">
                  <i data-lucide="chevrons-down" class="w-5 h-5 animate-bounce"></i>
                  <span class="text-[12px]">Scroll</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Info Strip -->
        <div class="bg-white border-y border-slate-200">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-5 grid grid-cols-2 sm:grid-cols-4 gap-3">
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="key-round" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Check-in</div>
                <div class="text-sm font-medium">2:00 PM</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="clock-3" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Restaurant</div>
                <div class="text-sm font-medium">11 AM – 11 PM</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="wifi" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Connectivity</div>
                <div class="text-sm font-medium">High-speed WiFi</div>
              </div>
            </div>
            <div class="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
              <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                <i data-lucide="car" class="w-5 h-5"></i>
              </div>
              <div>
                <div class="text-[12px] text-slate-500">Parking</div>
                <div class="text-sm font-medium">Ample &amp; Secure</div>
              </div>
            </div>
          </div>
        </div>

        <!-- About Snapshot -->
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
          <div class="text-center">
            <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">
              Luxury Stay &amp; Memorable Events on Bardoli–Vyara Highway
            </h2>
            <p class="mt-3 text-slate-600 max-w-2xl mx-auto">
              A modern retreat offering refined rooms, curated dining, and bespoke event experiences.
            </p>
          </div>
        </div>

        <!-- Highlights -->
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-14">
          <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Stay -->
            <a href="#rooms" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Rooms &amp; Suites" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="building-2" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Stay</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Rooms &amp; suites designed for comfort and elegance.</p>
              </div>
            </a>
            <!-- Dine -->
            <a href="#restaurant" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Restaurant" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="utensils" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Dine</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Taste luxury, taste tradition with curated cuisine.</p>
              </div>
            </a>
            <!-- Celebrate -->
            <a href="#events" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Events &amp; Banquets" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="party-popper" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Celebrate</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Weddings, corporate events, parties, and more.</p>
              </div>
            </a>
            <!-- Explore -->
            <a href="#about" class="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" alt="Nearby Attractions" class="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
              <div class="absolute inset-x-0 bottom-0 p-4">
                <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                  <i data-lucide="map" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px] font-medium text-emerald-950">Explore</span>
                </div>
                <p class="mt-3 text-white/90 text-sm">Discover the best of Bardoli &amp; Vyara nearby.</p>
              </div>
            </a>
          </div>
        </div>

        <!-- Events Showcase Carousel -->
        <div class="bg-emerald-950">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-white tracking-tight text-2xl sm:text-3xl font-semibold" style="font-family: 'Cormorant Garamond', serif;">Events Showcase</h3>
              <div class="flex items-center gap-2">
                <button id="eventsPrev" class="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Previous">
                  <i data-lucide="chevron-left" class="w-5 h-5"></i>
                </button>
                <button id="eventsNext" class="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Next">
                  <i data-lucide="chevron-right" class="w-5 h-5"></i>
                </button>
              </div>
            </div>
            <div class="relative">
              <div id="eventsTrack" class="flex gap-4 overflow-hidden scroll-smooth">
                <!-- Slides -->
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1522673607200-164d1b6ce486?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Weddings" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Weddings</div>
                    <p class="mt-3 text-sm text-slate-600">Grand celebrations tailored to your story.</p>
                  </div>
                </div>
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Corporate Events" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Corporate</div>
                    <p class="mt-3 text-sm text-slate-600">Professional settings for conferences and meets.</p>
                  </div>
                </div>
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Parties" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Parties</div>
                    <p class="mt-3 text-sm text-slate-600">Birthdays and anniversaries with flair.</p>
                  </div>
                </div>
                <div class="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                  <img src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Exhibitions" class="h-44 w-full object-cover">
                  <div class="p-4">
                    <div class="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Exhibitions</div>
                    <p class="mt-3 text-sm text-slate-600">Versatile layouts for shows and expos.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Social Live Feed -->
        <div class="bg-white">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-2xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Live Feed</h3>
              <div class="flex items-center gap-3">
                <a href="https://instagram.com" target="_blank" class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <i data-lucide="instagram" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px]">Instagram</span>
                </a>
                <a href="https://facebook.com" target="_blank" class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <i data-lucide="facebook" class="w-4 h-4 text-emerald-900"></i>
                  <span class="text-[13px]">Facebook</span>
                </a>
              </div>
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
              <!-- 6 feed items -->
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&amp;q=80" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&amp;q=80" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
              <a href="#" class="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" class="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed">
                <div class="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
              </a>
            </div>
          </div>
        </div>

        <!-- CTA -->
        <div class="bg-[url('https://images.unsplash.com/photo-1518277850984-94e89c892b26?q=80&amp;w=1760&amp;auto=format&amp;fit=crop')] bg-cover bg-center bg-fixed">
          <div class="bg-emerald-950/80">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
              <h3 class="text-white tracking-tight text-3xl sm:text-4xl font-semibold" style="font-family: 'Cormorant Garamond', serif;">Ready to make memories?</h3>
              <p class="mt-3 text-white/85">Reserve your stay or plan a bespoke event with our expert team.</p>
              <div class="mt-7 flex items-center justify-center gap-3">
                <button id="bookNowCTA" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                  <i data-lucide="calendar" class="w-5 h-5"></i>
                  Book Now
                </button>
                <a href="#contact" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                  <i data-lucide="messages-square" class="w-5 h-5"></i>
                  Talk to Us
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- ABOUT -->
      <section id="about" class="bg-white">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="grid lg:grid-cols-2 gap-10 items-center">
            <div>
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold mb-3" style="font-family: 'Cormorant Garamond', serif;">Meadow De Jalsa — where luxury meets tradition.</h2>
              <p class="text-slate-600">
                From a humble vision to a captivating destination, our story is woven with heartfelt hospitality, contemporary design, and timeless values.
              </p>
              <!-- Timeline -->
              <div class="mt-8 space-y-6">
                <div class="flex gap-4">
                  <div class="flex flex-col items-center">
                    <div class="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                    <div class="flex-1 w-px bg-slate-200"></div>
                  </div>
                  <div>
                    <div class="text-sm text-slate-500">Est. 2016</div>
                    <div class="text-[18px] font-medium text-emerald-950">Foundations</div>
                    <p class="text-sm text-slate-600">The idea of a luxury stopover on the Bardoli–Vyara highway comes alive.</p>
                  </div>
                </div>
                <div class="flex gap-4">
                  <div class="flex flex-col items-center">
                    <div class="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                    <div class="flex-1 w-px bg-slate-200"></div>
                  </div>
                  <div>
                    <div class="text-sm text-slate-500">2018–2021</div>
                    <div class="text-[18px] font-medium text-emerald-950">Growth</div>
                    <p class="text-sm text-slate-600">Rooms, banquet spaces, and a signature restaurant take shape.</p>
                  </div>
                </div>
                <div class="flex gap-4">
                  <div class="flex flex-col items-center">
                    <div class="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                    <div class="flex-1 w-px bg-slate-200"></div>
                  </div>
                  <div>
                    <div class="text-sm text-slate-500">Today</div>
                    <div class="text-[18px] font-medium text-emerald-950">Destination</div>
                    <p class="text-sm text-slate-600">A preferred venue for luxury stays and memorable events.</p>
                  </div>
                </div>
              </div>
              <!-- Highlights -->
              <div class="mt-8 grid sm:grid-cols-3 gap-3">
                <div class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="leaf" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Eco-friendly</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Energy-efficient lighting, water-saving fixtures, greenery.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="route" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Highway Location</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Seamless access and ample secure parking.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="medal" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Premium Hospitality</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Attentive service and bespoke experiences.</p>
                </div>
              </div>
            </div>
            <!-- Gallery Masonry -->
            <div class="columns-2 gap-3">
              <img src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Lobby">
              <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&amp;q=80" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Banquet">
              <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Dining">
              <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&amp;w=1600&amp;auto=format&amp;fit=crop" class="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Room">
            </div>
          </div>

          <!-- Mission & Vision -->
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-4">
            <div class="grid md:grid-cols-2 gap-4">
              <div class="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                <h4 class="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style="font-family: 'Cormorant Garamond', serif;">Our Mission</h4>
                <p class="text-slate-600 text-[15px]">To craft refined stays and memorable events through attentive service, culinary excellence, and thoughtful design.</p>
              </div>
              <div class="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                <h4 class="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style="font-family: 'Cormorant Garamond', serif;">Our Vision</h4>
                <p class="text-slate-600 text-[15px]">To be the region’s most...loved destination for stays and celebrations, setting new standards in hospitality across South Gujarat.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- ROOMS -->
      <section id="rooms" class="bg-slate-50">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="flex items-end justify-between gap-4 mb-8">
            <div>
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Rooms &amp; Suites</h2>
              <p class="mt-2 text-slate-600">Thoughtfully designed spaces for business, leisure, and celebrations.</p>
            </div>
            <a href="#contact" class="hidden sm:inline-flex items-center gap-2 px-4 py-2 rounded-full text-[14px] ring-1 ring-emerald-300 text-emerald-950 hover:bg-emerald-50 transition">
              <i data-lucide="help-circle" class="w-4 h-4"></i>
              Need help choosing?
            </a>
          </div>

          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-5">
            <!-- Deluxe Room -->
            <div class="group rounded-2xl overflow-hidden bg-white ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" alt="Deluxe Room" class="h-52 w-full object-cover group-hover:scale-[1.03] transition duration-500">
                <div class="absolute top-3 left-3 inline-flex items-center gap-2 px-3 py-1 rounded-full bg-white/90 ring-1 ring-slate-200 text-[12px] text-emerald-950">
                  <i data-lucide="sparkles" class="w-4 h-4"></i> Popular
                </div>
              </div>
              <div class="p-5">
                <h3 class="text-[18px] font-semibold text-emerald-950">Deluxe Room</h3>
                <p class="mt-1 text-sm text-slate-600">Queen bed, workstation, and city/green views.</p>
                <div class="mt-3 flex flex-wrap gap-2">
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-emerald-50 ring-1 ring-emerald-200 text-[12px] text-emerald-900">
                    <i data-lucide="wifi" class="w-3.5 h-3.5"></i> WiFi
                  </span>
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-amber-50 ring-1 ring-amber-200 text-[12px] text-amber-900">
                    <i data-lucide="snowflake" class="w-3.5 h-3.5"></i> AC
                  </span>
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200 text-[12px] text-slate-900">
                    <i data-lucide="cup-soda" class="w-3.5 h-3.5"></i> Mini Bar
                  </span>
                </div>
                <div class="mt-5 flex items-center justify-between">
                  <div class="text-slate-900">
                    <span class="text-[13px] text-slate-500">Starting</span>
                    <div class="text-lg font-semibold tracking-tight">₹3,499</div>
                  </div>
                  <button data-room="Deluxe Room" class="book-btn inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium ring-1 ring-amber-400/50 bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 hover:ring-amber-500/70 transition">
                    <i data-lucide="calendar-plus" class="w-4 h-4"></i>
                    Book
                  </button>
                </div>
              </div>
            </div>

            <!-- Premium King -->
            <div class="group rounded-2xl overflow-hidden bg-white ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&amp;q=80" alt="Premium King" class="h-52 w-full object-cover group-hover:scale-[1.03 transition duration-500">
                <div class="absolute top-3 left-3 inline-flex items-center gap-2 px-3 py-1 rounded-full bg-white/90 ring-1 ring-slate-200 text-[12px] text-emerald-950">
                  <i data-lucide="crown" class="w-4 h-4"></i> Premium
                </div>
              </div>
              <div class="p-5">
                <h3 class="text-[18px] font-semibold text-emerald-950">Premium King</h3>
                <p class="mt-1 text-sm text-slate-600">Spacious king bed, lounge corner, and balcony option.</p>
                <div class="mt-3 flex flex-wrap gap-2">
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-emerald-50 ring-1 ring-emerald-200 text-[12px] text-emerald-900">
                    <i data-lucide="tv" class="w-3.5 h-3.5"></i> Smart TV
                  </span>
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-amber-50 ring-1 ring-amber-200 text-[12px] text-amber-900">
                    <i data-lucide="coffee" class="w-3.5 h-3.5"></i> Tea/Coffee
                  </span>
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200 text-[12px] text-slate-900">
                    <i data-lucide="shower-head" class="w-3.5 h-3.5"></i> Rain Shower
                  </span>
                </div>
                <div class="mt-5 flex items-center justify-between">
                  <div class="text-slate-900">
                    <span class="text-[13px] text-slate-500">Starting</span>
                    <div class="text-lg font-semibold tracking-tight">₹4,499</div>
                  </div>
                  <button data-room="Premium King" class="book-btn inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium ring-1 ring-amber-400/50 bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 hover:ring-amber-500/70 transition">
                    <i data-lucide="calendar-plus" class="w-4 h-4"></i>
                    Book
                  </button>
                </div>
              </div>
            </div>

            <!-- Family Suite -->
            <div class="group rounded-2xl overflow-hidden bg-white ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <div class="relative">
                <img src="https://images.unsplash.com/photo-1554995207-c18c203602cb?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Family Suite" class="h-52 w-full object-cover group-hover:scale-[1.03] transition duration-500">
                <div class="absolute top-3 left-3 inline-flex items-center gap-2 px-3 py-1 rounded-full bg-white/90 ring-1 ring-slate-200 text-[12px] text-emerald-950">
                  <i data-lucide="users" class="w-4 h-4"></i> Family
                </div>
              </div>
              <div class="p-5">
                <h3 class="text-[18px] font-semibold text-emerald-950">Family Suite</h3>
                <p class="mt-1 text-sm text-slate-600">Two-room suite with living area—perfect for families.</p>
                <div class="mt-3 flex flex-wrap gap-2">
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-emerald-50 ring-1 ring-emerald-200 text-[12px] text-emerald-900">
                    <i data-lucide="bed-double" class="w-3.5 h-3.5"></i> 2 Beds
                  </span>
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-amber-50 ring-1 ring-amber-200 text-[12px] text-amber-900">
                    <i data-lucide="baby" class="w-3.5 h-3.5"></i> Crib on req.
                  </span>
                  <span class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200 text-[12px] text-slate-900">
                    <i data-lucide="microwave" class="w-3.5 h-3.5"></i> Pantry
                  </span>
                </div>
                <div class="mt-5 flex items-center justify-between">
                  <div class="text-slate-900">
                    <span class="text-[13px] text-slate-500">Starting</span>
                    <div class="text-lg font-semibold tracking-tight">₹5,999</div>
                  </div>
                  <button data-room="Family Suite" class="book-btn inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium ring-1 ring-amber-400/50 bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 hover:ring-amber-500/70 transition">
                    <i data-lucide="calendar-plus" class="w-4 h-4"></i>
                    Book
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-8 flex items-center justify-center">
            <button id="roomsAll" class="inline-flex items-center gap-2 rounded-full px-5 py-2.5 text-[14px] text-emerald-950 font-medium ring-1 ring-slate-300 hover:ring-emerald-300 hover:bg-emerald-50 transition">
              <i data-lucide="layout-grid" class="w-4 h-4"></i>
              View All Amenities
            </button>
          </div>
        </div>
      </section>

      <!-- RESTAURANT -->
      <section id="restaurant" class="bg-white">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="grid lg:grid-cols-2 gap-10 items-center">
            <div class="order-2 lg:order-1">
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Signature Dining</h2>
              <p class="mt-3 text-slate-600">A seasonal menu that blends regional flavors with contemporary techniques—served in warm, elegant surrounds.</p>

              <div class="mt-6 grid sm:grid-cols-3 gap-3">
                <div class="p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="flame" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Live Grill</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Weekend evenings with chef’s specials.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="shrimp" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Multi-cuisine</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Indian, Continental, and Pan-Asian.</p>
                </div>
                <div class="p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="flex items-center gap-2 text-emerald-950">
                    <i data-lucide="wine" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">Mocktails</span>
                  </div>
                  <p class="text-[13px] text-slate-600 mt-1.5">Refreshing sips and classic blends.</p>
                </div>
              </div>

              <div class="mt-6">
                <h3 class="text-[18px] font-semibold text-emerald-950">Chef’s Picks</h3>
                <ul class="mt-2 grid sm:grid-cols-2 gap-2 text-sm text-slate-700">
                  <li class="inline-flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i>Tandoori Platter</li>
                  <li class="inline-flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i>Herbed Grilled Paneer</li>
                  <li class="inline-flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i>Thai Green Curry</li>
                  <li class="inline-flex items-center gap-2"><i data-lucide="check" class="w-4 h-4 text-emerald-700"></i>Sizzling Brownie</li>
                </ul>
              </div>

              <div class="mt-7 flex items-center gap-3">
                <a href="tel:+************" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[14px] text-emerald-950 ring-1 ring-slate-300 hover:bg-emerald-50 hover:ring-emerald-300 transition">
                  <i data-lucide="phone-call" class="w-4 h-4"></i>
                  Reserve a Table
                </a>
                <a href="#gallery" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[14px] text-white bg-emerald-900 hover:bg-emerald-800 transition">
                  <i data-lucide="images" class="w-4 h-4"></i>
                  View Ambience
                </a>
              </div>
            </div>
            <div class="order-1 lg:order-2">
              <div class="grid grid-cols-2 gap-3">
                <img src="https://images.unsplash.com/photo-1528605248644-14dd04022da1?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Restaurant Interior" class="h-64 w-full object-cover rounded-xl ring-1 ring-slate-200">
                <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Dining Area" class="h-64 w-full object-cover rounded-xl ring-1 ring-slate-200">
                <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Signature Dish" class="h-48 sm:h-56 w-full object-cover rounded-xl ring-1 ring-slate-200 col-span-2">
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- EVENTS -->
      <section id="events" class="bg-slate-50">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="text-center mb-8">
            <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Celebrate With Us</h2>
            <p class="mt-2 text-slate-600">From intimate gatherings to grand affairs—our versatile venues and expert team make it seamless.</p>
          </div>
          <div class="grid md:grid-cols-3 gap-5">
            <div class="rounded-2xl bg-white ring-1 ring-slate-200 p-5">
              <img src="https://images.unsplash.com/photo-1464349153735-7db50ed83c84?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Grand Ballroom" class="h-40 w-full object-cover rounded-xl ring-1 ring-slate-200">
              <div class="mt-4">
                <h3 class="text-[18px] font-semibold text-emerald-950">Grand Ballroom</h3>
                <p class="text-sm text-slate-600">Opulent indoor venue with customizable decor.</p>
                <div class="mt-3 inline-flex items-center gap-2 px-3 py-1 rounded-full bg-emerald-50 ring-1 ring-emerald-200 text-[12px] text-emerald-900">
                  <i data-lucide="users" class="w-4 h-4"></i> Up to 500 guests
                </div>
              </div>
            </div>
            <div class="rounded-2xl bg-white ring-1 ring-slate-200 p-5">
              <img src="https://images.unsplash.com/photo-1470225620780-dba8ba36b745?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Lawn Venue" class="h-40 w-full object-cover rounded-xl ring-1 ring-slate-200">
              <div class="mt-4">
                <h3 class="text-[18px] font-semibold text-emerald-950">Emerald Lawns</h3>
                <p class="text-sm text-slate-600">Lush outdoor setting for weddings and receptions.</p>
                <div class="mt-3 inline-flex items-center gap-2 px-3 py-1 rounded-full bg-emerald-50 ring-1 ring-emerald-200 text-[12px] text-emerald-900">
                  <i data-lucide="tree-pine" class="w-4 h-4"></i> Up to 800 guests
                </div>
              </div>
            </div>
            <div class="rounded-2xl bg-white ring-1 ring-slate-200 p-5">
              <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&amp;w=1760&amp;auto=format&amp;fit=crop" alt="Conference Hall" class="h-40 w-full object-cover rounded-xl ring-1 ring-slate-200">
              <div class="mt-4">
                <h3 class="text-[18px] font-semibold text-emerald-950">Conference Hall</h3>
                <p class="text-sm text-slate-600">Ideal for corporate meets, seminars, and workshops.</p>
                <div class="mt-3 inline-flex items-center gap-2 px-3 py-1 rounded-full bg-emerald-50 ring-1 ring-emerald-200 text-[12px] text-emerald-900">
                  <i data-lucide="presentation" class="w-4 h-4"></i> 150–200 guests
                </div>
              </div>
            </div>
          </div>

          <div class="mt-10 text-center">
            <a href="#contact" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white bg-emerald-900 hover:bg-emerald-800 transition">
              <i data-lucide="calendar-clock" class="w-5 h-5"></i>
              Enquire for Dates &amp; Packages
            </a>
          </div>
        </div>
      </section>

      <!-- GALLERY -->
      <section id="gallery" class="bg-white">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="flex items-end justify-between mb-6">
            <div>
              <h2 class="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Gallery</h2>
              <p class="mt-2 text-slate-600">A glimpse of our spaces, cuisine, and celebrations.</p>
            </div>
            <div class="hidden sm:flex items-center gap-2">
              <span class="inline-flex items-center gap-2 px-3 py-1 rounded-full ring-1 ring-slate-200 text-[12px] text-slate-700"><i data-lucide="camera" class="w-4 h-4"></i>Latest</span>
            </div>
          </div>

          <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
            <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&amp;q=80" alt="Room View" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
            <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" alt="Restaurant Corner" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
            <img src="https://images.unsplash.com/photo-1460353581641-37baddab0fa2?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Lobby" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
            <img src="https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Exterior" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
            <img src="https://images.unsplash.com/photo-1528605248644-14dd04022da1?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Fine Dining" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
            <img src="https://images.unsplash.com/photo-1512917774080-9991f1c4c750?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Banquet Entry" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
            <img src="https://images.unsplash.com/photo-1496412705862-e0088f16f791?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Dessert" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
            <img src="https://images.unsplash.com/photo-1518481612222-68bbe828ecd1?q=80&amp;w=1200&amp;auto=format&amp;fit=crop" alt="Event Decor" class="aspect-[4/3] object-cover rounded-xl ring-1 ring-slate-200">
          </div>
        </div>
      </section>

      <!-- CONTACT -->
      <section id="contact" class="bg-emerald-950">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div class="grid lg:grid-cols-2 gap-8">
            <!-- Contact Card -->
            <div class="rounded-2xl bg-white p-6 ring-1 ring-emerald-900/10">
              <h2 class="text-2xl font-semibold text-emerald-950" style="font-family: 'Cormorant Garamond', serif;">Get in Touch</h2>
              <p class="mt-1 text-slate-600 text-[15px]">We’d love to help you plan your stay or event. Reach out to us and our team will get back shortly.</p>

              <div class="mt-4 grid sm:grid-cols-2 gap-3">
                <a href="tel:+************" class="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="phone" class="w-5 h-5"></i>
                  </div>
                  <div class="text-sm">
                    <div class="text-slate-500">Phone</div>
                    <div class="font-medium text-emerald-950">+91 87400-27008</div>
                  </div>
                </a>
                <a href="mailto:<EMAIL>" class="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="mail" class="w-5 h-5"></i>
                  </div>
                  <div class="text-sm">
                    <div class="text-slate-500">Email</div>
                    <div class="font-medium text-emerald-950"><EMAIL></div>
                  </div>
                </a>
                <div class="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="map-pin" class="w-5 h-5"></i>
                  </div>
                  <div class="text-sm">
                    <div class="text-slate-500">Address</div>
                    <div class="font-medium text-emerald-950">Bardoli–Vyara Highway, Bardoli</div>
                  </div>
                </div>
                <div class="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200">
                  <div class="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <i data-lucide="clock" class="w-5 h-5"></i>
                  </div>
                  <div class="text-sm">
                    <div class="text-slate-500">Hours</div>
                    <div class="font-medium text-emerald-950">Hotel: 24/7 · Restaurant: 11 AM – 11 PM</div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <iframe title="Meadow De Jalsa Location" class="w-full h-56 rounded-xl ring-1 ring-slate-200" loading="lazy" referrerpolicy="no-referrer-when-downgrade" src="https://maps.google.com/maps?q=Bardoli-Vyara%20Highway&amp;t=&amp;z=12&amp;ie=UTF8&amp;iwloc=&amp;output=embed"></iframe>
              </div>
            </div>

            <!-- Form -->
            <div class="rounded-2xl bg-white/5 ring-1 ring-white/10 p-6">
              <h3 class="text-white text-xl font-semibold">Send us a message</h3>
              <form id="contactForm" class="mt-4 grid grid-cols-1 gap-3">
                <div class="grid sm:grid-cols-2 gap-3">
                  <label class="block">
                    <span class="sr-only">Full Name</span>
                    <input type="text" name="name" required="" placeholder="Full Name" class="w-full rounded-lg bg-white/90 text-slate-900 placeholder-slate-500 px-4 py-3 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
                  </label>
                  <label class="block">
                    <span class="sr-only">Phone</span>
                    <input type="tel" name="phone" required="" placeholder="Phone" class="w-full rounded-lg bg-white/90 text-slate-900 placeholder-slate-500 px-4 py-3 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
                  </label>
                </div>
                <label class="block">
                  <span class="sr-only">Email</span>
                  <input type="email" name="email" placeholder="Email (optional)" class="w-full rounded-lg bg-white/90 text-slate-900 placeholder-slate-500 px-4 py-3 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
                </label>
                <div class="grid sm:grid-cols-2 gap-3">
                  <label class="block">
                    <span class="sr-only">Event/Stay Type</span>
                    <select name="type" class="w-full rounded-lg bg-white/90 text-slate-900 px-4 py-3 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
                      <option>Room Reservation</option>
                      <option>Wedding</option>
                      <option>Corporate Event</option>
                      <option>Party</option>
                      <option>Other</option>
                    </select>
                  </label>
                  <label class="block">
                    <span class="sr-only">Preferred Date</span>
                    <input type="date" name="preferredDate" class="w-full rounded-lg bg-white/90 text-slate-900 px-4 py-3 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
                  </label>
                </div>
                <label class="block">
                  <span class="sr-only">Message</span>
                  <textarea name="message" rows="4" placeholder="Tell us about your plan..." class="w-full rounded-lg bg-white/90 text-slate-900 placeholder-slate-500 px-4 py-3 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none"></textarea>
                </label>
                <div class="flex items-center justify-between">
                  <label class="inline-flex items-center gap-2 text-white/80 text-sm">
                    <input type="checkbox" class="rounded border-slate-300 text-emerald-600 focus:ring-emerald-400" required="">
                    I agree to be contacted regarding my enquiry.
                  </label>
                  <button type="submit" class="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[14px] font-medium text-emerald-950 ring-1 ring-amber-400/50 bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 hover:ring-amber-500/70 transition">
                    <i data-lucide="send" class="w-4 h-4"></i>
                    Send Message
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- FOOTER -->
    <footer class="bg-white border-t border-slate-200">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-10">
        <div class="grid md:grid-cols-4 gap-8">
          <div class="md:col-span-2">
            <div class="flex items-center gap-3">
              <div class="h-9 w-9 rounded-lg bg-emerald-950 text-amber-400 grid place-items-center text-sm tracking-tight font-semibold" style="font-family: 'Cormorant Garamond', serif;">MJ</div>
              <div class="flex flex-col leading-tight">
                <span class="text-[16px] text-emerald-950 font-semibold" style="font-family: 'Cormorant Garamond', serif;">Meadow De Jalsa</span>
                <span class="text-[12px] text-slate-500">Luxury Stay &amp; Events</span>
              </div>
            </div>
            <p class="mt-3 text-sm text-slate-600 max-w-lg">A refined destination on the Bardoli–Vyara Highway offering elegant rooms, curated dining, and bespoke celebrations.</p>
            <div class="mt-4 flex items-center gap-2">
              <a href="https://instagram.com" target="_blank" rel="noopener" class="p-2 rounded-lg ring-1 ring-slate-200 hover:ring-emerald-300 text-slate-700 hover:text-emerald-800 transition" aria-label="Instagram">
                <i data-lucide="instagram" class="w-4 h-4"></i>
              </a>
              <a href="https://facebook.com" target="_blank" rel="noopener" class="p-2 rounded-lg ring-1 ring-slate-200 hover:ring-emerald-300 text-slate-700 hover:text-emerald-800 transition" aria-label="Facebook">
                <i data-lucide="facebook" class="w-4 h-4"></i>
              </a>
            </div>
          </div>

          <div>
            <h4 class="text-[15px] font-semibold text-emerald-950">Quick Links</h4>
            <ul class="mt-3 space-y-2 text-sm">
              <li><a href="#home" class="text-slate-600 hover:text-emerald-800 transition">Home</a></li>
              <li><a href="#about" class="text-slate-600 hover:text-emerald-800 transition">About</a></li>
              <li><a href="#rooms" class="text-slate-600 hover:text-emerald-800 transition">Rooms</a></li>
              <li><a href="#restaurant" class="text-slate-600 hover:text-emerald-800 transition">Restaurant</a></li>
              <li><a href="#events" class="text-slate-600 hover:text-emerald-800 transition">Events</a></li>
              <li><a href="#gallery" class="text-slate-600 hover:text-emerald-800 transition">Gallery</a></li>
              <li><a href="#contact" class="text-slate-600 hover:text-emerald-800 transition">Contact</a></li>
            </ul>
          </div>

          <div>
            <h4 class="text-[15px] font-semibold text-emerald-950">Newsletter</h4>
            <p class="mt-3 text-sm text-slate-600">Get offers, event updates, and seasonal menus.</p>
            <form id="newsletter" class="mt-3 flex items-center gap-2">
              <label class="sr-only" for="nl-email">Email</label>
              <input id="nl-email" type="email" required="" placeholder="Your email" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
              <button class="inline-flex items-center gap-2 rounded-md px-3 py-2 text-[13px] text-white bg-emerald-900 hover:bg-emerald-800 transition">
                <i data-lucide="mail-check" class="w-4 h-4"></i>
                Join
              </button>
            </form>
          </div>
        </div>

        <div class="mt-8 pt-6 border-t border-slate-200 flex flex-col sm:flex-row items-center justify-between gap-3">
          <p class="text-[13px] text-slate-500">© <span id="year"></span> Meadow De Jalsa. All rights reserved.</p>
          <div class="flex items-center gap-4 text-[12px] text-slate-500">
            <a href="#" class="hover:text-emerald-800 transition">Privacy</a>
            <a href="#" class="hover:text-emerald-800 transition">Terms</a>
          </div>
        </div>
      </div>
    </footer>

    <!-- Back to Top -->
    <button id="backToTop" aria-label="Back to top" class="fixed bottom-5 right-5 z-40 hidden p-3 rounded-full bg-emerald-900 text-white shadow-lg hover:bg-emerald-800 transition">
      <i data-lucide="arrow-up" class="w-5 h-5"></i>
    </button>

    <!-- Booking Modal -->
    <div id="bookingModal" class="fixed inset-0 z-50 hidden" aria-hidden="true">
      <div class="absolute inset-0 bg-black/40 backdrop-blur-sm"></div>
      <div role="dialog" aria-modal="true" aria-labelledby="bookingTitle" class="relative mx-auto my-10 w-[92%] max-w-lg rounded-2xl bg-white ring-1 ring-slate-200 shadow-2xl">
        <div class="flex items-center justify-between px-5 py-4 border-b border-slate-200">
          <div class="flex items-center gap-2">
            <i data-lucide="calendar" class="w-5 h-5 text-emerald-900"></i>
            <h3 id="bookingTitle" class="text-[18px] font-semibold text-emerald-950">Book Your Stay</h3>
          </div>
          <button id="bookingClose" aria-label="Close booking form" class="p-2 rounded-md hover:bg-slate-100 text-slate-700 transition">
            <i data-lucide="x" class="w-5 h-5"></i>
          </button>
        </div>
        <form id="bookingForm" class="px-5 py-5 space-y-3">
          <div class="grid sm:grid-cols-2 gap-3">
            <label class="block">
              <span class="sr-only">Check-in</span>
              <input type="date" id="checkin" name="checkin" required="" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
            </label>
            <label class="block">
              <span class="sr-only">Check-out</span>
              <input type="date" id="checkout" name="checkout" required="" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
            </label>
          </div>
          <div class="grid sm:grid-cols-2 gap-3">
            <label class="block">
              <span class="sr-only">Guests</span>
              <input type="number" name="guests" min="1" value="2" required="" placeholder="Guests" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
            </label>
            <label class="block">
              <span class="sr-only">Room Type</span>
              <select id="roomType" name="roomType" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
                <option>Deluxe Room</option>
                <option>Premium King</option>
                <option>Family Suite</option>
              </select>
            </label>
          </div>
          <div class="grid sm:grid-cols-2 gap-3">
            <label class="block">
              <span class="sr-only">Full Name</span>
              <input type="text" name="fullName" required="" placeholder="Full Name" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
            </label>
            <label class="block">
              <span class="sr-only">Phone</span>
              <input type="tel" name="phone" required="" placeholder="Phone" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none">
            </label>
          </div>
          <label class="block">
            <span class="sr-only">Special Requests</span>
            <textarea name="requests" rows="3" placeholder="Special requests (optional)" class="w-full rounded-lg px-3 py-2 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none"></textarea>
          </label>
          <div class="flex items-center justify-between pt-2">
            <label class="inline-flex items-center gap-2 text-slate-600 text-[13px]">
              <input type="checkbox" required="" class="rounded border-slate-300 text-emerald-600 focus:ring-emerald-400">
              I agree to the terms and privacy policy.
            </label>
            <button type="submit" class="inline-flex items-center gap-2 rounded-full px-5 py-2.5 text-[14px] font-medium text-emerald-950 ring-1 ring-amber-400/50 bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 hover:ring-amber-500/70 transition">
              <i data-lucide="check-circle" class="w-4 h-4"></i>
              Request Booking
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- SCRIPTS -->
    <script>
      // Icons
      document.addEventListener('DOMContentLoaded', () => {
        if (window.lucide && lucide.createIcons) lucide.createIcons();
      });

      // Year
      document.getElementById('year').textContent = new Date().getFullYear();

      // Mobile Nav Toggle
      const navToggle = document.getElementById('navToggle');
      const mobileMenu = document.getElementById('mobileMenu');
      if (navToggle && mobileMenu) {
        navToggle.addEventListener('click', () => {
          const expanded = navToggle.getAttribute('aria-expanded') === 'true';
          navToggle.setAttribute('aria-expanded', String(!expanded));
          mobileMenu.style.maxHeight = expanded ? '0px' : mobileMenu.scrollHeight + 'px';
          mobileMenu.style.opacity = expanded ? '0' : '1';
        });
        // Close mobile menu on link click
        mobileMenu.querySelectorAll('a').forEach(a => {
          a.addEventListener('click', () => {
            mobileMenu.style.maxHeight = '0px';
            mobileMenu.style.opacity = '0';
            navToggle.setAttribute('aria-expanded', 'false');
          });
        });
      }

      // Events carousel
      const eventsTrack = document.getElementById('eventsTrack');
      const eventsNext = document.getElementById('eventsNext');
      const eventsPrev = document.getElementById('eventsPrev');
      if (eventsTrack && eventsNext && eventsPrev) {
        const scrollBy = () => Math.min(400, eventsTrack.clientWidth * 0.6);
        eventsNext.addEventListener('click', () => eventsTrack.scrollBy({ left: scrollBy(), behavior: 'smooth' }));
        eventsPrev.addEventListener('click', () => eventsTrack.scrollBy({ left: -scrollBy(), behavior: 'smooth' }));
      }

      // Back to top
      const backToTop = document.getElementById('backToTop');
      window.addEventListener('scroll', () => {
        if (window.scrollY > 600) backToTop.classList.remove('hidden');
        else backToTop.classList.add('hidden');
      });
      backToTop?.addEventListener('click', () => window.scrollTo({ top: 0, behavior: 'smooth' }));

      // Booking Modal
      const modal = document.getElementById('bookingModal');
      const openIds = ['bookNowTop','bookNowTopMobile','bookNowHero','bookNowCTA'];
      openIds.forEach(id => document.getElementById(id)?.addEventListener('click', () => openModal()));
      document.querySelectorAll('.book-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const room = btn.getAttribute('data-room');
          if (room) document.getElementById('roomType').value = room;
          openModal();
        });
      });
      function openModal() {
        modal.classList.remove('hidden');
        modal.setAttribute('aria-hidden', 'false');
        // initialize dates
        const ci = document.getElementById('checkin');
        const co = document.getElementById('checkout');
        const today = new Date();
        const tomorrow = new Date(Date.now() + 24*60*60*1000);
        const fmt = d => d.toISOString().slice(0,10);
        ci.value = fmt(today);
        co.value = fmt(tomorrow);
        ci.min = fmt(today);
        co.min = fmt(tomorrow);
        // focus
        setTimeout(() => ci.focus(), 50);
      }
      function closeModal() {
        modal.classList.add('hidden');
        modal.setAttribute('aria-hidden', 'true');
      }
      document.getElementById('bookingClose')?.addEventListener('click', closeModal);
      modal?.addEventListener('click', () => {
        if (e.target === modal.firstElementChild) closeModal();
      });
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) closeModal();
      });

      // Booking submission (demo)
      document.getElementById('bookingForm')?.addEventListener('submit', (e) => {
        e.preventDefault();
        closeModal();
        alert('Thank you! Your booking request has been received. Our team will contact you shortly.');
      });

      // Contact form (demo)
      document.getElementById('contactForm')?.addEventListener('submit', (e) => {
        e.preventDefault();
        alert('Thanks for reaching out! We will get back to you soon.');
        e.target.reset();
      });

      // Newsletter (demo)
      document.getElementById('newsletter')?.addEventListener('submit', (e) => {
        e.preventDefault();
        alert('You have been subscribed to our newsletter.');
        e.target.reset();
      });
    </script>
  
</body></html>