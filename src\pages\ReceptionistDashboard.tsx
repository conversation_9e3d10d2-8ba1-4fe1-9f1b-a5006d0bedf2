import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { 
  CalendarCheck, 
  Clock, 
  Users, 
  Bed,
  CreditCard,
  Phone,
  LogIn,
  LogOut,
  UserPlus,
  AlertTriangle,
  Activity,
  Bell,
  TrendingUp
} from 'lucide-react';

export const ReceptionistDashboard: React.FC = () => {
  const todayStats = {
    checkIns: 12,
    checkOuts: 8,
    currentGuests: 45,
    pendingPayments: 3,
    roomsAvailable: 15
  };

  const upcomingArrivals = [
    { id: 'arr1', name: '<PERSON><PERSON>', room: '101', time: '14:00', guests: 2, status: 'confirmed' },
    { id: 'arr2', name: '<PERSON><PERSON>', room: '205', time: '15:30', guests: 1, status: 'pending' },
    { id: 'arr3', name: '<PERSON>', room: '302', time: '16:00', guests: 4, status: 'confirmed' },
    { id: 'arr4', name: '<PERSON>', room: '108', time: '17:00', guests: 2, status: 'vip' }
  ];

  const upcomingDepartures = [
    { id: 'dep1', name: 'Suresh <PERSON>di', room: '204', time: '11:00', payment: 'settled', checkout: false },
    { id: 'dep2', name: 'Meera Joshi', room: '156', time: '12:00', payment: 'pending', checkout: false },
    { id: 'dep3', name: 'Arjun Singh', room: '301', time: '10:30', payment: 'settled', checkout: true }
  ];

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <motion.div 
        className="relative overflow-hidden rounded-2xl bg-gradient-hero p-8 text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute inset-0 bg-black/20" />
        <div className="absolute top-4 right-4">
          <Button variant="secondary" size="sm" className="bg-white/20 hover:bg-white/30 text-white border-white/30">
            <Bell className="h-4 w-4 mr-2" />
            Alerts
            <Badge variant="destructive" className="ml-2">5</Badge>
          </Button>
        </div>
        <div className="relative z-10">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <Activity className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-4xl font-bold mb-2">Reception Dashboard</h1>
              <p className="text-primary-foreground/80 text-lg">Managing guest arrivals and departures</p>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4 text-gold" />
                  <span className="text-sm">85% Occupancy</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4 text-gold" />
                  <span className="text-sm">45 Active Guests</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Enhanced Quick Stats */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-5 gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {[
          { icon: LogIn, value: todayStats.checkIns, label: 'Check-ins Today', color: 'success' },
          { icon: LogOut, value: todayStats.checkOuts, label: 'Check-outs Today', color: 'warning' },
          { icon: Users, value: todayStats.currentGuests, label: 'Current Guests', color: 'primary' },
          { icon: Bed, value: todayStats.roomsAvailable, label: 'Rooms Available', color: 'gold' },
          { icon: AlertTriangle, value: todayStats.pendingPayments, label: 'Pending Payments', color: 'destructive' }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            whileHover={{ scale: 1.02, y: -2 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant hover:shadow-card transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-xl ${
                    stat.color === 'success' ? 'bg-success/10 text-success' :
                    stat.color === 'warning' ? 'bg-warning/10 text-warning' :
                    stat.color === 'gold' ? 'bg-gold/10 text-gold' :
                    stat.color === 'destructive' ? 'bg-destructive/10 text-destructive' :
                    'bg-primary/10 text-primary'
                  }`}>
                    <stat.icon className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-foreground">{stat.value}</p>
                    <p className="text-sm text-muted-foreground font-medium">{stat.label}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Enhanced Upcoming Arrivals */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <LogIn className="h-5 w-5 text-success" />
                <span>Today's Arrivals</span>
              </CardTitle>
              <CardDescription>Guests checking in today</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingArrivals.map((guest, index) => (
                  <motion.div 
                    key={guest.id} 
                    className="p-4 border border-primary/10 rounded-xl bg-gradient-to-r from-white to-success/5 hover:shadow-card transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="font-bold text-foreground">{guest.name}</span>
                          <Badge 
                            variant={guest.status === 'vip' ? 'default' : 'secondary'}
                            className={guest.status === 'vip' ? 'bg-gold text-gold-foreground' : ''}
                          >
                            {guest.status.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-6 text-sm">
                          <span className="flex items-center space-x-2">
                            <Bed className="h-4 w-4 text-primary" />
                            <span className="font-medium">Room {guest.room}</span>
                          </span>
                          <span className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-primary" />
                            <span className="font-medium">{guest.time}</span>
                          </span>
                          <span className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-primary" />
                            <span className="font-medium">{guest.guests} guests</span>
                          </span>
                        </div>
                      </div>
                      <Button size="sm" className="ml-4 bg-success hover:bg-success/90">
                        <LogIn className="h-3 w-3 mr-1" />
                        Check In
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Enhanced Upcoming Departures */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <LogOut className="h-5 w-5 text-warning" />
                <span>Today's Departures</span>
              </CardTitle>
              <CardDescription>Guests checking out today</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingDepartures.map((guest, index) => (
                  <motion.div 
                    key={guest.id} 
                    className="p-4 border border-primary/10 rounded-xl bg-gradient-to-r from-white to-warning/5 hover:shadow-card transition-all duration-300"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="font-bold text-foreground">{guest.name}</span>
                          <Badge 
                            variant={guest.payment === 'settled' ? 'default' : 'destructive'}
                            className={guest.payment === 'settled' ? 'bg-success text-success-foreground' : ''}
                          >
                            {guest.payment.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-6 text-sm">
                          <span className="flex items-center space-x-2">
                            <Bed className="h-4 w-4 text-primary" />
                            <span className="font-medium">Room {guest.room}</span>
                          </span>
                          <span className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-primary" />
                            <span className="font-medium">{guest.time}</span>
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {guest.payment === 'pending' && (
                          <Button size="sm" variant="outline" className="border-gold/30 hover:bg-gold/5 text-gold">
                            <CreditCard className="h-3 w-3 mr-1" />
                            Pay
                          </Button>
                        )}
                        <Button 
                          size="sm" 
                          disabled={guest.checkout}
                          className={guest.checkout ? 'bg-muted' : 'bg-warning hover:bg-warning/90'}
                        >
                          <LogOut className="h-3 w-3 mr-1" />
                          {guest.checkout ? 'Checked Out' : 'Check Out'}
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Enhanced Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-primary" />
              <span>Quick Actions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { icon: UserPlus, label: 'Walk-in Guest', color: 'primary' },
                { icon: CalendarCheck, label: 'New Booking', color: 'success' },
                { icon: CreditCard, label: 'Process Payment', color: 'gold' },
                { icon: Phone, label: 'Guest Services', color: 'warning' }
              ].map((action, index) => (
                <motion.div
                  key={action.label}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button 
                    variant={index === 0 ? 'default' : 'outline'} 
                    className={`h-24 w-full flex flex-col space-y-2 ${
                      index === 0 
                        ? 'bg-gradient-to-br from-primary to-primary-light hover:from-primary-light hover:to-primary shadow-elegant' 
                        : 'bg-white/80 backdrop-blur-sm hover:bg-primary/5 border-primary/20'
                    }`}
                  >
                    <action.icon className={`h-7 w-7 ${index === 0 ? 'text-white' : 'text-primary'}`} />
                    <span className={`text-sm font-medium ${index === 0 ? 'text-white' : 'text-primary'}`}>
                      {action.label}
                    </span>
                  </Button>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default ReceptionistDashboard;