-- Create accommodation_types table
CREATE TABLE IF NOT EXISTS public.accommodation_types (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text NOT NULL,
  icon text NOT NULL,
  features text[] DEFAULT '{}',
  price text NOT NULL,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.accommodation_types ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to accommodation_types" ON public.accommodation_types
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage accommodation_types" ON public.accommodation_types
  FOR ALL USING (auth.role() = 'authenticated');

-- Create updated_at trigger
CREATE TRIGGER handle_accommodation_types_updated_at
  BEFORE UPDATE ON public.accommodation_types
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample accommodation types
INSERT INTO public.accommodation_types (title, description, icon, features, price, display_order, is_active) VALUES
('Standard Rooms', 'Cozy comfort with essential amenities', 'Bed', ARRAY['AC & WiFi', 'Smart TV', 'Room Service', 'Daily Housekeeping'], '₹2,999/night', 1, true),
('Deluxe Rooms', 'Spacious elegance with premium amenities', 'Building2', ARRAY['King Bed', 'Rain Shower', 'Tea/Coffee', 'Work Desk'], '₹4,499/night', 2, true),
('Luxury Suites', 'Ultimate comfort with personalized service', 'Crown', ARRAY['Butler Service', 'Lounge Access', 'Luxury Linen', 'Soaking Tub'], '₹6,999/night', 3, true),
('Premium Amenities', 'Comprehensive facilities for all guests', 'Car', ARRAY['Valet Parking', '24/7 Concierge', 'Business Center', 'High-speed WiFi'], 'Complimentary', 4, true);