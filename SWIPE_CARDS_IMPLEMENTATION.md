# Dynamic Swipe Cards Implementation

## Overview
This implementation provides a dynamic swipe cards system where content can be managed from the admin dashboard and displayed on the website.

## Database Structure

### Swipe Cards Table
- **Table**: `swipe_cards`
- **Fields**:
  - `id`: UUID (Primary Key)
  - `title`: VARCHAR(255) - Card title
  - `description`: TEXT - Card description
  - `image_url`: TEXT - Image URL (can be uploaded or external)
  - `display_order`: INTEGER - Order of display
  - `is_active`: BOOLEAN - Whether card is active
  - `created_at`: TIMESTAMP
  - `updated_at`: TIMESTAMP

### Storage Bucket
- **Bucket**: `images`
- **Purpose**: Store uploaded images
- **File Types**: JPEG, PNG, WebP, GIF
- **Size Limit**: 10MB
- **Access**: Public read, authenticated write

## Features

### Admin Dashboard (ContentManager)
1. **Swipe Cards Tab**: New tab in the content manager
2. **CRUD Operations**: Create, Read, Update, Delete swipe cards
3. **Image Upload**: Upload images directly to Supabase storage
4. **Image Preview**: Preview uploaded images
5. **Order Management**: Set display order for cards
6. **Status Toggle**: Enable/disable cards

### Frontend Display
1. **Dynamic Loading**: Fetches active cards from database
2. **Fallback Images**: Uses default images if database fails
3. **Responsive Design**: Works on all screen sizes
4. **Auto-play**: Automatic card rotation
5. **Touch Support**: Swipe gestures on mobile

## Usage

### Adding New Swipe Cards
1. Go to Admin Dashboard
2. Navigate to Content Management
3. Click on "Swipe Cards" tab
4. Click "Add Swipe Card" button
5. Fill in title and description
6. Upload image or provide URL
7. Set display order
8. Save

### Managing Existing Cards
1. View all cards in the grid layout
2. Edit cards using the edit button
3. Delete cards using the delete button
4. Toggle active status using the switch

## File Structure
```
src/
├── components/
│   ├── admin/
│   │   └── ContentManager.tsx (Updated with swipe cards tab)
│   └── ui/
│       └── Swipecard.tsx (Updated to be dynamic)
├── integrations/
│   └── supabase/
│       └── types.ts (Updated with swipe_cards types)
└── supabase/
    └── migrations/
        ├── 20240101000001_create_swipe_cards.sql
        └── 20240101000002_create_storage_bucket.sql
```

## Migration Commands
Run these migrations in your Supabase project:
1. `20240101000001_create_swipe_cards.sql` - Creates the swipe_cards table
2. `20240101000002_create_storage_bucket.sql` - Creates the images storage bucket

## Security
- RLS (Row Level Security) enabled
- Public read access for active cards only
- Authenticated users can manage all cards
- Storage bucket has proper access policies