import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { X, Phone, Mail, Clock, Users, Calendar } from 'lucide-react';

interface TableReservationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CompanySetting {
  setting_key: string;
  setting_value: string;
}

const TableReservationModal: React.FC<TableReservationModalProps> = ({ isOpen, onClose }) => {
  const [settings, setSettings] = useState<CompanySetting[]>([]);

  useEffect(() => {
    if (isOpen) {
      fetchSettings();
    }
  }, [isOpen]);

  const fetchSettings = async () => {
    try {
      const { data } = await supabase
        .from('company_settings')
        .select('setting_key, setting_value')
        .eq('is_active', true)
        .in('setting_key', ['phone_primary', 'email_primary']);
      
      setSettings(data || []);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const getSetting = (key: string, defaultValue: string = '') => {
    const setting = settings.find(s => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full ring-1 ring-slate-200 overflow-hidden">
        <div className="relative bg-emerald-950 px-6 py-8 text-center">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-full bg-white/10 hover:bg-white/20 transition"
          >
            <X className="w-5 h-5 text-white" />
          </button>
          
          <div className="h-16 w-16 rounded-full bg-amber-400 text-emerald-950 grid place-items-center mx-auto mb-4">
            <Users className="w-8 h-8" />
          </div>
          
          <h2 className="text-2xl font-semibold text-white mb-2" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Reserve Your Table
          </h2>
          <p className="text-white/80 text-sm">
            Contact us directly to secure your dining experience
          </p>
        </div>

        <div className="p-6 space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-emerald-950 mb-2">Quick Reservation</h3>
            <p className="text-sm text-slate-600 mb-4">
              Call or email us to reserve your table. Our team will confirm your booking within minutes.
            </p>
          </div>

          <div className="space-y-4">
            <a
              href={`tel:${getSetting('phone_primary', '+918740027008')}`}
              className="flex items-center gap-4 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition group"
            >
              <div className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center group-hover:bg-emerald-100 transition">
                <Phone className="w-6 h-6" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-emerald-950">Call Now</div>
                <div className="text-sm text-slate-600">{getSetting('phone_primary', '+91 87400-27008')}</div>
              </div>
            </a>

            <a
              href={`mailto:${getSetting('email_primary', '<EMAIL>')}?subject=Table Reservation Request&body=Hello, I would like to reserve a table. Please contact me with available times.`}
              className="flex items-center gap-4 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition group"
            >
              <div className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center group-hover:bg-emerald-100 transition">
                <Mail className="w-6 h-6" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-emerald-950">Email Us</div>
                <div className="text-sm text-slate-600">{getSetting('email_primary', '<EMAIL>')}</div>
              </div>
            </a>
          </div>

          <div className="bg-slate-50 rounded-xl p-4">
            <h4 className="text-sm font-semibold text-emerald-950 mb-3 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Dining Hours
            </h4>
            <div className="space-y-2 text-sm text-slate-600">
              <div className="flex justify-between">
                <span>Lunch</span>
                <span>11:00 AM - 3:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span>Dinner</span>
                <span>7:00 PM - 11:00 PM</span>
              </div>
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={onClose}
              className="w-full rounded-full px-6 py-3 text-sm font-medium text-slate-600 ring-1 ring-slate-200 hover:bg-slate-50 transition"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableReservationModal;