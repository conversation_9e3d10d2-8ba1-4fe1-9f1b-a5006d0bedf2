import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import * as LucideIcons from 'lucide-react';

interface EventVenuePackage {
  id: string;
  title: string;
  description: string;
  icon: string;
  price: string;
  display_order: number;
  is_active: boolean;
}

const EventPackagesSection: React.FC = () => {
  const [eventVenuePackages, setEventVenuePackages] = useState<EventVenuePackage[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchEventVenuePackages();
  }, []);

  const fetchEventVenuePackages = async () => {
    try {
      const { data } = await supabase
        .from('event_venue_packages')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      setEventVenuePackages(data || []);
    } catch (error) {
      console.error('Error fetching event venue packages:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIconComponent = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName] || LucideIcons.Package;
    return IconComponent;
  };

  if (loading) {
    return (
      <section className="bg-slate-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-12"></div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <motion.div 
                  key={i} 
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.6, delay: i * 0.1, ease: "easeOut" }}
                  viewport={{ once: true }}
                  className="h-48 bg-slate-200 rounded-2xl"
                ></motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  };

  if (eventVenuePackages.length === 0) {
    return null;
  }

  return (
    <section className="bg-slate-50">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" 
            style={{ fontFamily: "'Cormorant Garamond', serif" }}
          >
            Event Packages
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-2 text-slate-600"
          >
            Comprehensive packages tailored to make your celebration perfect
          </motion.p>
        </motion.div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {eventVenuePackages.map((pkg, index) => {
            const IconComponent = getIconComponent(pkg.icon);
            return (
              <motion.div 
                key={pkg.id} 
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.5 + (index * 0.1), ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.05 }}
                className="bg-white rounded-2xl p-6 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300 text-center"
              >
                <motion.div 
                  initial={{ scale: 0, rotate: -180 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 + (index * 0.1) }}
                  viewport={{ once: true }}
                  whileHover={{ rotate: 360, scale: 1.2 }}
                  className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mx-auto mb-4"
                >
                  <IconComponent className="w-6 h-6" />
                </motion.div>
                <motion.h3 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.8 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[16px] font-semibold text-emerald-950 mb-2"
                >
                  {pkg.title}
                </motion.h3>
                <motion.p 
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.9 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[13px] text-slate-600 mb-4"
                >
                  {pkg.description}
                </motion.p>
                <motion.div 
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 1 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[14px] font-semibold text-emerald-900"
                >
                  {pkg.price}
                </motion.div>
              </motion.div>
            );
          })}
        </div>
      </motion.div>
    </section>
  );
};

export default EventPackagesSection;