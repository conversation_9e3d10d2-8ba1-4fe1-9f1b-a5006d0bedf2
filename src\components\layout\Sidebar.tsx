import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { 
  Hotel, 
  LayoutDashboard, 
  Bed, 
  CalendarCheck, 
  Users, 
  CreditCard, 
  ClipboardList, 
  Utensils,
  UserCog,
  BarChart3,
  Settings,
  LogOut,
  ChevronRight,
  ChevronDown,
  Globe,
  X,
  Database,
  Wrench
} from 'lucide-react';

interface SidebarProps {
  userRole: string;
  onLogout: () => void;
  onClose?: () => void;
}

const navigationItems = [
  { icon: LayoutDashboard, label: 'Dashboard', path: '/dashboard' },
  { icon: Globe, label: 'Website Management', path: '/website-management' },
  { icon: Database, label: 'Master Tables', path: '/master', hasDropdown: true, subItems: [
    { label: 'Properties', path: '/master/properties' },
    { label: 'Room Types', path: '/master/room-types' },
    { label: 'Amenities', path: '/master/amenities' },
    { label: 'Rooms', path: '/master/rooms' },
  ]},
  { icon: Bed, label: 'Rooms', path: '/rooms' },
  { icon: CalendarCheck, label: 'Bookings', path: '/bookings' },
  { icon: Users, label: 'Guests', path: '/guests' },
  { icon: ClipboardList, label: 'Housekeeping', path: '/housekeeping' },
  { icon: ClipboardList, label: 'Tasks', path: '/tasks' },
  { icon: UserCog, label: 'Staff & HR', path: '/staff' },
  { icon: Utensils, label: 'Restaurant', path: '/restaurant' },
  { icon: Wrench, label: 'Maintenance', path: '/maintenance' },
  { icon: Users, label: 'Guest CRM', path: '/guest-crm' },
  { icon: Hotel, label: 'Multi-Property', path: '/multi-property' },
  { icon: CreditCard, label: 'OTA Manager', path: '/ota-manager' },
  { icon: CreditCard, label: 'Payments', path: '/payments' },
  { icon: BarChart3, label: 'Reports', path: '/reports' },
  { icon: Settings, label: 'Profile', path: '/profile' },
];

export const Sidebar: React.FC<SidebarProps> = ({ userRole, onLogout, onClose }) => {
  const [masterTablesOpen, setMasterTablesOpen] = useState(false);

  return (
    <div className="w-64 bg-card border-r shadow-card flex flex-col h-screen relative">
      {/* Mobile close button */}
      {onClose && (
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 rounded-lg hover:bg-accent lg:hidden"
        >
          <X className="h-5 w-5" />
        </button>
      )}
      {/* Logo Section */}
      <div className="p-6 border-b">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-primary rounded-lg">
            <Hotel className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">HotelManager</h1>
            <p className="text-sm text-muted-foreground capitalize">{userRole.replace('-', ' ')}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => (
            <li key={item.path}>
              {item.hasDropdown ? (
                <div>
                  <button
                    onClick={() => setMasterTablesOpen(!masterTablesOpen)}
                    className="flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group w-full text-left text-foreground hover:bg-accent hover:text-accent-foreground"
                  >
                    <item.icon className="h-5 w-5" />
                    <span className="font-medium">{item.label}</span>
                    {masterTablesOpen ? (
                      <ChevronDown className="h-4 w-4 ml-auto" />
                    ) : (
                      <ChevronRight className="h-4 w-4 ml-auto" />
                    )}
                  </button>
                  {masterTablesOpen && (
                    <ul className="ml-8 mt-2 space-y-1">
                      {item.subItems?.map((subItem) => (
                        <li key={subItem.path}>
                          <NavLink
                            to={subItem.path}
                            onClick={onClose}
                            className={({ isActive }) =>
                              cn(
                                "flex items-center px-4 py-2 rounded-lg transition-all duration-200 text-sm",
                                isActive
                                  ? "bg-primary text-primary-foreground shadow-card"
                                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                              )
                            }
                          >
                            {subItem.label}
                          </NavLink>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <NavLink
                  to={item.path}
                  onClick={onClose}
                  className={({ isActive }) =>
                    cn(
                      "flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group",
                      isActive
                        ? "bg-primary text-primary-foreground shadow-card"
                        : "text-foreground hover:bg-accent hover:text-accent-foreground"
                    )
                  }
                >
                  <item.icon className="h-5 w-5" />
                  <span className="font-medium">{item.label}</span>
                  <ChevronRight className="h-4 w-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
                </NavLink>
              )}
            </li>
          ))}
        </ul>
      </nav>

      {/* Logout */}
      <div className="p-4 border-t">
        <button
          onClick={onLogout}
          className="flex items-center space-x-3 px-4 py-3 rounded-lg w-full text-left text-destructive hover:bg-destructive/10 transition-all duration-200"
        >
          <LogOut className="h-5 w-5" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;