import React, { useState, useEffect } from 'react';
import { Plus, X, Upload, Star, StarOff, MoveUp, MoveDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface Image {
  id?: string;
  image_url: string;
  image_title?: string;
  image_description?: string;
  image_type?: string;
  display_order: number;
  is_primary: boolean;
}

interface ImageManagerProps {
  entityId?: string;
  entityType: 'property' | 'room' | 'room_type';
  images: Image[];
  onImagesChange: (images: Image[]) => void;
  imageTypes: { value: string; label: string }[];
}

const ImageManager: React.FC<ImageManagerProps> = ({
  entityId,
  entityType,
  images,
  onImagesChange,
  imageTypes
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newImage, setNewImage] = useState<Image>({
    image_url: '',
    image_title: '',
    image_description: '',
    image_type: imageTypes[0]?.value || '',
    display_order: images.length + 1,
    is_primary: images.length === 0
  });
  const { toast } = useToast();

  const getTableName = () => {
    switch (entityType) {
      case 'property': return 'property_images';
      case 'room': return 'room_images';
      case 'room_type': return 'room_type_images';
      default: return '';
    }
  };

  const getEntityColumn = () => {
    switch (entityType) {
      case 'property': return 'property_id';
      case 'room': return 'room_id';
      case 'room_type': return 'room_type_id';
      default: return '';
    }
  };

  const addImage = async () => {
    if (!newImage.image_url.trim()) {
      toast({
        title: 'Error',
        description: 'Image URL is required',
        variant: 'destructive',
      });
      return;
    }

    const imageToAdd = {
      ...newImage,
      display_order: images.length + 1,
      is_primary: images.length === 0 || newImage.is_primary
    };

    if (entityId) {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        const insertData = {
          ...imageToAdd,
          [getEntityColumn()]: entityId,
          created_by: user?.id,
          updated_by: user?.id,
        };

        const { data, error } = await supabase
          .from(getTableName())
          .insert([insertData])
          .select()
          .single();

        if (error) throw error;
        
        const updatedImages = [...images, data];
        onImagesChange(updatedImages);
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to add image',
          variant: 'destructive',
        });
        return;
      }
    } else {
      const updatedImages = [...images, { ...imageToAdd, id: `temp-${Date.now()}` }];
      onImagesChange(updatedImages);
    }

    setNewImage({
      image_url: '',
      image_title: '',
      image_description: '',
      image_type: imageTypes[0]?.value || '',
      display_order: images.length + 2,
      is_primary: false
    });
    setShowAddForm(false);

    toast({
      title: 'Success',
      description: 'Image added successfully',
    });
  };

  const removeImage = async (index: number) => {
    const imageToRemove = images[index];
    
    if (entityId && imageToRemove.id && !imageToRemove.id.startsWith('temp-')) {
      try {
        const { error } = await supabase
          .from(getTableName())
          .delete()
          .eq('id', imageToRemove.id);

        if (error) throw error;
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to remove image',
          variant: 'destructive',
        });
        return;
      }
    }

    const updatedImages = images.filter((_, i) => i !== index);
    onImagesChange(updatedImages);

    toast({
      title: 'Success',
      description: 'Image removed successfully',
    });
  };

  const setPrimary = async (index: number) => {
    const updatedImages = images.map((img, i) => ({
      ...img,
      is_primary: i === index
    }));

    if (entityId) {
      try {
        // Update all images to set is_primary = false
        await supabase
          .from(getTableName())
          .update({ is_primary: false })
          .eq(getEntityColumn(), entityId);

        // Set the selected image as primary
        const imageToUpdate = updatedImages[index];
        if (imageToUpdate.id && !imageToUpdate.id.startsWith('temp-')) {
          await supabase
            .from(getTableName())
            .update({ is_primary: true })
            .eq('id', imageToUpdate.id);
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to set primary image',
          variant: 'destructive',
        });
        return;
      }
    }

    onImagesChange(updatedImages);
  };

  const moveImage = (index: number, direction: 'up' | 'down') => {
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= images.length) return;

    const updatedImages = [...images];
    [updatedImages[index], updatedImages[newIndex]] = [updatedImages[newIndex], updatedImages[index]];
    
    // Update display orders
    updatedImages.forEach((img, i) => {
      img.display_order = i + 1;
    });

    onImagesChange(updatedImages);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Label className="text-base font-medium">Images</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowAddForm(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Image
        </Button>
      </div>

      {/* Existing Images */}
      <div className="grid gap-4">
        {images.map((image, index) => (
          <Card key={image.id || index} className="p-4">
            <div className="flex items-start gap-4">
              <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={image.image_url}
                  alt={image.image_title || 'Image'}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder.svg';
                  }}
                />
              </div>
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{image.image_title || 'Untitled'}</span>
                  {image.is_primary && (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  )}
                </div>
                <p className="text-sm text-gray-600">{image.image_type}</p>
                {image.image_description && (
                  <p className="text-sm text-gray-500">{image.image_description}</p>
                )}
              </div>
              <div className="flex flex-col gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setPrimary(index)}
                  disabled={image.is_primary}
                >
                  {image.is_primary ? (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  ) : (
                    <StarOff className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => moveImage(index, 'up')}
                  disabled={index === 0}
                >
                  <MoveUp className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => moveImage(index, 'down')}
                  disabled={index === images.length - 1}
                >
                  <MoveDown className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeImage(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add Image Form */}
      {showAddForm && (
        <Card className="p-4 border-dashed">
          <div className="space-y-4">
            <div>
              <Label htmlFor="image_url">Image URL *</Label>
              <Input
                id="image_url"
                value={newImage.image_url}
                onChange={(e) => setNewImage({ ...newImage, image_url: e.target.value })}
                placeholder="Enter image URL or path"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="image_title">Title</Label>
                <Input
                  id="image_title"
                  value={newImage.image_title}
                  onChange={(e) => setNewImage({ ...newImage, image_title: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="image_type">Type</Label>
                <Select
                  value={newImage.image_type}
                  onValueChange={(value) => setNewImage({ ...newImage, image_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {imageTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="image_description">Description</Label>
              <Input
                id="image_description"
                value={newImage.image_description}
                onChange={(e) => setNewImage({ ...newImage, image_description: e.target.value })}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
              >
                Cancel
              </Button>
              <Button type="button" onClick={addImage}>
                Add Image
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ImageManager;