import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Upload } from 'lucide-react';

interface ImageUploadFieldProps {
  value: string;
  onChange: (value: string) => void;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  uploading: boolean;
  placeholder?: string;
}

export const ImageUploadField: React.FC<ImageUploadFieldProps> = ({
  value,
  onChange,
  onFileUpload,
  uploading,
  placeholder = "Enter image URL or upload image"
}) => {
  const triggerFileUpload = () => {
    const input = document.getElementById('image-upload-input') as HTMLInputElement;
    input?.click();
  };

  return (
    <div className="flex items-center gap-2">
      <Input 
        value={value} 
        onChange={(e) => onChange(e.target.value)} 
        placeholder={placeholder} 
        className="flex-1" 
      />
      <input
        type="file"
        accept="image/*"
        onChange={onFileUpload}
        disabled={uploading}
        className="hidden"
        id="image-upload-input"
      />
      <Button 
        type="button" 
        disabled={uploading} 
        size="sm"
        onClick={triggerFileUpload}
      >
        {uploading ? (
          <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
        ) : (
          <Upload className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
};