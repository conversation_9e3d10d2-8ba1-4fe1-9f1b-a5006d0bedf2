import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import { FEATURE_FLAGS } from '@/utils/featureFlags';
import { 
  Search, 
  Filter, 
  Star, 
  Users, 
  Bed, 
  Wifi, 
  Car, 
  Coffee,
  Bath,
  Tv,
  Phone,
  MapPin,
  Calendar,
  ArrowRight,
  Heart,
  Building2,
  PartyPopper,
  Crown,
  Check,
  Snowflake,
  Tv2,
  ShowerHead,
  Sparkles,
  CheckCircle2,
  PhoneCall,
  Images,
  Utensils
} from 'lucide-react';

interface DbProperty {
  id: string;
  name: string;
  description: string;
  image_url: string;
  location: string;
  price_per_night: number;
  rating: number;
  amenities: string[];
  is_featured: boolean;
  is_active: boolean;
}

const Properties = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('recommended');
  const [filterType, setFilterType] = useState('all');
  const [priceRange, setPriceRange] = useState('all');
  const [dbProperties, setDbProperties] = useState<DbProperty[]>([]);
  const [loading, setLoading] = useState(true);
  const { isEnabled } = useFeatureFlags();

  useEffect(() => {
    fetchProperties();
  }, []);

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setDbProperties(data || []);
    } catch (error) {
      console.error('Error fetching properties:', error);
    } finally {
      setLoading(false);
    }
  };

  const properties = [
    {
      id: 1,
      title: "Standard Room",
      type: "hotel",
      description: "Cozy comfort with essential amenities, perfect for solo travelers or couples",
      price: 2999,
      originalPrice: 3499,
      rating: 4.8,
      reviews: 127,
      image: "https://images.unsplash.com/photo-1562790351-d273a961e0e9?q=80&w=1600&auto=format&fit=crop",
      amenities: ["WiFi", "AC", "Smart TV", "Minibar", "Room Service", "Daily Housekeeping"],
      capacity: "2 Adults",
      size: "280 sq ft",
      features: ["Queen Bed", "City View", "Work Desk", "Tea/Coffee Maker"],
      available: true,
      discount: 14
    },
    {
      id: 2,
      title: "Deluxe Room",
      type: "hotel",
      description: "Spacious elegance with premium amenities and modern comforts",
      price: 4499,
      originalPrice: 5299,
      rating: 4.9,
      reviews: 89,
      image: "https://images.unsplash.com/photo-1554995207-c18c203602cb?q=80&w=1600&auto=format&fit=crop",
      amenities: ["High-speed WiFi", "Tea/Coffee", "King Bed", "Rain Shower", "Work Desk", "Mini Fridge"],
      capacity: "2 Adults",
      size: "350 sq ft",
      features: ["King Size Bed", "Rain Shower", "Premium Linen", "Balcony"],
      available: true,
      discount: 15
    },
    {
      id: 3,
      title: "Luxury Suite",
      type: "hotel",
      description: "Ultimate comfort with separate living area and personalized service",
      price: 6999,
      originalPrice: 8499,
      rating: 4.9,
      reviews: 67,
      image: "https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&q=80",
      amenities: ["Butler Service", "Lounge Access", "Luxury Linen", "Soaking Tub", "Premium Minibar", "Concierge"],
      capacity: "2 Adults + 1 Child",
      size: "520 sq ft",
      features: ["Separate Living Area", "Soaking Tub", "Butler Service", "Premium Amenities"],
      available: true,
      discount: 18
    },
    {
      id: 4,
      title: "Grand Wedding Hall",
      type: "event",
      description: "Elegant banquet hall perfect for weddings and large celebrations",
      price: 85000,
      originalPrice: 100000,
      rating: 4.8,
      reviews: 45,
      image: "https://images.unsplash.com/photo-1522673607200-164d1b6ce486?q=80&w=1760&auto=format&fit=crop",
      amenities: ["Mandap Setup", "Audio System", "Stage", "Catering", "Decoration", "Valet Parking"],
      capacity: "300-500 Guests",
      size: "4500 sq ft",
      features: ["Professional Stage", "Bridal Suite", "Kitchen Access", "Photography Area"],
      available: true,
      discount: 15
    },
    {
      id: 5,
      title: "Corporate Event Hall",
      type: "event",
      description: "Professional venue for conferences, meetings, and corporate events",
      price: 25000,
      originalPrice: 30000,
      rating: 4.7,
      reviews: 34,
      image: "https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&w=1760&auto=format&fit=crop",
      amenities: ["AV Equipment", "High-speed WiFi", "Business Lunch", "Parking", "Meeting Rooms", "Accommodation"],
      capacity: "50-200 Guests",
      size: "2500 sq ft",
      features: ["Theatre Seating", "Presentation Equipment", "Coffee Service", "Business Center"],
      available: true,
      discount: 17
    },
    {
      id: 6,
      title: "Garden Party Venue",
      type: "event",
      description: "Beautiful outdoor space for intimate celebrations and social gatherings",
      price: 35000,
      originalPrice: 42000,
      rating: 4.6,
      reviews: 72,
      image: "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&w=1760&auto=format&fit=crop",
      amenities: ["Garden Setting", "Gazebo", "Decoration", "Catering", "Music System", "Lighting"],
      capacity: "100-250 Guests",
      size: "3500 sq ft",
      features: ["Lush Garden", "Gazebo Setup", "Outdoor Kitchen", "String Lights"],
      available: true,
      discount: 17
    }
  ];

  // Use only database properties, fallback to static only if no database properties
  const displayProperties = dbProperties.length > 0 ? dbProperties.map(p => ({
    ...p,
    title: p.name,
    price: p.price_per_night,
    type: 'hotel',
    available: true,
    capacity: '2 Adults',
    size: '350 sq ft',
  
    reviews: Math.floor(Math.random() * 100) + 20
  })) : properties;

  const filteredProperties = displayProperties.filter(property => {
    const title = property.name || property.title;
    const price = property.price_per_night || property.price;
    const matchesSearch = title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || (property.type && property.type === filterType);
    const matchesPrice = priceRange === 'all' || 
                        (priceRange === 'budget' && price <= 5000) ||
                        (priceRange === 'mid' && price > 5000 && price <= 30000) ||
                        (priceRange === 'luxury' && price > 30000);
    
    return matchesSearch && matchesType && matchesPrice;
  });

  const sortedProperties = filteredProperties.sort((a, b) => {
    const priceA = a.price_per_night || a.price;
    const priceB = b.price_per_night || b.price;
    const ratingA = a.rating || 4.5;
    const ratingB = b.rating || 4.5;
    
    switch (sortBy) {
      case 'price-low':
        return priceA - priceB;
      case 'price-high':
        return priceB - priceA;
      case 'rating':
        return ratingB - ratingA;
      default:
        return ratingB - ratingA;
    }
  });

  return (
    <div className="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900 min-h-screen" style={{ fontFamily: "'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'", scrollBehavior: 'smooth' }}>
      {/* Hero Section */}
      <section className="relative">
        <div className="relative h-[60vh] sm:h-[70vh] w-full overflow-hidden">
          <motion.img
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 w-full h-full object-cover"
            src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1760&auto=format&fit=crop"
            alt="Luxury Accommodation"
          />
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute inset-0 bg-gradient-to-b from-emerald-950/70 via-emerald-950/50 to-emerald-950/70"
          ></motion.div>
          <div className="relative z-10 h-full">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4 sm:space-y-6">
                  <motion.div 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-white/10 backdrop-blur-sm ring-1 ring-white/20"
                  >
                    <Building2 className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-amber-300" />
                    <span className="text-white/90 text-xs sm:text-sm font-medium">Our Properties</span>
                  </motion.div>
                  <motion.h1 
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 1, delay: 1, ease: "easeOut" }}
                    className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold leading-[1.1] tracking-tight text-white px-4 sm:px-0" 
                    style={{ fontFamily: "'Cormorant Garamond', serif" }}
                  >
                    Find Your Perfect <span className="text-amber-300">Stay or Venue</span>
                  </motion.h1>
                  <motion.p 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 1.3, ease: "easeOut" }}
                    className="text-sm sm:text-base lg:text-lg xl:text-xl text-white/90 leading-relaxed max-w-3xl mx-auto px-4 sm:px-0"
                  >
                    Choose from our luxury rooms and suites or book our elegant event venues for your special occasions
                  </motion.p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="bg-white border-b border-slate-200">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6 sm:py-8"
        >
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex flex-col lg:flex-row gap-3 sm:gap-4 items-stretch lg:items-center"
          >
            <motion.div 
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="relative flex-1 max-w-full lg:max-w-md"
            >
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-500" />
              <input
                type="text"
                placeholder="Search properties..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 sm:py-3 rounded-lg ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none text-slate-900 placeholder-slate-500 text-sm sm:text-base"
              />
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="flex gap-2 sm:gap-3 flex-wrap"
            >
              <select 
                value={priceRange} 
                onChange={(e) => setPriceRange(e.target.value)}
                className="px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none text-slate-900 bg-white min-w-[120px] sm:min-w-[140px] text-sm sm:text-base"
              >
                <option value="all">All Prices</option>
                <option value="budget">Under ₹5,000</option>
                <option value="mid">₹5,000 - ₹30,000</option>
                <option value="luxury">Above ₹30,000</option>
              </select>

              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none text-slate-900 bg-white min-w-[120px] sm:min-w-[140px] text-sm sm:text-base"
              >
                <option value="recommended">Recommended</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
              </select>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>

      {/* Properties Grid */}
      <section className="bg-slate-50">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16"
        >
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex justify-between items-center mb-8 sm:mb-12"
          >
            <div>
              <motion.h2 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-emerald-950 font-semibold" 
                style={{ fontFamily: "'Cormorant Garamond', serif" }}
              >
                Available Properties
              </motion.h2>
              <motion.p 
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.4 }}
                viewport={{ once: true }}
                className="mt-2 text-sm sm:text-base text-slate-600"
              >
                {sortedProperties.length} properties found
              </motion.p>
            </div>
          </motion.div>

          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 laptop-sm:grid-cols-3 gap-4 sm:gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <motion.div 
                  key={i} 
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.6, delay: i * 0.1, ease: "easeOut" }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 animate-pulse"
                >
                  <div className="h-40 sm:h-48 bg-slate-200"></div>
                  <div className="p-4 sm:p-5 space-y-3">
                    <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                    <div className="h-3 bg-slate-200 rounded w-full"></div>
                    <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 laptop-sm:grid-cols-3 gap-4 sm:gap-6">
              {sortedProperties.map((property, index) => {
                const title = property.title || property.name;
                const price = property.price || property.price_per_night;
                const rating = property.rating || 4.5;
                const reviews = property.reviews || Math.floor(Math.random() * 100) + 20;
                const available = property.available !== false;
                
                return (
                <motion.div 
                  key={property.id} 
                  initial={{ opacity: 0, y: 40, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1, ease: "easeOut" }}
                  viewport={{ once: true }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className={`bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300 ${!available ? 'opacity-75' : ''}`}
                >
                <div className="relative">
                  <motion.img 
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.4 }}
                    src={property.image_url || property.image} 
                    alt={title}
                    className="h-40 sm:h-48 w-full object-cover"
                  />
                  {(property.discount_percentage || property.discount) > 0 && (
                    <motion.div 
                      initial={{ scale: 0, rotate: -180 }}
                      whileInView={{ scale: 1, rotate: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      viewport={{ once: true }}
                      className="absolute top-3 left-3 px-2 py-1 rounded-full bg-red-500 text-white text-[12px] font-medium"
                    >
                      {property.discount_percentage || property.discount}% OFF
                    </motion.div>
                  )}
                  <motion.button 
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    className="absolute top-3 right-3 h-8 w-8 rounded-full bg-white/90 hover:bg-white flex items-center justify-center transition"
                  >
                    <Heart className="w-4 h-4 text-slate-600" />
                  </motion.button>
                  {!property.available && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="px-4 py-2 rounded-full bg-white/90 text-slate-900 text-sm font-medium">
                        Not Available
                      </div>
                    </div>
                  )}
                </div>

                <div className="p-4 sm:p-5">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] font-medium mb-2">
                        {(property.property_type || property.type) === 'hotel' ? (
                          <><Building2 className="w-3 h-3" /> Hotel Room</>
                        ) : (
                          <><PartyPopper className="w-3 h-3" /> Event Venue</>
                        )}
                      </div>
                      <h3 className="text-[18px] font-semibold text-emerald-950">{title}</h3>
                    </div>
                  </div>
                  <p className="text-[13px] text-slate-600 mb-3">{property.description}</p>
                  
                  <div className="flex items-center gap-2 text-[13px] mb-4">
                    <Star className="w-4 h-4 fill-amber-400 text-amber-400" />
                    <span className="font-medium text-slate-900">{rating}</span>
                    <span className="text-slate-500">({reviews} reviews)</span>
                  </div>

                  {/* Key Info */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-[13px] mb-4">
                    <div className="flex items-center gap-2">
                      <Users className="w-3.5 h-3.5 text-slate-500 flex-shrink-0" />
                      <span className="text-slate-700 truncate">{property.capacity || property.location}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-3.5 h-3.5 text-slate-500 flex-shrink-0" />
                      <span className="text-slate-700 truncate">{property.size}</span>
                    </div>
                  </div>

                  {/* Amenities */}
                  <div className="flex flex-wrap gap-1.5 mb-4">
                    {(property.amenities || []).slice(0, 3).map((amenity, idx) => (
                      <span key={idx} className="px-2 py-1 rounded-full bg-slate-100 text-slate-700 text-[11px] font-medium">
                        {amenity}
                      </span>
                    ))}
                    {(property.amenities || []).length > 3 && (
                      <span className="px-2 py-1 rounded-full bg-slate-100 text-slate-700 text-[11px] font-medium">
                        +{(property.amenities || []).length - 3} more
                      </span>
                    )}
                  </div>

                  {/* Features */}
                  <div className="space-y-1 mb-4">
                    {(property.features || []).slice(0, 2).map((feature, idx) => (
                      <div key={idx} className="flex items-center gap-2 text-[12px] text-slate-600">
                        <Check className="w-3 h-3 text-emerald-700" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Pricing */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-end justify-between pt-4 border-t border-slate-200 gap-3 sm:gap-0">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="text-lg sm:text-xl font-semibold text-emerald-900">
                          ₹{price.toLocaleString()}
                        </span>
                        {(property.original_price || property.originalPrice) && (property.original_price || property.originalPrice) > price && (
                          <span className="text-xs sm:text-[13px] text-slate-500 line-through">
                            ₹{(property.original_price || property.originalPrice).toLocaleString()}
                          </span>
                        )}
                      </div>
                      <span className="text-[10px] sm:text-[11px] text-slate-500">
                        per {(property.property_type || property.type) === 'hotel' ? 'night' : 'event'}
                      </span>
                    </div>
                    
                    <div className="flex gap-2 w-full sm:w-auto">
                      {isEnabled(FEATURE_FLAGS.PROPERTY_BOOKING_ENABLED) ? (
                        <>
                          <Link to={`/property/${property.id}`} className="flex-1 sm:flex-none px-3 py-1.5 rounded-full text-xs sm:text-[12px] text-emerald-900 ring-1 ring-slate-300 hover:ring-emerald-300 transition text-center">
                            Details
                          </Link>
                          {available && (
                            <Link to="/contact" className="flex-1 sm:flex-none inline-flex items-center justify-center gap-1 px-3 py-1.5 rounded-full text-xs sm:text-[12px] text-white bg-emerald-900 hover:bg-emerald-800 transition">
                              Book
                              <ArrowRight className="w-3 h-3" />
                            </Link>
                          )}
                        </>
                      ) : (
                        <div className="w-full sm:w-auto px-3 py-1.5 rounded-full text-xs sm:text-[12px] text-white bg-slate-500 cursor-not-allowed text-center">
                          Coming Soon
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
                );
              })}
            </div>
          )}

          {sortedProperties.length === 0 && (
            <motion.div 
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center py-16 col-span-full"
            >
              <div className="max-w-md mx-auto">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <Search className="h-12 w-12 text-slate-400 mx-auto mb-6" />
                </motion.div>
                <motion.h3 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="text-xl font-semibold text-emerald-950 mb-3"
                >
                  No properties found
                </motion.h3>
                <motion.p 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.4 }}
                  viewport={{ once: true }}
                  className="text-slate-600 mb-6"
                >
                  Try adjusting your search criteria or filters to find more properties.
                </motion.p>
                <motion.button 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    setSearchTerm('');
                    setFilterType('all');
                    setPriceRange('all');
                  }}
                  className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[14px] text-white bg-emerald-900 hover:bg-emerald-800 transition"
                >
                  Clear Filters
                </motion.button>
              </div>
            </motion.div>
          )}
        </motion.div>
      </section>

      {/* CTA Section */}
      <section className="bg-emerald-950">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16 text-center"
        >
          <motion.h2 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-white font-semibold" 
            style={{ fontFamily: "'Cormorant Garamond', serif" }}
          >
            Need Help Choosing?
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-3 text-sm sm:text-base text-white/85 max-w-2xl mx-auto px-4 sm:px-0"
          >
            Our team is here to help you find the perfect room or venue for your needs. Contact us for personalized recommendations and special packages.
          </motion.p>
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-6 sm:mt-8 flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4"
          >
            <motion.a 
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              href="tel:+918740027008" 
              className="w-full sm:w-auto inline-flex items-center justify-center gap-2 rounded-full px-5 sm:px-6 py-3 text-sm sm:text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500"
            >
              <Phone className="w-4 sm:w-5 h-4 sm:h-5" />
              Call: +91 87400-27008
            </motion.a>
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link to="/services" className="w-full sm:w-auto inline-flex items-center justify-center gap-2 rounded-full px-5 sm:px-6 py-3 text-sm sm:text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                View All Services
                <ArrowRight className="w-4 sm:w-5 h-4 sm:h-5" />
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>
    </div>
  );
};

export default Properties;