import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Link, Wifi, Calendar, CreditCard, Monitor, BarChart3 } from 'lucide-react';

const OTAManager = () => {
  const [selectedTab, setSelectedTab] = useState('channels');

  const channels = [
    { id: 'OTA001', name: 'Booking.com', type: 'OTA', status: 'Connected', lastSync: '2024-01-15 10:30', commission: '15%', bookings: 45 },
    { id: 'OTA002', name: 'Expedia', type: 'OTA', status: 'Connected', lastSync: '2024-01-15 10:25', commission: '18%', bookings: 32 },
    { id: 'OTA003', name: 'Agoda', type: 'OTA', status: 'Disconnected', lastSync: '2024-01-14 15:45', commission: '20%', bookings: 0 },
    { id: 'OTA004', name: 'Airbnb', type: 'Alternative', status: 'Connected', lastSync: '2024-01-15 09:15', commission: '12%', bookings: 18 },
  ];

  const syncLogs = [
    { timestamp: '2024-01-15 10:30:00', channel: 'Booking.com', type: 'Rate Push', status: 'Success', details: 'Updated 50 room rates' },
    { timestamp: '2024-01-15 10:25:00', channel: 'Expedia', type: 'Availability Push', status: 'Success', details: 'Updated availability for next 30 days' },
    { timestamp: '2024-01-15 09:15:00', channel: 'Airbnb', type: 'Booking Pull', status: 'Success', details: 'Retrieved 3 new bookings' },
    { timestamp: '2024-01-14 15:45:00', channel: 'Agoda', type: 'Rate Push', status: 'Failed', details: 'Connection timeout error' },
  ];

  const bookings = [
    { id: 'BK001', channel: 'Booking.com', guest: 'John Smith', room: 'Deluxe Double', checkin: '2024-01-20', checkout: '2024-01-22', amount: '$240', status: 'Confirmed' },
    { id: 'BK002', channel: 'Expedia', guest: 'Sarah Wilson', room: 'Standard Single', checkin: '2024-01-18', checkout: '2024-01-20', amount: '$180', status: 'Confirmed' },
    { id: 'BK003', channel: 'Airbnb', guest: 'Mike Johnson', room: 'Suite', checkin: '2024-01-25', checkout: '2024-01-28', amount: '$450', status: 'Pending' },
  ];

  const payments = [
    { id: 'VCC001', booking: 'BK001', channel: 'Booking.com', cardNumber: '**** **** **** 1234', amount: '$240', status: 'Processed', date: '2024-01-15' },
    { id: 'VCC002', booking: 'BK002', channel: 'Expedia', cardNumber: '**** **** **** 5678', amount: '$180', status: 'Processed', date: '2024-01-14' },
    { id: 'VCC003', booking: 'BK003', channel: 'Airbnb', cardNumber: '**** **** **** 9012', amount: '$450', status: 'Pending', date: '2024-01-13' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Connected': return 'default';
      case 'Disconnected': return 'destructive';
      case 'Success': return 'default';
      case 'Failed': return 'destructive';
      case 'Confirmed': return 'default';
      case 'Pending': return 'secondary';
      case 'Processed': return 'default';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">OTA & Channel Manager</h1>
          <p className="text-muted-foreground">Manage online travel agency integrations and bookings</p>
        </div>
        <Button>
          <Link className="mr-2 h-4 w-4" />
          Add Channel
        </Button>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="sync">Rates & Sync</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="channels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Channel Connections
              </CardTitle>
              <CardDescription>Manage OTA channel connections and status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <Input placeholder="Search channels..." className="max-w-sm" />
                <Button variant="outline">Filter</Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Channel ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Sync</TableHead>
                    <TableHead>Commission</TableHead>
                    <TableHead>Monthly Bookings</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {channels.map((channel) => (
                    <TableRow key={channel.id}>
                      <TableCell className="font-medium">{channel.id}</TableCell>
                      <TableCell>{channel.name}</TableCell>
                      <TableCell>{channel.type}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(channel.status)}>{channel.status}</Badge>
                      </TableCell>
                      <TableCell>{channel.lastSync}</TableCell>
                      <TableCell>{channel.commission}</TableCell>
                      <TableCell>{channel.bookings}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sync" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                Rates & Availability Sync
              </CardTitle>
              <CardDescription>Monitor rate and availability synchronization logs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <Button>Push Rates</Button>
                <Button variant="outline">Push Availability</Button>
                <Button variant="outline">Sync All</Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Channel</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {syncLogs.map((log, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{log.timestamp}</TableCell>
                      <TableCell>{log.channel}</TableCell>
                      <TableCell>{log.type}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(log.status)}>{log.status}</Badge>
                      </TableCell>
                      <TableCell>{log.details}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                OTA Bookings
              </CardTitle>
              <CardDescription>Manage reservations from online travel agencies</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Booking ID</TableHead>
                    <TableHead>Channel</TableHead>
                    <TableHead>Guest</TableHead>
                    <TableHead>Room Type</TableHead>
                    <TableHead>Check-in</TableHead>
                    <TableHead>Check-out</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell className="font-medium">{booking.id}</TableCell>
                      <TableCell>{booking.channel}</TableCell>
                      <TableCell>{booking.guest}</TableCell>
                      <TableCell>{booking.room}</TableCell>
                      <TableCell>{booking.checkin}</TableCell>
                      <TableCell>{booking.checkout}</TableCell>
                      <TableCell>{booking.amount}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(booking.status)}>{booking.status}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Virtual Card Payments
              </CardTitle>
              <CardDescription>Track virtual card transactions from OTA bookings</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>VCC ID</TableHead>
                    <TableHead>Booking</TableHead>
                    <TableHead>Channel</TableHead>
                    <TableHead>Card Number</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.id}</TableCell>
                      <TableCell>{payment.booking}</TableCell>
                      <TableCell>{payment.channel}</TableCell>
                      <TableCell>
                        <code className="bg-muted px-2 py-1 rounded text-sm">{payment.cardNumber}</code>
                      </TableCell>
                      <TableCell>{payment.amount}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(payment.status)}>{payment.status}</Badge>
                      </TableCell>
                      <TableCell>{payment.date}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Monitoring & Logs
              </CardTitle>
              <CardDescription>System monitoring and integration health</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Active Channels</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">3/4</div>
                    <p className="text-xs text-muted-foreground">Connected channels</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Sync Success</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">96%</div>
                    <p className="text-xs text-muted-foreground">Successful syncs</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Daily Bookings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">12</div>
                    <p className="text-xs text-muted-foreground">OTA bookings today</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Response Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2.3s</div>
                    <p className="text-xs text-muted-foreground">Average API response</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Channel Performance Reports
              </CardTitle>
              <CardDescription>Analytics and performance metrics by channel</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Total Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">$12,850</div>
                    <p className="text-xs text-muted-foreground">This month</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Top Channel</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">Booking.com</div>
                    <p className="text-xs text-muted-foreground">45 bookings</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Avg Commission</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">16.3%</div>
                    <p className="text-xs text-muted-foreground">Weighted average</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Conversion Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">3.2%</div>
                    <p className="text-xs text-muted-foreground">Views to bookings</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OTAManager;