import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import TeamSection from '@/components/website/TeamSection';
import ServicesSection from '@/components/website/ServicesSection';
import {
  Users,
  Target,
  Eye,
  Award,
  Clock,
  Star,
  ChefHat,
  Bed,
  PartyPopper,
  MapPin,
  Calendar,
  Check,
  Building2,
  Utensils,
  Heart,
  Leaf,
  Shield,
  Lightbulb
} from 'lucide-react';

const AboutUs = () => {
  const stats = [
    { icon: Clock, label: "Years of Excellence", value: "10+" },
    { icon: Users, label: "Happy Guests", value: "50,000+" },
    { icon: Star, label: "Average Rating", value: "4.9/5" },
    { icon: Award, label: "Awards Won", value: "15+" }
  ];



  const milestones = [
    { year: "2016", event: "Meadow De Jalsa established on Bardoli–Vyara Highway" },
    { year: "2018", event: "Expanded luxury accommodation and signature dining" },
    { year: "2020", event: "Added premium event venues and banquet facilities" },
    { year: "2022", event: "Received 'Best Luxury Hotel' award in Gujarat" },
    { year: "2023", event: "Launched digital transformation and online services" },
    { year: "2024", event: "Completed eco-friendly renovation and sustainability initiatives" }
  ];

  const values = [
    { icon: Heart, title: "Guest-Centric Service", desc: "Every decision we make puts our guests' comfort and satisfaction first" },
    { icon: Leaf, title: "Environmental Responsibility", desc: "Committed to sustainable practices and eco-friendly operations" },
    { icon: Shield, title: "Quality Assurance", desc: "Uncompromising standards in food, service, and accommodation" },
    { icon: Lightbulb, title: "Innovation", desc: "Continuously evolving to exceed expectations and embrace new trends" }
  ];

  return (
    <div className="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900 min-h-screen" style={{ fontFamily: "'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'", scrollBehavior: 'smooth' }}>
      {/* Hero Section */}
      <section className="relative">
        <div className="relative h-[70vh] w-full overflow-hidden">
          <motion.img
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 w-full h-full object-cover"
            src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?q=80&w=1760&auto=format&fit=crop"
            alt="Meadow De Jalsa Property"
          />
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute inset-0 bg-gradient-to-b from-emerald-950/70 via-emerald-950/50 to-emerald-950/70"
          ></motion.div>
          <div className="relative z-10 h-full">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-6">
                  <motion.div 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm ring-1 ring-white/20"
                  >
                    <MapPin className="w-4 h-4 text-amber-300" />
                    <span className="text-white/90 text-sm font-medium">Our Story</span>
                  </motion.div>
                  <motion.h1 
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 1, delay: 1, ease: "easeOut" }}
                    className="text-4xl sm:text-5xl lg:text-6xl font-semibold leading-[1.1] tracking-tight text-white" 
                    style={{ fontFamily: "'Cormorant Garamond', serif" }}
                  >
                    About <span className="text-amber-300">Meadow De Jalsa</span>
                  </motion.h1>
                  <motion.p 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 1.3, ease: "easeOut" }}
                    className="text-lg sm:text-xl text-white/90 leading-relaxed max-w-3xl mx-auto"
                  >
                    A decade of luxury hospitality, culinary excellence, and memorable experiences on the Bardoli–Vyara Highway
                  </motion.p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-white border-b border-slate-200">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
        >
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div 
                key={index} 
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.05 }}
                className="text-center"
              >
                <motion.div 
                  initial={{ scale: 0, rotate: -180 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 + (index * 0.1), ease: "easeOut" }}
                  viewport={{ once: true }}
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mx-auto mb-4"
                >
                  <stat.icon className="w-6 h-6" />
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, scale: 0.5 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 0.4 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-2xl sm:text-3xl font-semibold text-emerald-950 mb-2"
                >
                  {stat.value}
                </motion.div>
                <motion.p 
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-sm text-slate-600 font-medium"
                >
                  {stat.label}
                </motion.p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* Our Story */}
      <section className="bg-slate-50">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
        >
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <motion.h2 
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold mb-6" 
                style={{ fontFamily: "'Cormorant Garamond', serif" }}
              >
                Our <span className="text-amber-600">Journey</span>
              </motion.h2>
              <div className="space-y-6 text-slate-600">
                <motion.p 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  viewport={{ once: true }}
                  className="text-lg leading-relaxed"
                >
                  Meadow De Jalsa began as a vision to create a luxury hospitality destination that seamlessly blends modern comfort with traditional warmth. Located strategically on the Bardoli–Vyara Highway, we've become a preferred choice for discerning travelers and event hosts.
                </motion.p>
                <motion.p 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  viewport={{ once: true }}
                  className="leading-relaxed"
                >
                  Our commitment to excellence extends beyond accommodation to encompass world-class dining, state-of-the-art event facilities, and personalized service that creates lasting memories. Every detail is carefully curated to exceed expectations.
                </motion.p>
                <motion.p 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  viewport={{ once: true }}
                  className="leading-relaxed"
                >
                  Today, we stand as a testament to the power of vision, dedication, and unwavering commitment to quality. We continue to evolve while staying true to our core values of hospitality, sustainability, and community engagement.
                </motion.p>
              </div>
            </motion.div>
            <motion.div 
              initial={{ opacity: 0, x: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, x: 0, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
              className="relative"
            >
              <motion.img
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.4 }}
                src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&w=1600&auto=format&fit=crop"
                alt="Hotel Interior"
                className="w-full h-96 object-cover rounded-2xl ring-1 ring-slate-200"
              />
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Mission, Vision, Values */}
      <section className="bg-white">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
        >
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Our Foundation</h2>
            <p className="mt-3 text-slate-600 max-w-2xl mx-auto">
              The principles that guide everything we do at Meadow De Jalsa
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <motion.div 
              initial={{ opacity: 0, x: -50, scale: 0.9 }}
              whileInView={{ opacity: 1, x: 0, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.3, ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.02 }}
              className="bg-slate-50 rounded-2xl p-8 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
            >
              <motion.div 
                initial={{ scale: 0, rotate: -180 }}
                whileInView={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                viewport={{ once: true }}
                whileHover={{ rotate: 360, scale: 1.1 }}
                className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mb-6"
              >
                <Target className="w-6 h-6" />
              </motion.div>
              <motion.h3 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.6 }}
                viewport={{ once: true }}
                className="text-xl font-semibold text-emerald-950 mb-4"
              >
                Our Mission
              </motion.h3>
              <motion.p 
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.7 }}
                viewport={{ once: true }}
                className="text-slate-600 leading-relaxed"
              >
                To provide exceptional hospitality experiences through luxury accommodation, world-class dining, and memorable events while fostering sustainable practices and community engagement.
              </motion.p>
            </motion.div>

            <motion.div 
              initial={{ opacity: 0, x: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, x: 0, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.02 }}
              className="bg-slate-50 rounded-2xl p-8 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
            >
              <motion.div 
                initial={{ scale: 0, rotate: -180 }}
                whileInView={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                viewport={{ once: true }}
                whileHover={{ rotate: 360, scale: 1.1 }}
                className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mb-6"
              >
                <Eye className="w-6 h-6" />
              </motion.div>
              <motion.h3 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.7 }}
                viewport={{ once: true }}
                className="text-xl font-semibold text-emerald-950 mb-4"
              >
                Our Vision
              </motion.h3>
              <motion.p 
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.8 }}
                viewport={{ once: true }}
                className="text-slate-600 leading-relaxed"
              >
                To be Gujarat's premier luxury hospitality destination, recognized for our commitment to excellence, innovation, and creating unforgettable experiences for every guest.
              </motion.p>
            </motion.div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <motion.div 
                key={index} 
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.5 + (index * 0.1), ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.05 }}
                className="text-center p-6 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
              >
                <motion.div 
                  initial={{ scale: 0, rotate: -180 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 + (index * 0.1) }}
                  viewport={{ once: true }}
                  whileHover={{ rotate: 360, scale: 1.2 }}
                  className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mx-auto mb-4"
                >
                  <value.icon className="w-6 h-6" />
                </motion.div>
                <motion.h4 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.8 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[16px] font-semibold text-emerald-950 mb-2"
                >
                  {value.title}
                </motion.h4>
                <motion.p 
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.9 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[13px] text-slate-600 leading-relaxed"
                >
                  {value.desc}
                </motion.p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* Timeline */}
      <section className="bg-emerald-950">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
        >
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl tracking-tight text-white font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Our Journey Through Time</h2>
            <p className="mt-3 text-white/85 max-w-2xl mx-auto">
              Key milestones that shaped Meadow De Jalsa into what it is today
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <motion.div 
                  key={index} 
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50, scale: 0.9 }}
                  whileInView={{ opacity: 1, x: 0, scale: 1 }}
                  transition={{ duration: 0.7, delay: index * 0.1, ease: "easeOut" }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="flex items-start gap-6"
                >
                  <motion.div 
                    initial={{ scale: 0, rotate: -180 }}
                    whileInView={{ scale: 1, rotate: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 + (index * 0.1) }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.1, rotate: 360 }}
                    className="flex-shrink-0"
                  >
                    <div className="w-12 h-12 bg-amber-400 rounded-full flex items-center justify-center text-emerald-950 font-bold text-sm">
                      {milestone.year.slice(-2)}
                    </div>
                  </motion.div>
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 + (index * 0.1) }}
                    viewport={{ once: true }}
                    whileHover={{ y: -2 }}
                    className="bg-white/5 ring-1 ring-white/10 rounded-2xl p-6 flex-1 hover:bg-white/10 hover:ring-white/20 transition-all duration-300"
                  >
                    <motion.h4 
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.5 + (index * 0.1) }}
                      viewport={{ once: true }}
                      className="text-lg font-semibold text-white mb-2"
                    >
                      {milestone.year}
                    </motion.h4>
                    <motion.p 
                      initial={{ opacity: 0, x: -15 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.6 + (index * 0.1) }}
                      viewport={{ once: true }}
                      className="text-white/85"
                    >
                      {milestone.event}
                    </motion.p>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </section>

      {/* Team Section */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <TeamSection />
      </motion.div>

      {/* Services Overview */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <ServicesSection />
      </motion.div>

      {/* Location */}
      {/* <section className="bg-slate-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Find Us</h2>
            <p className="mt-3 text-slate-600">
              Conveniently located on the Bardoli–Vyara Highway
            </p>
          </div>
          
          <div className="max-w-2xl mx-auto bg-white rounded-2xl p-8 ring-1 ring-slate-200">
            <div className="text-center">
              <div className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mx-auto mb-6">
                <MapPin className="w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold text-emerald-950 mb-4">Our Location</h3>
              <p className="text-lg text-slate-700 mb-6">
                Bardoli–Vyara Highway<br />
                Bardoli, Gujarat 394601
              </p>
              <div className="grid sm:grid-cols-3 gap-4 text-sm text-slate-600">
                <div className="flex items-center justify-center gap-2">
                  <span>📞</span>
                  <span>+91 87400-27008</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>⏰</span>
                  <span>24/7 Service</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>🚗</span>
                  <span>Ample Parking</span>
                </div>
              </div>
              <div className="mt-8">
                <Link to="/contact" className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[14px] text-white bg-emerald-900 hover:bg-emerald-800 transition">
                  <Calendar className="w-4 h-4" />
                  Get Directions
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section> */}
    </div>
  );
};

export default AboutUs;