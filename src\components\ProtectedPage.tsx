import React from 'react';
import { Navigate } from 'react-router-dom';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import { FeatureFlagKey } from '@/utils/featureFlags';

interface ProtectedPageProps {
  children: React.ReactNode;
  flagKey: FeatureFlagKey;
  redirectTo?: string;
}

export const ProtectedPage: React.FC<ProtectedPageProps> = ({ 
  children, 
  flagKey, 
  redirectTo = '/' 
}) => {
  const { isEnabled, loading } = useFeatureFlags();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-900 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isEnabled(flagKey)) {
    return <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};