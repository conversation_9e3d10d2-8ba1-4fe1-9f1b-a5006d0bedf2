import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Utensils, 
  Search, 
  Filter,
  Plus,
  Eye,
  Edit,
  Clock,
  User,
  MapPin,
  DollarSign,
  CheckCircle,
  Timer
} from 'lucide-react';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: 'appetizer' | 'main' | 'dessert' | 'beverage' | 'special';
  available: boolean;
  preparationTime: number; // in minutes
  ingredients: string[];
  allergens: string[];
}

interface Order {
  id: string;
  orderNumber: string;
  type: 'room-service' | 'restaurant' | 'bar';
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  customer: string;
  roomNumber?: string;
  tableNumber?: string;
  items: {
    menuItem: MenuItem;
    quantity: number;
    specialInstructions?: string;
  }[];
  subtotal: number;
  tax: number;
  total: number;
  orderTime: string;
  estimatedTime?: string;
  deliveredTime?: string;
}

const mockMenuItems: MenuItem[] = [
  {
    id: '1',
    name: 'Grilled Salmon',
    description: 'Fresh Atlantic salmon with lemon herb butter',
    price: 28.00,
    category: 'main',
    available: true,
    preparationTime: 20,
    ingredients: ['Salmon', 'Lemon', 'Herbs', 'Butter'],
    allergens: ['Fish']
  },
  {
    id: '2',
    name: 'Caesar Salad',
    description: 'Crisp romaine lettuce with parmesan and croutons',
    price: 14.00,
    category: 'appetizer',
    available: true,
    preparationTime: 10,
    ingredients: ['Romaine', 'Parmesan', 'Croutons', 'Caesar Dressing'],
    allergens: ['Dairy', 'Gluten']
  },
  {
    id: '3',
    name: 'Chocolate Cake',
    description: 'Rich chocolate cake with vanilla ice cream',
    price: 12.00,
    category: 'dessert',
    available: false,
    preparationTime: 5,
    ingredients: ['Chocolate', 'Flour', 'Vanilla Ice Cream'],
    allergens: ['Dairy', 'Gluten', 'Eggs']
  }
];

const mockOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    type: 'room-service',
    status: 'preparing',
    customer: 'John Smith',
    roomNumber: '205',
    items: [
      {
        menuItem: mockMenuItems[0],
        quantity: 1,
        specialInstructions: 'No lemon please'
      },
      {
        menuItem: mockMenuItems[1],
        quantity: 1
      }
    ],
    subtotal: 42.00,
    tax: 3.36,
    total: 45.36,
    orderTime: '2024-01-15T18:30:00Z',
    estimatedTime: '19:00'
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    type: 'restaurant',
    status: 'ready',
    customer: 'Emma Wilson',
    tableNumber: '12',
    items: [
      {
        menuItem: mockMenuItems[1],
        quantity: 2
      }
    ],
    subtotal: 28.00,
    tax: 2.24,
    total: 30.24,
    orderTime: '2024-01-15T19:15:00Z',
    estimatedTime: '19:30'
  }
];

const categoryColors = {
  appetizer: 'bg-green-100 text-green-800',
  main: 'bg-blue-100 text-blue-800',
  dessert: 'bg-pink-100 text-pink-800',
  beverage: 'bg-purple-100 text-purple-800',
  special: 'bg-gold text-gold-foreground'
};

const orderStatusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirmed: 'bg-blue-100 text-blue-800',
  preparing: 'bg-orange-100 text-orange-800',
  ready: 'bg-green-100 text-green-800',
  delivered: 'bg-success text-success-foreground',
  cancelled: 'bg-destructive text-destructive-foreground'
};

const orderTypeColors = {
  'room-service': 'bg-primary text-primary-foreground',
  'restaurant': 'bg-blue-100 text-blue-800',
  'bar': 'bg-purple-100 text-purple-800'
};

const MenuItemCard: React.FC<{ item: MenuItem }> = ({ item }) => {
  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{item.name}</CardTitle>
            <p className="text-muted-foreground text-sm">{item.description}</p>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <Badge className={categoryColors[item.category]}>
              {item.category}
            </Badge>
            <Badge className={item.available ? 'bg-success text-success-foreground' : 'bg-muted text-muted-foreground'}>
              {item.available ? 'Available' : 'Unavailable'}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Price & Prep Time */}
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-lg font-semibold">${item.price}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{item.preparationTime} min</span>
            </div>
          </div>

          {/* Allergens */}
          {item.allergens.length > 0 && (
            <div>
              <p className="text-xs text-muted-foreground mb-2">Allergens:</p>
              <div className="flex flex-wrap gap-1">
                {item.allergens.map((allergen, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {allergen}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
            <Button size="sm" variant="outline" className="flex-1">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const OrderCard: React.FC<{ order: Order }> = ({ order }) => {
  const getLocationDisplay = () => {
    if (order.roomNumber) return `Room ${order.roomNumber}`;
    if (order.tableNumber) return `Table ${order.tableNumber}`;
    return 'Pickup';
  };

  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{order.orderNumber}</CardTitle>
            <p className="text-muted-foreground">{order.customer}</p>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <Badge className={orderStatusColors[order.status]}>
              {order.status}
            </Badge>
            <Badge className={orderTypeColors[order.type]}>
              {order.type.replace('-', ' ')}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Location */}
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{getLocationDisplay()}</span>
          </div>

          {/* Items */}
          <div className="bg-accent p-3 rounded-lg">
            <div className="space-y-2">
              {order.items.map((item, index) => (
                <div key={index} className="flex justify-between items-start">
                  <div>
                    <span className="text-sm font-medium">{item.quantity}x {item.menuItem.name}</span>
                    {item.specialInstructions && (
                      <p className="text-xs text-muted-foreground">{item.specialInstructions}</p>
                    )}
                  </div>
                  <span className="text-sm">${(item.menuItem.price * item.quantity).toFixed(2)}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Timing */}
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <Timer className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Ordered: {new Date(order.orderTime).toLocaleTimeString()}</span>
            </div>
            {order.estimatedTime && (
              <span className="text-sm text-primary">ETA: {order.estimatedTime}</span>
            )}
          </div>

          {/* Total */}
          <div className="border-t pt-2">
            <div className="flex justify-between items-center font-semibold">
              <span>Total:</span>
              <span>${order.total.toFixed(2)}</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            {order.status === 'pending' && (
              <Button size="sm" className="flex-1 btn-primary">
                Confirm
              </Button>
            )}
            {order.status === 'preparing' && (
              <Button size="sm" className="flex-1 bg-success hover:bg-success/90 text-success-foreground">
                <CheckCircle className="h-4 w-4 mr-1" />
                Ready
              </Button>
            )}
            {order.status === 'ready' && order.type === 'room-service' && (
              <Button size="sm" className="flex-1 btn-primary">
                Deliver
              </Button>
            )}
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-1" />
              Details
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Restaurant: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'menu' | 'orders'>('orders');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredMenu = mockMenuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const filteredOrders = mockOrders.filter(order => {
    const matchesSearch = 
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const orderStats = {
    total: mockOrders.length,
    pending: mockOrders.filter(o => o.status === 'pending').length,
    preparing: mockOrders.filter(o => o.status === 'preparing').length,
    ready: mockOrders.filter(o => o.status === 'ready').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Restaurant Management</h1>
          <p className="text-muted-foreground">Manage menu items and food orders</p>
        </div>
        <Button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          {activeTab === 'menu' ? 'Add Menu Item' : 'New Order'}
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b">
        <button
          onClick={() => setActiveTab('orders')}
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'orders' 
              ? 'border-primary text-primary' 
              : 'border-transparent text-muted-foreground hover:text-foreground'
          }`}
        >
          Orders
        </button>
        <button
          onClick={() => setActiveTab('menu')}
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'menu' 
              ? 'border-primary text-primary' 
              : 'border-transparent text-muted-foreground hover:text-foreground'
          }`}
        >
          Menu Management
        </button>
      </div>

      {activeTab === 'orders' && (
        <>
          {/* Order Stats */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{orderStats.total}</div>
                <div className="text-sm text-muted-foreground">Total Orders</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-yellow-600">{orderStats.pending}</div>
                <div className="text-sm text-muted-foreground">Pending</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{orderStats.preparing}</div>
                <div className="text-sm text-muted-foreground">Preparing</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-success">{orderStats.ready}</div>
                <div className="text-sm text-muted-foreground">Ready</div>
              </CardContent>
            </Card>
          </div>

          {/* Order Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search orders..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Orders</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="preparing">Preparing</SelectItem>
                    <SelectItem value="ready">Ready</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Orders Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} />
            ))}
          </div>
        </>
      )}

      {activeTab === 'menu' && (
        <>
          {/* Menu Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search menu items..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="appetizer">Appetizers</SelectItem>
                    <SelectItem value="main">Main Courses</SelectItem>
                    <SelectItem value="dessert">Desserts</SelectItem>
                    <SelectItem value="beverage">Beverages</SelectItem>
                    <SelectItem value="special">Specials</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Menu Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredMenu.map((item) => (
              <MenuItemCard key={item.id} item={item} />
            ))}
          </div>
        </>
      )}

      {((activeTab === 'orders' && filteredOrders.length === 0) || 
        (activeTab === 'menu' && filteredMenu.length === 0)) && (
        <div className="text-center py-12">
          <Utensils className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            No {activeTab === 'orders' ? 'orders' : 'menu items'} found
          </h3>
          <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Restaurant;