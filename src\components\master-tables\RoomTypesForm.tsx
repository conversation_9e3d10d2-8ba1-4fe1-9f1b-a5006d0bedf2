import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import JsonImageManager from './JsonImageManager';
import { ImageUploadField } from '../admin/ImageUploadField';

interface RoomType {
  id?: string;
  property_id: string;
  name: string;
  code: string;
  description?: string;
  base_occupancy: number;
  max_occupancy: number;
  extra_bed_capacity: number;
  extra_bed_charge: number;
  bed_type?: string;
  bed_count: number;
  is_active: boolean;
}

interface Property {
  id: string;
  name: string;
}

interface Amenity {
  id: string;
  name: string;
  category: string;
  is_active: boolean;
}

interface RoomTypesFormProps {
  roomType?: RoomType | null;
  onClose: () => void;
  onSuccess: () => void;
}

const RoomTypesForm: React.FC<RoomTypesFormProps> = ({ roomType, onClose, onSuccess }) => {
  const [formData, setFormData] = useState<RoomType>({
    property_id: '',
    name: '',
    code: '',
    description: '',
    base_occupancy: 1,
    max_occupancy: 2,
    extra_bed_capacity: 0,
    extra_bed_charge: 0,
    bed_type: '',
    bed_count: 1,
    is_active: true,
  });
  const [properties, setProperties] = useState<Property[]>([]);
  const [amenities, setAmenities] = useState<Amenity[]>([]);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const { toast } = useToast();

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `room-types/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      setImageUrl(data.publicUrl);

      toast({
        title: "Success",
        description: "Image uploaded successfully",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Error",
        description: "Failed to upload image",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      event.target.value = '';
    }
  };



  useEffect(() => {
    const initializeForm = async () => {
      await fetchProperties();
      await fetchAmenities();
      if (roomType) {
        setFormData(roomType);
        fetchRoomTypeImage(roomType.id!);
        fetchRoomTypeAmenities(roomType.id!);
      } else {
        setSelectedAmenities([]);
      }
    };
    initializeForm();
  }, [roomType]);

  const fetchRoomTypeImage = async (roomTypeId: string) => {
    try {
      const { data, error } = await supabase
        .from('room_types')
        .select('image_url')
        .eq('id', roomTypeId)
        .single();

      if (error) throw error;
      setImageUrl(data?.image_url || '');
    } catch (error) {
      console.error('Error fetching room type image:', error);
    }
  };

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from('properties_admin')
        .select('id, name')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setProperties(data || []);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch properties',
        variant: 'destructive',
      });
    }
  };

  const fetchAmenities = async () => {
    try {
      const { data, error } = await supabase
        .from('amenities_admin')
        .select('id, name, category')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('category', { ascending: true })
        .order('name', { ascending: true });

      if (error) throw error;
      setAmenities(data || []);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch amenities',
        variant: 'destructive',
      });
    }
  };

  const fetchRoomTypeAmenities = async (roomTypeId: string) => {
    try {
      // Check if table exists by trying to query it
      const { data, error } = await supabase
        .from('room_type_amenities')
        .select('amenity_id')
        .eq('room_type_id', roomTypeId)
        .limit(1);

      if (error) {
        // Table doesn't exist or other error - set empty for now
        console.log('Room type amenities table not available yet');
        setSelectedAmenities([]);
        return;
      }

      // Fetch all amenities for this room type
      const { data: allData, error: fetchError } = await supabase
        .from('room_type_amenities')
        .select('amenity_id')
        .eq('room_type_id', roomTypeId);

      if (fetchError) {
        console.error('Error fetching room type amenities:', fetchError);
        setSelectedAmenities([]);
        return;
      }

      setSelectedAmenities(allData?.map(item => item.amenity_id) || []);
    } catch (error) {
      console.error('Error fetching room type amenities:', error);
      setSelectedAmenities([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!formData.property_id) {
        toast({
          title: 'Error',
          description: 'Please select a property',
          variant: 'destructive',
        });
        return;
      }

      const submitData = {
        ...formData,
        image_url: imageUrl || null,
        created_by: user?.id,
        updated_by: user?.id,
      };

      let roomTypeId = roomType?.id;

      if (roomType?.id) {
        const { error } = await supabase
          .from('room_types')
          .update(submitData)
          .eq('id', roomType.id);
        if (error) throw error;
      } else {
        const { data, error } = await supabase
          .from('room_types')
          .insert([submitData])
          .select('id')
          .single();
        if (error) throw error;
        roomTypeId = data.id;
      }

      // Handle amenities (skip if table doesn't exist)
      if (roomTypeId && selectedAmenities.length > 0) {
        try {
          // Delete existing amenities
          await supabase
            .from('room_type_amenities')
            .delete()
            .eq('room_type_id', roomTypeId);

          // Insert new amenities
          const amenityInserts = selectedAmenities.map(amenityId => ({
            room_type_id: roomTypeId,
            amenity_id: amenityId
          }));

          await supabase
            .from('room_type_amenities')
            .insert(amenityInserts);
        } catch (amenityError) {
          console.error('Amenities table not available:', amenityError);
          // Continue without failing the room type creation
        }
      }

      toast({
        title: 'Success',
        description: `Room type ${roomType?.id ? 'updated' : 'created'} successfully`,
      });
      onSuccess();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save room type',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>{roomType?.id ? 'Edit Room Type' : 'Add New Room Type'}</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Property Selection */}
            <div>
              <Label htmlFor="property_id">Property *</Label>
              <Select value={formData.property_id} onValueChange={(value) => setFormData({ ...formData, property_id: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a property" />
                </SelectTrigger>
                <SelectContent>
                  {properties.map((property) => (
                    <SelectItem key={property.id} value={property.id}>
                      {property.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Room Type Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="code">Room Type Code *</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>

            {/* Occupancy Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="base_occupancy">Base Occupancy *</Label>
                <Input
                  id="base_occupancy"
                  type="number"
                  min="1"
                  value={formData.base_occupancy}
                  onChange={(e) => setFormData({ ...formData, base_occupancy: parseInt(e.target.value) })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="max_occupancy">Max Occupancy *</Label>
                <Input
                  id="max_occupancy"
                  type="number"
                  min="1"
                  value={formData.max_occupancy}
                  onChange={(e) => setFormData({ ...formData, max_occupancy: parseInt(e.target.value) })}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="extra_bed_capacity">Extra Bed Capacity</Label>
                <Input
                  id="extra_bed_capacity"
                  type="number"
                  min="0"
                  value={formData.extra_bed_capacity}
                  onChange={(e) => setFormData({ ...formData, extra_bed_capacity: parseInt(e.target.value) })}
                />
              </div>
              <div>
                <Label htmlFor="extra_bed_charge">Extra Bed Charge</Label>
                <Input
                  id="extra_bed_charge"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.extra_bed_charge}
                  onChange={(e) => setFormData({ ...formData, extra_bed_charge: parseFloat(e.target.value) })}
                />
              </div>
            </div>

            {/* Physical Attributes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="bed_type">Bed Type</Label>
                <Select value={formData.bed_type} onValueChange={(value) => setFormData({ ...formData, bed_type: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select bed type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Single">Single</SelectItem>
                    <SelectItem value="Twin">Twin</SelectItem>
                    <SelectItem value="Double">Double</SelectItem>
                    <SelectItem value="Queen">Queen</SelectItem>
                    <SelectItem value="King">King</SelectItem>
                    <SelectItem value="Super King">Super King</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="bed_count">Bed Count</Label>
                <Input
                  id="bed_count"
                  type="number"
                  min="1"
                  value={formData.bed_count}
                  onChange={(e) => setFormData({ ...formData, bed_count: parseInt(e.target.value) })}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
              />
              <Label htmlFor="is_active">Active</Label>
            </div>

            {/* Amenities Section */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Room Amenities</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto border rounded-lg p-4">
                {amenities.map((amenity) => (
                  <div key={amenity.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`amenity-${amenity.id}`}
                      checked={selectedAmenities.includes(amenity.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedAmenities([...selectedAmenities, amenity.id]);
                        } else {
                          setSelectedAmenities(selectedAmenities.filter(id => id !== amenity.id));
                        }
                      }}
                    />
                    <Label htmlFor={`amenity-${amenity.id}`} className="text-sm">
                      {amenity.name} <span className="text-xs text-muted-foreground">({amenity.category})</span>
                    </Label>
                  </div>
                ))}
              </div>
              {selectedAmenities.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  {selectedAmenities.length} amenities selected
                </p>
              )}
            </div>

            {/* Image Section */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Room Type Image</Label>
              <div className="flex gap-4">
                <div className="flex-1">
                  <ImageUploadField
                    value={imageUrl}
                    onChange={setImageUrl}
                    onFileUpload={handleImageUpload}
                    uploading={uploading}
                    placeholder="Enter image URL or upload image"
                  />
                </div>
                {imageUrl && (
                  <img
                    src={imageUrl}
                    alt="Room Type Preview"
                    className="w-16 h-16 object-cover rounded"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/placeholder.svg';
                    }}
                  />
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : roomType?.id ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoomTypesForm;