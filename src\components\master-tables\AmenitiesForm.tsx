import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface Amenity {
  id?: string;
  property_id?: string;
  name: string;
  category: string;
  sub_category?: string;
  description?: string;
  is_chargeable: boolean;
  charge_amount: number;
  charge_type?: string;
  is_active: boolean;
}

interface Property {
  id: string;
  name: string;
}

interface AmenitiesFormProps {
  amenity?: Amenity | null;
  onClose: () => void;
  onSuccess: () => void;
}

const AmenitiesForm: React.FC<AmenitiesFormProps> = ({ amenity, onClose, onSuccess }) => {
  const [formData, setFormData] = useState<Amenity>({
    property_id: '',
    name: '',
    category: 'ROOM',
    sub_category: '',
    description: '',
    is_chargeable: false,
    charge_amount: 0,
    charge_type: 'PER_NIGHT',
    is_active: true,
  });
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchProperties();
    if (amenity) {
      setFormData(amenity);
    }
  }, [amenity]);

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from('properties_admin')
        .select('id, name')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setProperties(data || []);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch properties',
        variant: 'destructive',
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        ...formData,
        property_id: formData.property_id || null,
      };

      if (amenity?.id) {
        const { error } = await supabase
          .from('amenities_admin')
          .update(submitData)
          .eq('id', amenity.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('amenities_admin')
          .insert([submitData]);
        if (error) throw error;
      }

      toast({
        title: 'Success',
        description: `Amenity ${amenity?.id ? 'updated' : 'created'} successfully`,
      });
      onSuccess();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save amenity',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>{amenity?.id ? 'Edit Amenity' : 'Add New Amenity'}</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Property Selection (Optional) */}
            <div>
              <Label htmlFor="property_id">Property (Optional - Leave empty for global amenity)</Label>
              <Select value={formData.property_id || 'global'} onValueChange={(value) => setFormData({ ...formData, property_id: value === 'global' ? undefined : value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a property (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="global">Global Amenity</SelectItem>
                  {properties.map((property) => (
                    <SelectItem key={property.id} value={property.id}>
                      {property.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Basic Information */}
            <div>
              <Label htmlFor="name">Amenity Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ROOM">Room</SelectItem>
                    <SelectItem value="BATHROOM">Bathroom</SelectItem>
                    <SelectItem value="TECHNOLOGY">Technology</SelectItem>
                    <SelectItem value="FACILITY">Facility</SelectItem>
                    <SelectItem value="SERVICE">Service</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="sub_category">Sub Category</Label>
                <Input
                  id="sub_category"
                  value={formData.sub_category}
                  onChange={(e) => setFormData({ ...formData, sub_category: e.target.value })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>

            {/* Pricing Information */}
            <div className="flex items-center space-x-2">
              <Switch
                id="is_chargeable"
                checked={formData.is_chargeable}
                onCheckedChange={(checked) => setFormData({ ...formData, is_chargeable: checked })}
              />
              <Label htmlFor="is_chargeable">Chargeable Amenity</Label>
            </div>

            {formData.is_chargeable && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="charge_amount">Charge Amount</Label>
                  <Input
                    id="charge_amount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.charge_amount}
                    onChange={(e) => setFormData({ ...formData, charge_amount: parseFloat(e.target.value) })}
                  />
                </div>
                <div>
                  <Label htmlFor="charge_type">Charge Type</Label>
                  <Select value={formData.charge_type} onValueChange={(value) => setFormData({ ...formData, charge_type: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PER_NIGHT">Per Night</SelectItem>
                      <SelectItem value="ONE_TIME">One Time</SelectItem>
                      <SelectItem value="PER_PERSON">Per Person</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
              />
              <Label htmlFor="is_active">Active</Label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : amenity?.id ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AmenitiesForm;