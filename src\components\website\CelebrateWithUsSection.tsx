import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface CelebrationEvent {
  id: string;
  title: string;
  description: string;
  image_url: string;
  category: string;
  display_order: number;
  is_active: boolean;
}

const CelebrateWithUsSection: React.FC = () => {
  const [celebrationEvents, setCelebrationEvents] = useState<CelebrationEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    fetchCelebrationEvents();
  }, []);

  const fetchCelebrationEvents = async () => {
    try {
      const { data } = await supabase
        .from('celebration_events')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      setCelebrationEvents(data || []);
    } catch (error) {
      console.error('Error fetching celebration events:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section className="bg-slate-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-12"></div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <motion.div 
                  key={i} 
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.6, delay: i * 0.1, ease: "easeOut" }}
                  viewport={{ once: true }}
                  className="h-80 bg-slate-200 rounded-2xl"
                ></motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (celebrationEvents.length === 0) {
    return null;
  }

  const hasMoreThanFour = celebrationEvents.length > 4;
  const visibleEvents = hasMoreThanFour 
    ? celebrationEvents.slice(currentIndex, currentIndex + 4)
    : celebrationEvents;

  const nextSlide = () => {
    if (currentIndex + 4 < celebrationEvents.length) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  return (
    <section className="bg-slate-50">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" 
            style={{ fontFamily: "'Cormorant Garamond', serif" }}
          >
            Celebrate <span className="text-amber-600">With Us</span>
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-3 text-slate-600 max-w-2xl mx-auto"
          >
            From intimate gatherings to grand celebrations, we create unforgettable moments for every occasion
          </motion.p>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
          className="relative flex items-center gap-6"
        >
          {hasMoreThanFour && (
            <motion.button
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.6 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={prevSlide}
              disabled={currentIndex === 0}
              className="flex-shrink-0 p-2 rounded-full bg-white ring-1 ring-slate-200 hover:ring-emerald-300 disabled:opacity-50 disabled:cursor-not-allowed transition shadow-lg"
            >
              <ChevronLeft className="w-5 h-5 text-slate-600" />
            </motion.button>
          )}
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 flex-1">
          {visibleEvents.map((event, index) => (
            <motion.div 
              key={event.id} 
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.7 + (index * 0.1), ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ y: -8, scale: 1.02 }}
              className="group bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
            >
              <div className="aspect-[4/3] overflow-hidden">
                <motion.img
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.4 }}
                  src={event.image_url}
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <motion.div 
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 0.8 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="mb-2 flex flex-wrap gap-1"
                >
                  {event.category.split(',').map((cat, catIndex) => (
                    <span key={catIndex} className="inline-block px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 rounded-full">
                      {cat.trim()}
                    </span>
                  ))}
                </motion.div>
                <motion.h3 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.9 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-lg font-semibold text-emerald-950 mb-2"
                >
                  {event.title}
                </motion.h3>
                <motion.p 
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 1 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-sm text-slate-600 mb-4 line-clamp-3"
                >
                  {event.description}
                </motion.p>
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 1.1 + (index * 0.1) }}
                  viewport={{ once: true }}
                  whileHover={{ x: 5 }}
                >
                  <Link 
                    to="/contact" 
                    className="inline-flex items-center gap-2 text-sm font-medium text-emerald-900 hover:text-emerald-700 transition"
                  >
                    Plan Event
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </motion.div>
              </div>
            </motion.div>
          ))}
          </div>
          
          {hasMoreThanFour && (
            <motion.button
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.6 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={nextSlide}
              disabled={currentIndex + 4 >= celebrationEvents.length}
              className="flex-shrink-0 p-2 rounded-full bg-white ring-1 ring-slate-200 hover:ring-emerald-300 disabled:opacity-50 disabled:cursor-not-allowed transition shadow-lg"
            >
              <ChevronRight className="w-5 h-5 text-slate-600" />
            </motion.button>
          )}
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          viewport={{ once: true }}
          className="text-center mt-10"
        >
          <motion.div
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/contact" className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
              <Calendar className="w-5 h-5" />
              Book Your Event
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default CelebrateWithUsSection;