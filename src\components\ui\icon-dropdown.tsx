import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Crown,
  Building2,
  PartyPopper,
  Sparkles,
  Heart,
  Users,
  Calendar,
  Gift,
  Music,
  Camera,
  Utensils,
  Cake,
  Star,
  Flower,
  ChevronDown
} from 'lucide-react';

const eventVenueIcons = [
  { name: 'Crown', icon: Crown, label: 'Wedding/Royal' },
  { name: 'Building2', icon: Building2, label: 'Corporate' },
  { name: 'PartyPopper', icon: PartyPopper, label: 'Birthday/Party' },
  { name: 'Spark<PERSON>', icon: Sparkles, label: 'Celebration' },
  { name: 'Heart', icon: Heart, label: 'Romance/Love' },
  { name: 'Users', icon: Users, label: 'Group Events' },
  { name: 'Calendar', icon: Calendar, label: 'Scheduled Events' },
  { name: 'Gift', icon: Gift, label: 'Special Occasions' },
  { name: 'Music', icon: Music, label: 'Entertainment' },
  { name: 'Camera', icon: Camera, label: 'Photography' },
  { name: 'Utensils', icon: Utensils, label: 'Dining' },
  { name: 'Cake', icon: Cake, label: 'Dessert/Celebration' },
  { name: 'Star', icon: Star, label: 'Premium/VIP' },
  { name: 'Flower', icon: Flower, label: 'Decoration/Floral' }
];

interface IconDropdownProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
}

export const IconDropdown: React.FC<IconDropdownProps> = ({ value, onChange, label = "Icon" }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const selectedIcon = eventVenueIcons.find(icon => icon.name === value);
  const SelectedIconComponent = selectedIcon?.icon;

  return (
    <div>
      <Label htmlFor="icon">{label}</Label>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between"
            type="button"
          >
            <div className="flex items-center gap-2">
              {SelectedIconComponent && <SelectedIconComponent className="h-4 w-4" />}
              <span>{selectedIcon?.label || 'Select an icon'}</span>
            </div>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56 max-h-64 overflow-y-auto">
          {eventVenueIcons.map((iconItem) => {
            const IconComponent = iconItem.icon;
            return (
              <DropdownMenuItem
                key={iconItem.name}
                onClick={() => {
                  onChange(iconItem.name);
                  setIsOpen(false);
                }}
                className="flex items-center gap-2 cursor-pointer"
              >
                <IconComponent className="h-4 w-4" />
                <span>{iconItem.label}</span>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};