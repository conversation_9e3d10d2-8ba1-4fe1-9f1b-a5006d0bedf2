import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Calendar,
  DollarSign,
  Activity,
  Bell,
  Settings,
  Plus,
  ArrowUpRight,
  Zap,
  Target,
  Clock
} from 'lucide-react';

interface EnhancedDashboardProps {
  userRole: string;
}

export const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({ userRole }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const performanceMetrics = [
    { label: 'Revenue Growth', value: '+23%', trend: 'up', color: 'success' },
    { label: 'Guest Satisfaction', value: '4.8/5', trend: 'up', color: 'gold' },
    { label: 'Occupancy Rate', value: '85%', trend: 'up', color: 'primary' },
    { label: 'Avg. Stay Duration', value: '3.2 days', trend: 'stable', color: 'warning' }
  ];

  const quickInsights = [
    { 
      title: 'Peak Hours Today',
      value: '2-4 PM',
      description: 'Highest check-in activity',
      icon: Clock,
      color: 'primary'
    },
    {
      title: 'Top Room Type',
      value: 'Deluxe Suite',
      description: '68% booking rate',
      icon: Target,
      color: 'gold'
    },
    {
      title: 'Energy Efficiency',
      value: '92%',
      description: 'Above target by 12%',
      icon: Zap,
      color: 'success'
    }
  ];

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Hero Section */}
      <motion.div 
        className="relative overflow-hidden rounded-3xl bg-gradient-hero p-8 text-white"
        variants={itemVariants}
      >
        <div className="absolute inset-0 bg-black/20" />
        <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/10 rounded-full blur-3xl" />
        <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-gold/20 rounded-full blur-2xl" />
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <BarChart3 className="h-10 w-10" />
              </div>
              <div>
                <h1 className="text-5xl font-bold mb-2">Dashboard Overview</h1>
                <p className="text-primary-foreground/80 text-xl">
                  Real-time insights and performance metrics
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="secondary" size="sm" className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                <Bell className="h-4 w-4 mr-2" />
                Alerts
                <Badge variant="destructive" className="ml-2">3</Badge>
              </Button>
              <Button variant="secondary" size="sm" className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Performance Metrics Row */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {performanceMetrics.map((metric, index) => (
              <motion.div
                key={metric.label}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                whileHover={{ scale: 1.02, y: -2 }}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-white/80 font-medium">{metric.label}</span>
                  {metric.trend === 'up' && (
                    <ArrowUpRight className="h-4 w-4 text-success" />
                  )}
                </div>
                <div className="text-2xl font-bold text-white">{metric.value}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Quick Insights Grid */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
        variants={itemVariants}
      >
        {quickInsights.map((insight, index) => (
          <motion.div
            key={insight.title}
            whileHover={{ scale: 1.02, y: -4 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-elegant hover:shadow-card transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent" />
              <CardHeader className="relative pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-bold text-foreground">
                    {insight.title}
                  </CardTitle>
                  <div className={`p-3 rounded-xl ${
                    insight.color === 'success' ? 'bg-success/10 text-success' :
                    insight.color === 'gold' ? 'bg-gold/10 text-gold' :
                    insight.color === 'primary' ? 'bg-primary/10 text-primary' :
                    'bg-warning/10 text-warning'
                  }`}>
                    <insight.icon className="h-6 w-6" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="relative pt-0">
                <div className="text-3xl font-bold text-foreground mb-2">
                  {insight.value}
                </div>
                <p className="text-sm text-muted-foreground">
                  {insight.description}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Action Center */}
      <motion.div variants={itemVariants}>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-6 w-6 text-primary" />
              <span>Quick Actions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { icon: Plus, label: 'New Booking', color: 'primary' },
                { icon: Users, label: 'Guest Check-in', color: 'success' },
                { icon: BarChart3, label: 'View Reports', color: 'gold' },
                { icon: Calendar, label: 'Schedule Task', color: 'warning' }
              ].map((action, index) => (
                <motion.div
                  key={action.label}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button 
                    variant={index === 0 ? 'default' : 'outline'} 
                    className={`h-24 w-full flex flex-col space-y-3 ${
                      index === 0 
                        ? 'bg-gradient-to-br from-primary to-primary-light hover:from-primary-light hover:to-primary shadow-elegant' 
                        : 'bg-white/80 backdrop-blur-sm hover:bg-primary/5 border-primary/20'
                    }`}
                  >
                    <action.icon className={`h-8 w-8 ${index === 0 ? 'text-white' : 'text-primary'}`} />
                    <span className={`text-sm font-semibold ${index === 0 ? 'text-white' : 'text-primary'}`}>
                      {action.label}
                    </span>
                  </Button>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Recent Activity Feed */}
      <motion.div variants={itemVariants}>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-6 w-6 text-primary" />
              <span>Recent Activity</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { action: 'New booking received from John Doe', time: '2 minutes ago', type: 'booking' },
                { action: 'Room 205 maintenance completed', time: '15 minutes ago', type: 'maintenance' },
                { action: 'Payment of ₹15,000 processed', time: '1 hour ago', type: 'payment' },
                { action: 'Guest feedback received (5 stars)', time: '2 hours ago', type: 'feedback' }
              ].map((activity, index) => (
                <motion.div 
                  key={index}
                  className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-muted/30 to-transparent hover:from-muted/50 transition-all duration-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-3 h-3 rounded-full ${
                      activity.type === 'booking' ? 'bg-success' :
                      activity.type === 'maintenance' ? 'bg-warning' :
                      activity.type === 'payment' ? 'bg-gold' : 'bg-primary'
                    }`} />
                    <span className="font-medium text-foreground">{activity.action}</span>
                  </div>
                  <span className="text-sm text-muted-foreground font-medium">{activity.time}</span>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default EnhancedDashboard;