import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { PartyPop<PERSON>, Check } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';

interface EventPackage {
  id: string;
  title: string;
  description: string;
  capacity: string;
  features: string[];
  price: string;
  display_order: number;
  is_active: boolean;
}

const EventsSection: React.FC = () => {
  const [eventPackages, setEventPackages] = useState<EventPackage[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchEventPackages();
  }, []);

  const fetchEventPackages = async () => {
    try {
      const { data } = await supabase
        .from('event_packages')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      setEventPackages(data || []);
    } catch (error) {
      console.error('Error fetching event packages:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section className="bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-12"></div>
            <div className="grid md:grid-cols-2 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-64 bg-slate-200 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="bg-white">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Memorable <span className="text-amber-600">Events</span> & Celebrations
          </h2>
          <p className="mt-3 text-slate-600 max-w-2xl mx-auto">
            Create unforgettable memories with our comprehensive event management and elegant venues
          </p>
        </motion.div>
        
        <div className="grid md:grid-cols-2 gap-8">
          {eventPackages.map((event, index) => (
            <motion.div 
              key={event.id} 
              initial={{ opacity: 0, y: 40, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 + (index * 0.2), ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ y: -8, scale: 1.02 }}
              className="bg-slate-50 rounded-2xl p-8 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
            >
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.6 + (index * 0.2) }}
                viewport={{ once: true }}
                className="flex justify-between items-start mb-4"
              >
                <h3 className="text-xl font-semibold text-emerald-950">{event.title}</h3>
                <motion.div 
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.7 + (index * 0.2) }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1 }}
                  className="px-3 py-1 rounded-full bg-amber-100 text-amber-800 text-[12px] font-medium"
                >
                  {event.capacity}
                </motion.div>
              </motion.div>
              <motion.p 
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.7 + (index * 0.2) }}
                viewport={{ once: true }}
                className="text-slate-600 mb-6"
              >
                {event.description}
              </motion.p>
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.8 + (index * 0.2) }}
                viewport={{ once: true }}
                className="grid grid-cols-2 gap-3 mb-6"
              >
                {event.features?.map((feature, idx) => (
                  <motion.div 
                    key={idx} 
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.9 + (index * 0.2) + (idx * 0.05) }}
                    viewport={{ once: true }}
                    whileHover={{ x: 3 }}
                    className="flex items-center gap-2 text-[13px] text-slate-700"
                  >
                    <Check className="w-3 h-3 text-emerald-700" />
                    <span>{feature}</span>
                  </motion.div>
                ))}
              </motion.div>
              <motion.div 
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 1 + (index * 0.2) }}
                viewport={{ once: true }}
                className="pt-4 border-t border-slate-200"
              >
                <p className="text-[14px] font-semibold text-emerald-900">{event.price}</p>
              </motion.div>
            </motion.div>
          ))}
        </div>

        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-10"
        >
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link to="/events" className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[14px] text-white bg-emerald-900 hover:bg-emerald-800 transition">
              <PartyPopper className="w-4 h-4" />
              Plan Your Event
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default EventsSection;