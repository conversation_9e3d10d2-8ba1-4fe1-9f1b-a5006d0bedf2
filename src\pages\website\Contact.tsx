import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import ContactForm from '@/components/ContactForm';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock,
  MessageSquare,
  Calendar,
  Users,
  Building2,
  PartyPopper,
  Utensils,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  ArrowRight
} from 'lucide-react';

interface CompanySetting {
  setting_key: string;
  setting_value: string;
  setting_type: string;
  category: string;
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  display_order: number;
  is_active: boolean;
}

const Contact = () => {
  const [settings, setSettings] = useState<CompanySetting[]>([]);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const [totalFaqCount, setTotalFaqCount] = useState(0);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const [settingsData, faqsData, faqCountData] = await Promise.all([
        supabase
          .from('company_settings')
          .select('setting_key, setting_value, setting_type, category')
          .eq('is_active', true)
          .eq('is_deleted', false),
        supabase
          .from('faqs')
          .select('*')
          .eq('is_active', true)
          .order('display_order')
          .limit(5),
        supabase
          .from('faqs')
          .select('*', { count: 'exact', head: true })
          .eq('is_active', true)
      ]);

      if (settingsData.error) throw settingsData.error;
      if (faqsData.error) throw faqsData.error;
      if (faqCountData.error) throw faqCountData.error;
      
      setSettings(settingsData.data || []);
      setFaqs(faqsData.data || []);
      setTotalFaqCount(faqCountData.count || 0);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSetting = (key: string, defaultValue: string = '') => {
    const setting = settings.find(s => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-2xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900 min-h-screen" style={{ fontFamily: "'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'", scrollBehavior: 'smooth' }}>
      {/* Hero Section */}
      <section className="relative">
        <div className="relative h-[70vh] w-full overflow-hidden">
          <motion.img
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 w-full h-full object-cover"
            src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?q=80&w=1760&auto=format&fit=crop"
            alt="Contact Us"
          />
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute inset-0 bg-gradient-to-b from-emerald-950/70 via-emerald-950/50 to-emerald-950/70"
          ></motion.div>
          <div className="relative z-10 h-full">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-6">
                  <motion.div 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm ring-1 ring-white/20"
                  >
                    <MessageSquare className="w-4 h-4 text-amber-300" />
                    <span className="text-white/90 text-sm font-medium">Get in Touch</span>
                  </motion.div>
                  <motion.h1 
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 1, delay: 1, ease: "easeOut" }}
                    className="text-4xl sm:text-5xl lg:text-6xl font-semibold leading-[1.1] tracking-tight text-white" 
                    style={{ fontFamily: "'Cormorant Garamond', serif" }}
                  >
                    Contact <span className="text-amber-300">{getSetting('company_name', 'Meadow De Jalsa')}</span>
                  </motion.h1>
                  <motion.p 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 1.3, ease: "easeOut" }}
                    className="text-lg sm:text-xl text-white/90 leading-relaxed max-w-3xl mx-auto"
                  >
                    {getSetting('company_description', "We're here to help you plan your perfect stay, dining experience, or special event")}
                  </motion.p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form & Info Section */}
      <section className="bg-slate-50">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
        >
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <ContactForm variant="light" />
            </motion.div>

            {/* Contact Information */}
            <motion.div 
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              {/* Quick Contact */}
              <motion.div 
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-white rounded-2xl p-6 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
              >
                <motion.h3 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 }}
                  viewport={{ once: true }}
                  className="text-xl font-semibold text-emerald-950 mb-4"
                >
                  Quick Contact
                </motion.h3>
                <div className="space-y-4">
                  {getSetting('phone_primary') && (
                    <motion.a 
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.6 }}
                      viewport={{ once: true }}
                      whileHover={{ x: 5, scale: 1.02 }}
                      href={`tel:${getSetting('phone_primary')}`} 
                      className="flex items-center gap-4 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition"
                    >
                      <motion.div 
                        whileHover={{ rotate: 360, scale: 1.1 }}
                        className="h-10 w-10 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center"
                      >
                        <Phone className="w-5 h-5" />
                      </motion.div>
                      <div>
                        <div className="text-sm text-slate-500">Call Us</div>
                        <div className="font-medium text-emerald-950">{getSetting('phone_primary')}</div>
                      </div>
                    </motion.a>
                  )}
                  
                  {getSetting('email_primary') && (
                    <motion.a 
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.7 }}
                      viewport={{ once: true }}
                      whileHover={{ x: 5, scale: 1.02 }}
                      href={`mailto:${getSetting('email_primary')}`} 
                      className="flex items-center gap-4 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition"
                    >
                      <motion.div 
                        whileHover={{ rotate: 360, scale: 1.1 }}
                        className="h-10 w-10 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center"
                      >
                        <Mail className="w-5 h-5" />
                      </motion.div>
                      <div>
                        <div className="text-sm text-slate-500">Email Us</div>
                        <div className="font-medium text-emerald-950">{getSetting('email_primary')}</div>
                      </div>
                    </motion.a>
                  )}
                </div>
              </motion.div>

              {/* Location */}
              <motion.div 
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-white rounded-2xl p-6 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
              >
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.6 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-3 mb-4"
                >
                  <motion.div 
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    className="h-10 w-10 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center"
                  >
                    <MapPin className="w-5 h-5" />
                  </motion.div>
                  <h3 className="text-xl font-semibold text-emerald-950">Visit Us</h3>
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.7 }}
                  viewport={{ once: true }}
                  className="text-slate-700 leading-relaxed"
                >
                  <p>{getSetting('address_full', 'Bardoli–Vyara Highway')}</p>
                  <p>{getSetting('address_city', 'Bardoli')}, {getSetting('address_state', 'Gujarat')} {getSetting('address_pincode', '394601')}</p>
                </motion.div>
                {getSetting('map_embed_url') && (
                  <motion.div 
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: 0.8 }}
                    viewport={{ once: true }}
                    className="mt-4"
                  >
                    <iframe 
                      title={`${getSetting('company_name', 'Company')} Location`}
                      className="w-full h-48 rounded-xl ring-1 ring-slate-200" 
                      loading="lazy" 
                      referrerPolicy="no-referrer-when-downgrade" 
                      src={getSetting('map_embed_url')}
                    />
                  </motion.div>
                )}
              </motion.div>

              {/* Operating Hours */}
              <motion.div 
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-white rounded-2xl p-6 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
              >
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.7 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-3 mb-4"
                >
                  <motion.div 
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    className="h-10 w-10 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center"
                  >
                    <Clock className="w-5 h-5" />
                  </motion.div>
                  <h3 className="text-xl font-semibold text-emerald-950">Operating Hours</h3>
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.8 }}
                  viewport={{ once: true }}
                  className="space-y-3 text-sm"
                >
                  <div className="text-slate-700">
                    {getSetting('business_hours', 'Hotel: 24/7 · Restaurant: 11 AM – 11 PM')}
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Services Quick Links */}
      <section className="bg-white">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
        >
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" 
              style={{ fontFamily: "'Cormorant Garamond', serif" }}
            >
              How Can We Help You?
            </motion.h2>
            <motion.p 
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.4 }}
              viewport={{ once: true }}
              className="mt-2 text-slate-600"
            >
              Choose the service you're interested in for faster assistance
            </motion.p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { icon: Building2, title: "Room Booking", desc: "Reserve your perfect stay with us", action: "Call for Booking", link: `tel:${getSetting('phone_primary', '+918740027008')}` },
              { icon: PartyPopper, title: "Event Planning", desc: "Plan your special celebration", action: "Discuss Your Event", link: `tel:${getSetting('phone_primary', '+918740027008')}` },
              { icon: Utensils, title: "Restaurant", desc: "Reserve your dining table", action: "Make Reservation", link: `tel:${getSetting('phone_primary', '+918740027008')}` },
              { icon: MessageSquare, title: "General Inquiry", desc: "Any questions or feedback", action: "Send Email", link: `mailto:${getSetting('email_primary', '<EMAIL>')}` }
            ].map((service, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.5 + (index * 0.1), ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.05 }}
                className="text-center p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
              >
                <motion.div 
                  initial={{ scale: 0, rotate: -180 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 + (index * 0.1) }}
                  viewport={{ once: true }}
                  whileHover={{ rotate: 360, scale: 1.2 }}
                  className="h-12 w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mx-auto mb-4"
                >
                  <service.icon className="w-6 h-6" />
                </motion.div>
                <motion.h3 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.8 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[16px] font-semibold text-emerald-950 mb-2"
                >
                  {service.title}
                </motion.h3>
                <motion.p 
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.9 + (index * 0.1) }}
                  viewport={{ once: true }}
                  className="text-[13px] text-slate-600 mb-4"
                >
                  {service.desc}
                </motion.p>
                <motion.a 
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 1 + (index * 0.1) }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  href={service.link} 
                  className="text-[13px] text-emerald-900 hover:text-emerald-700 transition"
                >
                  {service.action}
                </motion.a>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* FAQ Section */}
      <section className="bg-slate-50">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
        >
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" 
              style={{ fontFamily: "'Cormorant Garamond', serif" }}
            >
              Frequently Asked Questions
            </motion.h2>
            <motion.p 
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.4 }}
              viewport={{ once: true }}
              className="mt-2 text-slate-600"
            >
              Quick answers to common questions
            </motion.p>
          </motion.div>
          
          <div className="max-w-3xl mx-auto space-y-3">
            {faqs.map((faq, index) => (
              <motion.div 
                key={faq.id} 
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.5 + (index * 0.1), ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02, y: -2 }}
                className="bg-white rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300 overflow-hidden"
              >
                <motion.button
                  whileHover={{ backgroundColor: "rgb(248 250 252)" }}
                  onClick={() => setOpenFaq(openFaq === index ? null : index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between transition"
                >
                  <h3 className="text-[16px] font-semibold text-emerald-950">{faq.question}</h3>
                  <motion.div
                    animate={{ rotate: openFaq === index ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ChevronDown className="w-5 h-5 text-slate-400" />
                  </motion.div>
                </motion.button>
                {openFaq === index && (
                  <motion.div 
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="px-6 pb-4"
                  >
                    <p className="text-[14px] text-slate-600">{faq.answer}</p>
                  </motion.div>
                )}
              </motion.div>
            ))}
            {faqs.length === 0 && (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                viewport={{ once: true }}
                className="text-center py-8 text-slate-500"
              >
                No FAQs available at the moment.
              </motion.div>
            )}
          </div>
          
          {totalFaqCount > 5 && (
            <motion.div 
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              viewport={{ once: true }}
              className="mt-8 text-center"
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="/faq"
                  className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[14px] text-white bg-emerald-900 hover:bg-emerald-800 transition"
                >
                  View All FAQs
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </motion.div>
            </motion.div>
          )}
        </motion.div>
      </section>
    </div>
  );
};

export default Contact;