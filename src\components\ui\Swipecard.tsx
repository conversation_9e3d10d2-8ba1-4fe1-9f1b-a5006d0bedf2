import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectCards, Autoplay } from 'swiper/modules';
import { supabase } from '@/integrations/supabase/client';
import 'swiper/css';
import 'swiper/css/effect-cards';

interface SwipeCard {
    id: string;
    title: string;
    description: string;
    image_url: string;
    display_order: number;
    is_active: boolean;
}

const SwipeCards = () => {
    const [cards, setCards] = useState<SwipeCard[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchSwipeCards();
    }, []);

    const fetchSwipeCards = async () => {
        try {
            const { data, error } = await supabase
                .from('swipe_cards')
                .select('*')
                .eq('is_active', true)
                .order('display_order');

            if (error) throw error;
            setCards(data || []);
        } catch (error) {
            console.error('Error fetching swipe cards:', error);
            // Fallback to default images if database fails
            setCards([
                {
                    id: '1',
                    title: 'Default Image 1',
                    description: '',
                    image_url: '/hero1.png',
                    display_order: 1,
                    is_active: true
                },
                {
                    id: '2',
                    title: 'Default Image 2',
                    description: '',
                    image_url: '/hero2.jpg',
                    display_order: 2,
                    is_active: true
                }
            ]);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="w-full h-[300px] sm:h-[400px] lg:h-[500px] aspect-[4/5] flex items-center justify-center bg-gray-100 rounded-2xl">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="w-full h-[300px] sm:h-[400px] lg:h-[500px] aspect-[4/5]">
            <Swiper
                effect={'cards'}
                grabCursor={true}
                modules={[EffectCards, Autoplay]}
                autoplay={{
                    delay: 3000,
                    disableOnInteraction: false,
                }}
                cardsEffect={{
                    perSlideOffset: 6,
                    perSlideRotate: 2,
                    rotate: true,
                    slideShadows: true,
                }}
                className="w-full h-full"
            >
                {cards.map((card) => (
                    <SwiperSlide key={card.id} className='rounded-2xl sm:rounded-3xl overflow-hidden'>
                        <div className="relative w-full h-full rounded-2xl sm:rounded-3xl  overflow-hidden">
                            <img
                                src={card.image_url}
                                alt={card.title}
                                className="w-full h-full object-cover rounded-2xl sm:rounded-3xl s"
                            />
                            {card.title && (
                                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 rounded-b-2xl sm:rounded-b-3xl">
                                    <h3 className="text-white font-semibold text-lg">{card.title}</h3>
                                    {card.description && (
                                        <p className="text-white/90 text-sm mt-1">{card.description}</p>
                                    )}
                                </div>
                            )}
                        </div>
                    </SwiperSlide>
                ))}
            </Swiper>
        </div>
    );
};

export default SwipeCards;