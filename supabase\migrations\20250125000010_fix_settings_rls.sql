-- Drop existing policies
DROP POLICY IF EXISTS "Allow public read access for active settings" ON public.company_settings;
DROP POLICY IF EXISTS "Allow authenticated users to manage settings" ON public.company_settings;

-- Create simpler policies
CREATE POLICY "Allow all read access to settings" ON public.company_settings
    FOR SELECT USING (true);

CREATE POLICY "Allow all access to authenticated users" ON public.company_settings
    FOR ALL USING (auth.role() = 'authenticated');