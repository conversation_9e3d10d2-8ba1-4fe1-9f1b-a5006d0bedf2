-- Drop existing policies if any
DROP POLICY IF EXISTS "Allow public read access for active properties" ON public.properties;
DROP POLICY IF EXISTS "Allow authenticated users to manage properties" ON public.properties;

-- Enable RLS
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;

-- Allow public read access for active properties
CREATE POLICY "Allow public read access for active properties" ON public.properties
    FOR SELECT USING (is_active = true);

-- Allow authenticated users full access to manage properties
CREATE POLICY "Allow authenticated users to manage properties" ON public.properties
    FOR ALL USING (true);

-- Create the update function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;