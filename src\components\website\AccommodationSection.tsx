import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Check } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import * as LucideIcons from 'lucide-react';

interface AccommodationType {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
  price: string;
  display_order: number;
  is_active: boolean;
}

const AccommodationSection: React.FC = () => {
  const [accommodationTypes, setAccommodationTypes] = useState<AccommodationType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAccommodationTypes();
  }, []);

  const fetchAccommodationTypes = async () => {
    try {
      const { data } = await supabase
        .from('accommodation_types')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      setAccommodationTypes(data || []);
    } catch (error) {
      console.error('Error fetching accommodation types:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIconComponent = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName] || LucideIcons.Bed;
    return IconComponent;
  };

  if (loading) {
    return (
      <section className="bg-slate-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-12"></div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-80 bg-slate-200 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="bg-slate-50">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-8 sm:mb-12"
        >
          <h2 className="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Luxury <span className="text-amber-600">Accommodation</span>
          </h2>
          <p className="mt-3 text-sm sm:text-base text-slate-600 max-w-2xl mx-auto px-4 sm:px-0">
            Experience comfort and elegance with our range of premium rooms and suites
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 laptop-sm:grid-cols-4 gap-4 sm:gap-6">
          {accommodationTypes.map((room, index) => {
            const IconComponent = getIconComponent(room.icon);
            return (
              <motion.div 
                key={room.id} 
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4 + (index * 0.1), ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
              >
                <div className="p-4 sm:p-6">
                  <motion.div 
                    initial={{ scale: 0, rotate: -180 }}
                    whileInView={{ scale: 1, rotate: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 + (index * 0.1) }}
                    viewport={{ once: true }}
                    whileHover={{ rotate: 360, scale: 1.1 }}
                    className="h-10 w-10 sm:h-12 sm:w-12 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center mb-3 sm:mb-4"
                  >
                    <IconComponent className="w-5 h-5 sm:w-6 sm:h-6" />
                  </motion.div>
                  <motion.h3 
                    initial={{ opacity: 0, y: 15 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.7 + (index * 0.1) }}
                    viewport={{ once: true }}
                    className="text-base sm:text-[18px] font-semibold text-emerald-950 mb-2"
                  >
                    {room.title}
                  </motion.h3>
                  <motion.p 
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.8 + (index * 0.1) }}
                    viewport={{ once: true }}
                    className="text-xs sm:text-[13px] text-slate-600 mb-3 sm:mb-4"
                  >
                    {room.description}
                  </motion.p>
                  <motion.div 
                    initial={{ opacity: 0, y: 15 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.9 + (index * 0.1) }}
                    viewport={{ once: true }}
                    className="space-y-1.5 sm:space-y-2 mb-3 sm:mb-4"
                  >
                    {room.features?.map((feature, idx) => (
                      <motion.div 
                        key={idx} 
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 1 + (index * 0.1) + (idx * 0.05) }}
                        viewport={{ once: true }}
                        whileHover={{ x: 3 }}
                        className="flex items-center gap-2 text-xs sm:text-[13px] text-slate-700"
                      >
                        <Check className="w-3 h-3 text-emerald-700 flex-shrink-0" />
                        <span>{feature}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                  <motion.div 
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 1.1 + (index * 0.1) }}
                    viewport={{ once: true }}
                    className="pt-3 sm:pt-4 border-t border-slate-200"
                  >
                    <div className="text-center">
                      <div className="text-xs sm:text-[14px] font-semibold text-emerald-900">{room.price}</div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            );
          })}
        </div>

        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-8 sm:mt-10"
        >
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link to="/properties" className="inline-flex items-center gap-2 rounded-full px-5 sm:px-6 py-2.5 sm:py-3 text-sm sm:text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
              <Calendar className="w-4 sm:w-5 h-4 sm:h-5" />
              Book Your Stay
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default AccommodationSection;