// Hotel Management System - Landing Page (shows when not authenticated)

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Hotel, 
  CalendarCheck, 
  Users, 
  BarChart3,
  Shield,
  Zap,
  CheckCircle
} from 'lucide-react';
import heroImage from '@/assets/hotel-hero.jpg';

const Index = () => {
  const features = [
    {
      icon: CalendarCheck,
      title: 'Booking Management',
      description: 'Streamline reservations with our intuitive booking system'
    },
    {
      icon: Users,
      title: 'Guest Services',
      description: 'Deliver exceptional guest experiences from check-in to check-out'
    },
    {
      icon: BarChart3,
      title: 'Analytics & Reports',
      description: 'Make data-driven decisions with comprehensive reporting'
    },
    {
      icon: Shield,
      title: 'Secure & Reliable',
      description: 'Enterprise-grade security for your hotel operations'
    },
    {
      icon: Zap,
      title: 'Real-time Updates',
      description: 'Instant synchronization across all departments'
    },
    {
      icon: Hotel,
      title: 'Multi-property Support',
      description: 'Manage multiple properties from a single dashboard'
    }
  ];

  const benefits = [
    'Increase operational efficiency by 40%',
    'Reduce check-in time by 60%',
    'Improve guest satisfaction scores',
    'Streamline housekeeping operations',
    'Real-time room status updates',
    'Integrated payment processing'
  ];

  return (
    <div className="min-h-screen bg-gradient-subtle">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{ backgroundImage: `url(${heroImage})` }}
        >
          <div className="absolute inset-0 bg-gradient-hero opacity-90"></div>
        </div>
        
        <div className="relative z-10 px-6 py-20 text-center text-white">
          <div className="max-w-4xl mx-auto">
            <Hotel className="h-20 w-20 mx-auto mb-8" />
            <h1 className="text-5xl md:text-7xl font-bold mb-6 tracking-tight">
              Hotel Management
              <span className="block gradient-text">Excellence</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
              Transform your hotel operations with our comprehensive management platform. 
              From reservations to housekeeping, manage everything seamlessly.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="btn-primary text-lg px-8 py-4">
                Start Free Trial
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-primary">
                Watch Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need to Run Your Hotel
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive platform covers every aspect of hotel management, 
              from guest services to operations.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <Card key={index} className="card-hover text-center">
                <CardHeader>
                  <div className="p-4 bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-6 bg-card">
        <div className="max-w-6xl mx-auto">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Proven Results for Hotels Worldwide
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Join thousands of hotels that have transformed their operations 
                and improved guest satisfaction with our platform.
              </p>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-6 w-6 text-success flex-shrink-0" />
                    <span className="text-lg">{benefit}</span>
                  </div>
                ))}
              </div>

              <Button className="btn-gold mt-8 text-lg px-8 py-4">
                Get Started Today
              </Button>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <Card className="bg-gradient-primary text-white">
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold mb-2">10,000+</div>
                  <div className="opacity-90">Hotels Trust Us</div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-gold text-gold-foreground">
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold mb-2">99.9%</div>
                  <div className="opacity-90">Uptime Guarantee</div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-primary text-white">
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold mb-2">24/7</div>
                  <div className="opacity-90">Support Available</div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-gold text-gold-foreground">
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold mb-2">50+</div>
                  <div className="opacity-90">Countries Served</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 text-center bg-gradient-hero text-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Transform Your Hotel Operations?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join the thousands of hoteliers who trust our platform to manage their properties efficiently.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-primary hover:bg-white/90 text-lg px-8 py-4">
              Start Your Free Trial
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary text-lg px-8 py-4">
              Schedule a Demo
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
