import { supabase } from '@/integrations/supabase/client';

export interface CreateUserParams {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role?: string;
}

// Note: These functions require additional database setup with users, roles tables and RPC functions
// They are commented out until the proper database schema is implemented

/*
export const createUserWithRole = async ({
  email,
  password,
  firstName,
  lastName,
  role = 'admin'
}: CreateUserParams) => {
  try {
    const { data, error } = await supabase.rpc('create_user_with_role', {
      user_email: email,
      user_password: password,
      user_first_name: firstName,
      user_last_name: lastName,
      role_name: role
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

export const getUserWithRole = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        roles (
          name,
          description
        )
      `)
      .eq('auth_user_id', userId)
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};
*/