import { supabase } from '@/integrations/supabase/client';

export const setupDemoUsers = async () => {
  const demoUsers = [
    { email: '<EMAIL>', password: 'manager123', role: 'hotel-manager' },
    { email: '<EMAIL>', password: 'reception123', role: 'receptionist' },
    { email: '<EMAIL>', password: 'house123', role: 'housekeeping' },
    { email: '<EMAIL>', password: 'guest123', role: 'guest' }
  ];

  console.log('Setting up demo users...');
  
  for (const user of demoUsers) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: user.email,
        password: user.password,
        options: {
          data: {
            role: user.role
          }
        }
      });
      
      if (error) {
        console.log(`User ${user.email} might already exist:`, error.message);
      } else {
        console.log(`Created user: ${user.email} with role: ${user.role}`);
      }
    } catch (error) {
      console.error(`Error creating user ${user.email}:`, error);
    }
  }
  
  console.log('Demo users setup complete!');
};