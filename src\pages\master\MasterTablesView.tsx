import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Building2,
  Bed,
  Star,
  Home,
  Calendar,
  BarChart3,
  Loader2,
} from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";

interface MasterTableStats {
  properties: number;
  roomTypes: number;
  amenities: number;
  rooms: number;
  celebrationEvents: number;
}

const MasterTablesView = () => {
  const [stats, setStats] = useState<MasterTableStats>({
    properties: 0,
    roomTypes: 0,
    amenities: 0,
    rooms: 0,
    celebrationEvents: 0,
  });
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const [propertiesRes, roomTypesRes, amenitiesRes, roomsRes, eventsRes] =
        await Promise.all([
          supabase
            .from("properties_admin")
            .select("id", { count: "exact" })
            .eq("is_deleted", false),
          supabase
            .from("room_types")
            .select("id", { count: "exact" })
            .eq("is_deleted", false),
          supabase
            .from("amenities_admin")
            .select("id", { count: "exact" })
            .eq("is_deleted", false),
          supabase
            .from("rooms")
            .select("id", { count: "exact" })
            .eq("is_deleted", false),
          supabase
            .from("celebration_events")
            .select("id", { count: "exact" })
            .eq("is_active", true),
        ]);

      setStats({
        properties: propertiesRes.count || 0,
        roomTypes: roomTypesRes.count || 0,
        amenities: amenitiesRes.count || 0,
        rooms: roomsRes.count || 0,
        celebrationEvents: eventsRes.count || 0,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch master table statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const masterTables = [
    {
      title: "Properties",
      description: "Manage property details and settings",
      icon: Building2,
      count: stats.properties,
      path: "/master/properties",
      color: "bg-primary",
      gradient: "from-primary to-primary-light",
    },
    {
      title: "Room Types",
      description: "Define room categories and occupancy",
      icon: Bed,
      count: stats.roomTypes,
      path: "/master/room-types",
      color: "bg-success",
      gradient: "from-success to-green-600",
    },
    {
      title: "Amenities",
      description: "Manage amenities and services",
      icon: Star,
      count: stats.amenities,
      path: "/master/amenities",
      color: "bg-gold",
      gradient: "from-gold to-gold-light",
    },
    {
      title: "Rooms",
      description: "Physical room inventory management",
      icon: Home,
      count: stats.rooms,
      path: "/master/rooms",
      color: "bg-warning",
      gradient: "from-warning to-orange-500",
    },
    {
      title: "Celebration Events",
      description: "Event packages and celebrations",
      icon: Calendar,
      count: stats.celebrationEvents,
      path: "/master/celebration-events",
      color: "bg-purple-500",
      gradient: "from-purple-500 to-purple-600",
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-80" />
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="border-0 bg-gradient-subtle">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-12 w-12 rounded-xl" />
                  <Skeleton className="h-6 w-8" />
                </div>
                <Skeleton className="h-6 w-24 mt-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
            Master Tables
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base mt-1">
            Manage your property's master data and configurations
          </p>
        </div>
        <div className="flex items-center space-x-2 bg-muted/50 rounded-lg px-3 py-2">
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground font-medium">
            Total Records: {Object.values(stats).reduce((a, b) => a + b, 0)}
          </span>
        </div>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, staggerChildren: 0.1 }}
      >
        {masterTables.map((table, index) => {
          const IconComponent = table.icon;
          return (
            <motion.div
              key={table.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.02, y: -4 }}
            >
              <Card
                className="cursor-pointer hover:shadow-elegant transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm h-full group"
                onClick={() => navigate(table.path)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between mb-4">
                    <div
                      className={`p-3 rounded-xl bg-gradient-to-br ${table.gradient} text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                    >
                      <IconComponent className="h-5 w-5 sm:h-6 sm:w-6" />
                    </div>
                    <Badge
                      variant="secondary"
                      className="text-sm sm:text-base font-semibold px-2 py-1"
                    >
                      {table.count}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg sm:text-xl text-foreground group-hover:text-primary transition-colors duration-300">
                    {table.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-muted-foreground text-xs sm:text-sm leading-relaxed mb-3">
                    {table.description}
                  </p>
                  <div className="flex items-center text-xs sm:text-sm text-primary group-hover:text-primary/80 font-medium transition-colors duration-300">
                    Manage {table.title.toLowerCase()} →
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <BarChart3 className="h-5 w-5 text-primary" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <motion.div
                className="p-4 border rounded-xl hover:bg-accent cursor-pointer transition-all duration-200 hover:shadow-card group"
                onClick={() => navigate("/master/properties")}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <h3 className="font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                  Setup New Property
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Add a new property with complete configuration
                </p>
              </motion.div>
              <motion.div
                className="p-4 border rounded-xl hover:bg-accent cursor-pointer transition-all duration-200 hover:shadow-card group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <h3 className="font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                  Bulk Import
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Import master data from CSV files
                </p>
              </motion.div>
              <motion.div
                className="p-4 border rounded-xl hover:bg-accent cursor-pointer transition-all duration-200 hover:shadow-card group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <h3 className="font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                  Data Export
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Export master data for backup or analysis
                </p>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default MasterTablesView;
