import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Bed, 
  Users, 
  DollarSign, 
  Search, 
  Filter,
  Plus,
  Edit,
  Eye,
  Wifi,
  Tv,
  Coffee,
  Car
} from 'lucide-react';

interface Room {
  id: string;
  number: string;
  type: string;
  status: 'available' | 'occupied' | 'cleaning' | 'maintenance' | 'reserved';
  capacity: number;
  price: number;
  amenities: string[];
  guest?: string;
  checkOut?: string;
}

const mockRooms: Room[] = [
  { id: '1', number: '101', type: 'Standard', status: 'available', capacity: 2, price: 120, amenities: ['wifi', 'tv'] },
  { id: '2', number: '102', type: 'Standard', status: 'occupied', capacity: 2, price: 120, amenities: ['wifi', 'tv'], guest: '<PERSON>', checkOut: '2024-01-15' },
  { id: '3', number: '103', type: 'Deluxe', status: 'cleaning', capacity: 3, price: 180, amenities: ['wifi', 'tv', 'coffee'] },
  { id: '4', number: '201', type: 'Suite', status: 'available', capacity: 4, price: 350, amenities: ['wifi', 'tv', 'coffee', 'parking'] },
  { id: '5', number: '202', type: 'Suite', status: 'reserved', capacity: 4, price: 350, amenities: ['wifi', 'tv', 'coffee', 'parking'], guest: 'Emma Wilson', checkOut: '2024-01-16' },
  { id: '6', number: '203', type: 'Deluxe', status: 'maintenance', capacity: 3, price: 180, amenities: ['wifi', 'tv', 'coffee'] },
];

const statusColors = {
  available: 'bg-success text-success-foreground',
  occupied: 'bg-destructive text-destructive-foreground',
  cleaning: 'bg-warning text-warning-foreground',
  maintenance: 'bg-muted text-muted-foreground',
  reserved: 'bg-primary text-primary-foreground'
};

const statusLabels = {
  available: 'Available',
  occupied: 'Occupied',
  cleaning: 'Cleaning',
  maintenance: 'Maintenance',
  reserved: 'Reserved'
};

const amenityIcons = {
  wifi: Wifi,
  tv: Tv,
  coffee: Coffee,
  parking: Car
};

const RoomCard: React.FC<{ room: Room }> = ({ room }) => {
  return (
    <Card className="card-hover">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-bold">Room {room.number}</CardTitle>
            <p className="text-muted-foreground">{room.type}</p>
          </div>
          <Badge className={statusColors[room.status]}>
            {statusLabels[room.status]}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Room Details */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{room.capacity} guests</span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">${room.price}/night</span>
            </div>
          </div>

          {/* Amenities */}
          <div className="flex flex-wrap gap-2">
            {room.amenities.map((amenity) => {
              const Icon = amenityIcons[amenity as keyof typeof amenityIcons];
              return (
                <div key={amenity} className="flex items-center space-x-1 bg-muted px-2 py-1 rounded">
                  <Icon className="h-3 w-3" />
                  <span className="text-xs capitalize">{amenity}</span>
                </div>
              );
            })}
          </div>

          {/* Guest Info */}
          {room.guest && (
            <div className="bg-accent p-3 rounded-lg">
              <p className="text-sm font-medium">Current Guest: {room.guest}</p>
              {room.checkOut && (
                <p className="text-xs text-muted-foreground">Check-out: {room.checkOut}</p>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
            <Button size="sm" variant="outline" className="flex-1">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Rooms: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const filteredRooms = mockRooms.filter(room => {
    const matchesSearch = room.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || room.status === statusFilter;
    const matchesType = typeFilter === 'all' || room.type.toLowerCase() === typeFilter.toLowerCase();
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const statusCounts = {
    total: mockRooms.length,
    available: mockRooms.filter(r => r.status === 'available').length,
    occupied: mockRooms.filter(r => r.status === 'occupied').length,
    cleaning: mockRooms.filter(r => r.status === 'cleaning').length,
    maintenance: mockRooms.filter(r => r.status === 'maintenance').length,
    reserved: mockRooms.filter(r => r.status === 'reserved').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Room Management</h1>
          <p className="text-muted-foreground">Manage your hotel rooms and availability</p>
        </div>
        <Button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Add Room
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-6">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{statusCounts.total}</div>
            <div className="text-sm text-muted-foreground">Total Rooms</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-success">{statusCounts.available}</div>
            <div className="text-sm text-muted-foreground">Available</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-destructive">{statusCounts.occupied}</div>
            <div className="text-sm text-muted-foreground">Occupied</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-warning">{statusCounts.cleaning}</div>
            <div className="text-sm text-muted-foreground">Cleaning</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-muted-foreground">{statusCounts.maintenance}</div>
            <div className="text-sm text-muted-foreground">Maintenance</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{statusCounts.reserved}</div>
            <div className="text-sm text-muted-foreground">Reserved</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search rooms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="occupied">Occupied</SelectItem>
                <SelectItem value="cleaning">Cleaning</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="reserved">Reserved</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Bed className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="deluxe">Deluxe</SelectItem>
                <SelectItem value="suite">Suite</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Room Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredRooms.map((room) => (
          <RoomCard key={room.id} room={room} />
        ))}
      </div>

      {filteredRooms.length === 0 && (
        <div className="text-center py-12">
          <Bed className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No rooms found</h3>
          <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Rooms;