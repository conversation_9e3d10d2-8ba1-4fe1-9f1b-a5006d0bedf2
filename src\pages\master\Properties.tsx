import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash2, Search, Eye, X, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import PropertiesForm from "@/components/master-tables/PropertiesForm";
import { motion } from "framer-motion";

interface Property {
  id: string;
  name: string;
  code: string;
  description?: string;
  type: string;
  category: string;
  star_rating: number;
  email?: string;
  phone?: string;
  address_line1?: string;
  address_line2?: string;
  city: string;
  state: string;
  country?: string;
  pincode?: string;
  is_active: boolean;
  created_at: string;
}

const Properties = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showForm, setShowForm] = useState(false);
  const [editingProperty, setEditingProperty] = useState<Property | null>(null);
  const [viewingProperty, setViewingProperty] = useState<Property | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchProperties();
  }, []);

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from("properties_admin")
        .select("*")
        .eq("is_deleted", false)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setProperties(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch properties",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this property?")) return;

    try {
      const { error } = await supabase
        .from("properties_admin")
        .update({ is_deleted: true })
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Property deleted successfully",
      });
      fetchProperties();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete property",
        variant: "destructive",
      });
    }
  };

  const filteredProperties = properties.filter(
    (property) =>
      property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.city.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-80" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
          Properties Master
        </h1>
        <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Property
        </Button>
      </motion.div>

      <motion.div
        className="flex items-center space-x-4"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search properties..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </motion.div>

      <motion.div
        className="grid gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {filteredProperties.map((property, index) => (
          <motion.div
            key={property.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            whileHover={{ scale: 1.01, y: -2 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant hover:shadow-card transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                  <div className="flex-1">
                    <CardTitle className="flex flex-wrap items-center gap-2 text-lg">
                      {property.name}
                      <Badge
                        variant={property.is_active ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {property.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Code: {property.code}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewingProperty(property)}
                      className="hover:bg-primary/10 hover:border-primary/20"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingProperty(property);
                        setShowForm(true);
                      }}
                      className="hover:bg-warning/10 hover:border-warning/20"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(property.id)}
                      className="hover:bg-destructive/10 hover:border-destructive/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                  <div className="flex flex-col">
                    <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                      Type
                    </span>
                    <span className="text-foreground">{property.type}</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                      Category
                    </span>
                    <span className="text-foreground">{property.category}</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                      Rating
                    </span>
                    <span className="text-foreground flex items-center gap-1">
                      {property.star_rating}{" "}
                      <span className="text-gold">⭐</span>
                    </span>
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium text-muted-foreground text-xs uppercase tracking-wide">
                      Location
                    </span>
                    <span className="text-foreground">
                      {property.city}, {property.state}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {showForm && (
        <PropertiesForm
          property={editingProperty}
          onClose={() => {
            setShowForm(false);
            setEditingProperty(null);
          }}
          onSuccess={() => {
            fetchProperties();
            setShowForm(false);
            setEditingProperty(null);
          }}
        />
      )}

      {viewingProperty && (
        <Dialog
          open={!!viewingProperty}
          onOpenChange={() => setViewingProperty(null)}
        >
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader className="sticky top-0 bg-background z-10 pb-4">
              <DialogTitle>Property Details</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 pb-4">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Property Name
                  </label>
                  <p className="text-base font-medium">
                    {viewingProperty.name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Property Code
                  </label>
                  <p className="text-base">{viewingProperty.code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Type
                  </label>
                  <p className="text-base">{viewingProperty.type}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Category
                  </label>
                  <p className="text-base">{viewingProperty.category}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Star Rating
                  </label>
                  <p className="text-base">{viewingProperty.star_rating} ⭐</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Status
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        viewingProperty.is_active
                          ? "bg-green-500"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <Badge
                      variant={
                        viewingProperty.is_active ? "default" : "secondary"
                      }
                      className="font-medium"
                    >
                      {viewingProperty.is_active ? "✓ Active" : "✕ Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>

              {viewingProperty.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Description
                  </label>
                  <p className="text-base mt-1">
                    {viewingProperty.description}
                  </p>
                </div>
              )}

              <div className="border-t pt-3">
                <h4 className="font-medium mb-2">Contact Information</h4>
                <div className="grid grid-cols-2 gap-3">
                  {viewingProperty.email && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Email
                      </label>
                      <p className="text-base">{viewingProperty.email}</p>
                    </div>
                  )}
                  {viewingProperty.phone && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Phone
                      </label>
                      <p className="text-base">{viewingProperty.phone}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="border-t pt-3">
                <h4 className="font-medium mb-2">Address</h4>
                <div className="grid grid-cols-2 gap-3">
                  {viewingProperty.address_line1 && (
                    <div className="col-span-2">
                      <label className="text-sm font-medium text-muted-foreground">
                        Address
                      </label>
                      <p className="text-base">
                        {viewingProperty.address_line1}
                      </p>
                      {viewingProperty.address_line2 && (
                        <p className="text-base">
                          {viewingProperty.address_line2}
                        </p>
                      )}
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      City
                    </label>
                    <p className="text-base">{viewingProperty.city}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      State
                    </label>
                    <p className="text-base">{viewingProperty.state}</p>
                  </div>
                  {viewingProperty.country && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Country
                      </label>
                      <p className="text-base">{viewingProperty.country}</p>
                    </div>
                  )}
                  {viewingProperty.pincode && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Pincode
                      </label>
                      <p className="text-base">{viewingProperty.pincode}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="border-t pt-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Created At
                  </label>
                  <p className="text-base">
                    {new Date(viewingProperty.created_at).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Properties;
