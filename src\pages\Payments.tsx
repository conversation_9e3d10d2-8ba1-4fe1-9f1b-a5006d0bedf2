import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  CreditCard, 
  Search, 
  Filter,
  Plus,
  Eye,
  Download,
  DollarSign,
  User,
  Calendar,
  Receipt,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';

interface Payment {
  id: string;
  transactionId: string;
  guestName: string;
  roomNumber: string;
  bookingId: string;
  amount: number;
  method: 'cash' | 'card' | 'bank-transfer' | 'mobile-payment';
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  type: 'booking' | 'room-service' | 'restaurant' | 'laundry' | 'minibar' | 'damage';
  date: string;
  processedBy: string;
  description: string;
  refundAmount?: number;
}

const mockPayments: Payment[] = [
  {
    id: '1',
    transactionId: 'TXN-2024-001',
    guestName: '<PERSON>',
    roomNumber: '205',
    bookingId: 'HTL001234',
    amount: 450.00,
    method: 'card',
    status: 'completed',
    type: 'booking',
    date: '2024-01-15T14:30:00Z',
    processedBy: 'Lisa Chen',
    description: '3-night stay in Deluxe Room'
  },
  {
    id: '2',
    transactionId: 'TXN-2024-002',
    guestName: 'Emma Wilson',
    roomNumber: '108',
    bookingId: 'HTL001235',
    amount: 45.50,
    method: 'cash',
    status: 'completed',
    type: 'room-service',
    date: '2024-01-15T19:45:00Z',
    processedBy: 'Maria Garcia',
    description: 'Room service - Dinner order'
  },
  {
    id: '3',
    transactionId: 'TXN-2024-003',
    guestName: 'Michael Brown',
    roomNumber: '301',
    bookingId: 'HTL001236',
    amount: 1200.00,
    method: 'bank-transfer',
    status: 'pending',
    type: 'booking',
    date: '2024-01-15T16:20:00Z',
    processedBy: 'John Rodriguez',
    description: 'Conference booking - 5 rooms'
  },
  {
    id: '4',
    transactionId: 'TXN-2024-004',
    guestName: 'Sarah Johnson',
    roomNumber: '102',
    bookingId: 'HTL001237',
    amount: 75.00,
    method: 'card',
    status: 'refunded',
    type: 'damage',
    date: '2024-01-14T12:15:00Z',
    processedBy: 'Lisa Chen',
    description: 'Towel replacement charge',
    refundAmount: 75.00
  },
  {
    id: '5',
    transactionId: 'TXN-2024-005',
    guestName: 'David Chen',
    roomNumber: '309',
    bookingId: 'HTL001238',
    amount: 28.50,
    method: 'mobile-payment',
    status: 'failed',
    type: 'minibar',
    date: '2024-01-15T22:10:00Z',
    processedBy: 'Sofia Martinez',
    description: 'Minibar charges'
  }
];

const statusColors = {
  completed: 'bg-success text-success-foreground',
  pending: 'bg-warning text-warning-foreground',
  failed: 'bg-destructive text-destructive-foreground',
  refunded: 'bg-muted text-muted-foreground'
};

const methodColors = {
  cash: 'bg-green-100 text-green-800',
  card: 'bg-blue-100 text-blue-800',
  'bank-transfer': 'bg-purple-100 text-purple-800',
  'mobile-payment': 'bg-orange-100 text-orange-800'
};

const typeColors = {
  booking: 'bg-primary text-primary-foreground',
  'room-service': 'bg-blue-100 text-blue-800',
  restaurant: 'bg-green-100 text-green-800',
  laundry: 'bg-cyan-100 text-cyan-800',
  minibar: 'bg-yellow-100 text-yellow-800',
  damage: 'bg-red-100 text-red-800'
};

const PaymentCard: React.FC<{ payment: Payment }> = ({ payment }) => {
  const getStatusIcon = () => {
    switch (payment.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4" />;
      case 'refunded':
        return <Receipt className="h-4 w-4" />;
    }
  };

  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{payment.transactionId}</CardTitle>
            <p className="text-muted-foreground">{payment.guestName} • Room {payment.roomNumber}</p>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <Badge className={statusColors[payment.status]}>
              {getStatusIcon()}
              <span className="ml-1">{payment.status}</span>
            </Badge>
            <Badge className={typeColors[payment.type]}>
              {payment.type.replace('-', ' ')}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Amount */}
          <div className="text-center bg-accent p-4 rounded-lg">
            <div className="text-2xl font-bold">
              ${payment.amount.toFixed(2)}
            </div>
            {payment.refundAmount && (
              <div className="text-sm text-destructive">
                Refunded: ${payment.refundAmount.toFixed(2)}
              </div>
            )}
          </div>

          {/* Payment Method */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Payment Method:</span>
            </div>
            <Badge className={methodColors[payment.method]}>
              {payment.method.replace('-', ' ')}
            </Badge>
          </div>

          {/* Date & Time */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Date:</span>
            </div>
            <span className="text-sm">{new Date(payment.date).toLocaleString()}</span>
          </div>

          {/* Processed By */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Processed By:</span>
            </div>
            <span className="text-sm">{payment.processedBy}</span>
          </div>

          {/* Description */}
          <div className="bg-muted p-3 rounded-lg">
            <p className="text-sm">{payment.description}</p>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
            <Button size="sm" variant="outline" className="flex-1">
              <Download className="h-4 w-4 mr-1" />
              Receipt
            </Button>
            {payment.status === 'completed' && (
              <Button size="sm" variant="outline" className="flex-1">
                <Receipt className="h-4 w-4 mr-1" />
                Refund
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Payments: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [methodFilter, setMethodFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const filteredPayments = mockPayments.filter(payment => {
    const matchesSearch = 
      payment.guestName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.roomNumber.includes(searchTerm);
    
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    const matchesMethod = methodFilter === 'all' || payment.method === methodFilter;
    const matchesType = typeFilter === 'all' || payment.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesMethod && matchesType;
  });

  const paymentStats = {
    total: mockPayments.length,
    totalAmount: mockPayments.reduce((sum, p) => sum + p.amount, 0),
    completed: mockPayments.filter(p => p.status === 'completed').length,
    pending: mockPayments.filter(p => p.status === 'pending').length,
    failed: mockPayments.filter(p => p.status === 'failed').length,
    refunded: mockPayments.filter(p => p.status === 'refunded').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Payment Management</h1>
          <p className="text-muted-foreground">Track and manage all hotel payments and transactions</p>
        </div>
        <Button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Record Payment
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-6">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{paymentStats.total}</div>
            <div className="text-sm text-muted-foreground">Total</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">${paymentStats.totalAmount.toFixed(0)}</div>
            <div className="text-sm text-muted-foreground">Amount</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-success">{paymentStats.completed}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-warning">{paymentStats.pending}</div>
            <div className="text-sm text-muted-foreground">Pending</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-destructive">{paymentStats.failed}</div>
            <div className="text-sm text-muted-foreground">Failed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-muted-foreground">{paymentStats.refunded}</div>
            <div className="text-sm text-muted-foreground">Refunded</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search payments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="refunded">Refunded</SelectItem>
              </SelectContent>
            </Select>

            <Select value={methodFilter} onValueChange={setMethodFilter}>
              <SelectTrigger>
                <CreditCard className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Methods</SelectItem>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="card">Card</SelectItem>
                <SelectItem value="bank-transfer">Bank Transfer</SelectItem>
                <SelectItem value="mobile-payment">Mobile Payment</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <DollarSign className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="booking">Booking</SelectItem>
                <SelectItem value="room-service">Room Service</SelectItem>
                <SelectItem value="restaurant">Restaurant</SelectItem>
                <SelectItem value="laundry">Laundry</SelectItem>
                <SelectItem value="minibar">Minibar</SelectItem>
                <SelectItem value="damage">Damage</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Payments Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredPayments.map((payment) => (
          <PaymentCard key={payment.id} payment={payment} />
        ))}
      </div>

      {filteredPayments.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No payments found</h3>
          <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Payments;