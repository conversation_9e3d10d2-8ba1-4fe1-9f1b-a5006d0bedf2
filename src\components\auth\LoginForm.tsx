import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Hotel, Lock, Mail } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

import heroImage from '@/assets/hotel-hero.jpg';

interface LoginFormProps {
  onLogin: (email: string, role: string) => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onLogin }) => {
  const { signIn } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const { data, error: authError } = await signIn(email, password);

      if (authError) {
        throw new Error(authError.message);
      }

      if (data.user) {
        // Default role for all users
        const userRole = 'hotel-manager';
        onLogin(data.user.email || '', userRole);
      }
    } catch (error: any) {
      setError(error.message || 'Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex bg-[#1e2a23]">
      {/* Hero Section */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">

        <div className="absolute inset-0 bg-gradient-to-br from-emerald-950/80 via-emerald-950/60 to-emerald-950/80"></div>
        <div className="absolute inset-0 flex flex-col justify-center px-12 text-white">
          <div className="max-w-md">
            <img src="/logo2.png" alt="Meadow De Jalsa" className="h-32 w-42 rounded-full object-contain mb-6" />
            <h1 className="text-4xl font-bold mb-2 leading-tight font-serif">
              Meadow De
            </h1>
            <h1 className="text-4xl font-bold mb-6 leading-tight font-mono italic">
              Jalsa
            </h1>
            <p className="text-lg opacity-90 leading-relaxed">
              Access your luxury hospitality management dashboard.
              Manage bookings, events, and guest experiences seamlessly.
            </p>
          </div>
        </div>
      </div>

      {/* Login Section */}
      <div className="w-full lg:w-1/2 flex items-center justify-center bg-white p-8">
        <Card className="w-full max-w-md shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 rounded-full bg-[#344F1F] flex items-center justify-center">
                <img src="/logo3.png" alt="Meadow De Jalsa" className="h-10 w-10 rounded-full object-contain" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-[#1e2a23] font-serif">
              Welcome Back
            </CardTitle>
            <CardDescription className="text-slate-600">
              Sign in to your Meadow De Jalsa dashboard
            </CardDescription>
          </CardHeader>

          <CardContent>


            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>



              {error && (
                <div className="text-red-500 text-sm text-center">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                className="w-full bg-[#81928d] hover:bg-[#6d7d78] text-white h-11 text-base font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                disabled={loading}
              >
                {loading ? 'Signing In...' : 'Sign In'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <button
                type="button"
                className="text-sm text-slate-500 hover:text-[#81928d] transition-colors"
              >
                Forgot your password?
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LoginForm;