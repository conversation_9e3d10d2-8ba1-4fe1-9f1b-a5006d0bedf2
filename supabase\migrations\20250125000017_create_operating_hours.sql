-- Create operating_hours table
CREATE TABLE IF NOT EXISTS public.operating_hours (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  service_name text NOT NULL,
  icon text NOT NULL,
  schedule jsonb NOT NULL DEFAULT '{}',
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.operating_hours ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to operating_hours" ON public.operating_hours
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage operating_hours" ON public.operating_hours
  FOR ALL USING (auth.role() = 'authenticated');

-- <PERSON>reate updated_at trigger
CREATE TRIGGER handle_operating_hours_updated_at
  BEFORE UPDATE ON public.operating_hours
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample operating hours
INSERT INTO public.operating_hours (service_name, icon, schedule, display_order, is_active) VALUES
('Restaurant', 'Utensils', '{"Lunch": "11:00 AM - 3:00 PM", "Dinner": "7:00 PM - 11:00 PM", "Days": "All Week"}', 1, true),
('Hotel', 'Building2', '{"Check-in": "2:00 PM", "Check-out": "12:00 PM", "Reception": "24/7 Available"}', 2, true),
('Events', 'PartyPopper', '{"Booking": "Advance Required", "Setup": "Flexible Timing", "Support": "Full Day Available"}', 3, true);