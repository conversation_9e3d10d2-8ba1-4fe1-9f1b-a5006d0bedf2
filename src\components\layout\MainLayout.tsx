import React, { useState } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { useScrollToTop } from '@/hooks/useScrollToTop';
import ScrollToTop from '@/components/ui/ScrollToTop';

interface MainLayoutProps {
  children: React.ReactNode;
  userRole: string;
  onLogout: () => void;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children, userRole, onLogout }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  useScrollToTop();

  return (
    <div className="flex h-screen bg-gradient-subtle">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden" 
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} fixed inset-y-0 left-0 z-50 w-64 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <Sidebar userRole={userRole} onLogout={onLogout} onClose={() => setSidebarOpen(false)} />
      </div>
      
      <div className="flex-1 flex flex-col min-h-0 lg:ml-0">
        <Header userRole={userRole} onMenuClick={() => setSidebarOpen(true)} />
        
        <main className="flex-1 overflow-y-auto overflow-x-hidden">
          <div className="p-4 lg:p-6 max-w-full">
            {children}
          </div>
        </main>
      </div>
      
      <ScrollToTop />
    </div>
  );
};

export default MainLayout;