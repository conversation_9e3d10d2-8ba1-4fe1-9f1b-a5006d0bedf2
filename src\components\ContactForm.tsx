import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Send, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface ContactFormProps {
  variant?: 'light' | 'dark';
  className?: string;
}

interface FormData {
  fullName: string;
  phone: string;
  email: string;
  inquiryType: string;
  preferredDate: string;
  message: string;
  agreeToContact: boolean;
}

const ContactForm: React.FC<ContactFormProps> = ({ variant = 'light', className = '' }) => {
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    phone: '',
    email: '',
    inquiryType: 'Room Reservation',
    preferredDate: '',
    message: '',
    agreeToContact: false
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const validateForm = (): boolean => {
    if (!formData.fullName.trim()) {
      setErrorMessage('Full name is required');
      return false;
    }
    if (!formData.phone.trim()) {
      setErrorMessage('Phone number is required');
      return false;
    }
    if (!formData.message.trim()) {
      setErrorMessage('Message is required');
      return false;
    }
    if (!formData.agreeToContact) {
      setErrorMessage('Please agree to be contacted regarding your enquiry');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const { error } = await supabase
        .from('contact_messages')
        .insert({
          full_name: formData.fullName.trim(),
          phone: formData.phone.trim(),
          email: formData.email.trim() || null,
          inquiry_type: formData.inquiryType,
          preferred_date: formData.preferredDate || null,
          message: formData.message.trim(),
          status: 'new'
        });

      if (error) throw error;

      setSubmitStatus('success');
      setFormData({
        fullName: '',
        phone: '',
        email: '',
        inquiryType: 'Room Reservation',
        preferredDate: '',
        message: '',
        agreeToContact: false
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus('error');
      setErrorMessage('Failed to send message. Please try again or call us directly.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isDark = variant === 'dark';
  const inputClasses = isDark 
    ? "w-full rounded-lg bg-white/90 text-slate-900 placeholder-slate-500 px-4 py-3 ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none"
    : "w-full px-4 py-3 rounded-lg ring-1 ring-slate-200 focus:ring-emerald-400 focus:outline-none text-slate-900 placeholder-slate-500";

  if (submitStatus === 'success') {
    return (
      <div className={`rounded-2xl p-8 ${isDark ? 'bg-white/5 ring-1 ring-white/10' : 'bg-white ring-1 ring-slate-200'} ${className}`}>
        <div className="text-center">
          <div className="h-16 w-16 rounded-full bg-emerald-100 text-emerald-600 grid place-items-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8" />
          </div>
          <h3 className={`text-xl font-semibold mb-2 ${isDark ? 'text-white' : 'text-emerald-950'}`}>
            Message Sent Successfully!
          </h3>
          <p className={`text-sm mb-6 ${isDark ? 'text-white/80' : 'text-slate-600'}`}>
            Thank you for contacting us. We'll get back to you within 24 hours.
          </p>
          <button
            onClick={() => setSubmitStatus('idle')}
            className="inline-flex items-center gap-2 rounded-full px-5 py-3 text-sm font-medium text-emerald-950 ring-1 ring-amber-400/50 bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 hover:ring-amber-500/70 transition"
          >
            Send Another Message
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`rounded-2xl p-8 ${isDark ? 'bg-white/5 ring-1 ring-white/10' : 'bg-white ring-1 ring-slate-200'} ${className}`}>
      <h3 className={`text-2xl font-semibold mb-2 ${isDark ? 'text-white' : 'text-emerald-950'}`} style={{ fontFamily: "'Cormorant Garamond', serif" }}>
        Send us a Message
      </h3>
      <p className={`text-sm mb-6 ${isDark ? 'text-white/80' : 'text-slate-600'}`}>
        Fill out the form below and we'll get back to you shortly
      </p>

      {submitStatus === 'error' && (
        <div className="mb-4 p-4 rounded-lg bg-red-50 ring-1 ring-red-200 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
          <p className="text-sm text-red-700">{errorMessage}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid sm:grid-cols-2 gap-4">
          <div>
            <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-white/90' : 'text-slate-700'}`}>
              Full Name *
            </label>
            <input
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              required
              placeholder="Your full name"
              className={inputClasses}
            />
          </div>
          <div>
            <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-white/90' : 'text-slate-700'}`}>
              Phone Number *
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              required
              placeholder="Your phone number"
              className={inputClasses}
            />
          </div>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-white/90' : 'text-slate-700'}`}>
            Email Address
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="<EMAIL> (optional)"
            className={inputClasses}
          />
        </div>

        <div className="grid sm:grid-cols-2 gap-4">
          <div>
            <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-white/90' : 'text-slate-700'}`}>
              Inquiry Type
            </label>
            <select
              name="inquiryType"
              value={formData.inquiryType}
              onChange={handleInputChange}
              className={`${inputClasses} ${isDark ? 'bg-white/90' : 'bg-white'}`}
            >
              <option value="Room Reservation">Room Reservation</option>
              <option value="Event Booking">Event Booking</option>
              <option value="Restaurant Reservation">Restaurant Reservation</option>
              <option value="Wedding">Wedding</option>
              <option value="Corporate Event">Corporate Event</option>
              <option value="General Inquiry">General Inquiry</option>
              <option value="Feedback">Feedback</option>
            </select>
          </div>
          <div>
            <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-white/90' : 'text-slate-700'}`}>
              Preferred Date
            </label>
            <input
              type="date"
              name="preferredDate"
              value={formData.preferredDate}
              onChange={handleInputChange}
              className={inputClasses}
            />
          </div>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-white/90' : 'text-slate-700'}`}>
            Message *
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            rows={5}
            required
            placeholder="Tell us about your requirements..."
            className={inputClasses}
          />
        </div>

        <div className="flex items-start gap-3">
          <input
            type="checkbox"
            name="agreeToContact"
            checked={formData.agreeToContact}
            onChange={handleInputChange}
            required
            className="mt-1 rounded border-slate-300 text-emerald-600 focus:ring-emerald-400"
          />
          <label className={`text-sm ${isDark ? 'text-white/80' : 'text-slate-600'}`}>
            I agree to be contacted regarding my enquiry. *
          </label>
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full inline-flex items-center justify-center gap-2 rounded-full px-6 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Send className="w-5 h-5" />
              Send Message
            </>
          )}
        </button>
      </form>
    </div>
  );
};

export default ContactForm;