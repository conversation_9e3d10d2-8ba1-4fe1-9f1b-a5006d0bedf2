import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { ImageUploadField } from './ImageUploadField';
import { IconDropdown } from '@/components/ui/icon-dropdown';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  X,
  Image as ImageIcon,
  Star,
  MapPin,
  Upload
} from 'lucide-react';



interface Property {
  id: string;
  name: string;
  description: string;
  image_url: string;
  location: string;
  price_per_night: number;
  rating: number;
  amenities: string[];
  property_type: string;
  capacity: string;
  size: string;
  features: string[];
  original_price: number;
  discount_percentage: number;
  available: boolean;
  is_featured: boolean;
  is_active: boolean;
}

interface SwipeCard {
  id: string;
  title: string;
  description: string;
  image_url: string;
  display_order: number;
  is_active: boolean;
}

interface EventShowcase {
  id: string;
  title: string;
  description: string;
  image_url: string;
  category: string;
  display_order: number;
  is_active: boolean;
}

interface CompanySetting {
  id: string;
  setting_key: string;
  setting_value: string;
  setting_type: string;
  category: string;
  description: string;
  is_active: boolean;
}

interface TeamMember {
  id: string;
  name: string;
  position: string;
  experience: string;
  specialty: string;
  bio: string;
  image_url: string;
  display_order: number;
  is_active: boolean;
}

interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
  display_order: number;
  is_active: boolean;
}

interface DiningExperience {
  id: string;
  title: string;
  description: string;
  icon: string;
  items: string[];
  display_order: number;
  is_active: boolean;
}

interface Menu {
  id: string;
  title: string;
  description: string;
  pdf_url: string;
  display_order: number;
  is_active: boolean;
}

interface AccommodationType {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
  price: string;
  display_order: number;
  is_active: boolean;
}

interface EventPackage {
  id: string;
  title: string;
  description: string;
  capacity: string;
  features: string[];
  price: string;
  display_order: number;
  is_active: boolean;
}

interface Amenity {
  id: string;
  title: string;
  display_order: number;
  is_active: boolean;
}

interface OperatingHour {
  id: string;
  service_name: string;
  icon: string;
  schedule: Record<string, string>;
  display_order: number;
  is_active: boolean;
}

interface CelebrationEvent {
  id: string;
  title: string;
  description: string;
  image_url: string;
  category: string;
  display_order: number;
  is_active: boolean;
}

interface EventVenuePackage {
  id: string;
  title: string;
  description: string;
  icon: string;
  price: string;
  display_order: number;
  is_active: boolean;
}

interface GalleryImage {
  id: string;
  title: string;
  image_url: string;
  display_order: number;
  is_active: boolean;
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  display_order: number;
  is_active: boolean;
}

interface FeatureFlag {
  id: string;
  flag_key: string;
  flag_name: string;
  description: string;
  is_enabled: boolean;
  category: string;
}

interface ContactMessage {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  inquiry_type: string;
  preferred_date: string;
  message: string;
  status: string;
  created_at: string;
}

export const ContentManager: React.FC = () => {

  const [properties, setProperties] = useState<Property[]>([]);
  const [swipeCards, setSwipeCards] = useState<SwipeCard[]>([]);
  const [eventsShowcase, setEventsShowcase] = useState<EventShowcase[]>([]);
  const [companySettings, setCompanySettings] = useState<CompanySetting[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [diningExperiences, setDiningExperiences] = useState<DiningExperience[]>([]);
  const [menus, setMenus] = useState<Menu[]>([]);
  const [accommodationTypes, setAccommodationTypes] = useState<AccommodationType[]>([]);
  const [eventPackages, setEventPackages] = useState<EventPackage[]>([]);
  const [amenities, setAmenities] = useState<Amenity[]>([]);
  const [operatingHours, setOperatingHours] = useState<OperatingHour[]>([]);
  const [celebrationEvents, setCelebrationEvents] = useState<CelebrationEvent[]>([]);
  const [eventVenuePackages, setEventVenuePackages] = useState<EventVenuePackage[]>([]);
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);
  const [contactMessages, setContactMessages] = useState<ContactMessage[]>([]);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [featureFlags, setFeatureFlags] = useState<FeatureFlag[]>([]);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [editingType, setEditingType] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [activeTab, setActiveTab] = useState(() => {
    return localStorage.getItem('contentManagerActiveTab') || 'swipe-cards';
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchAllContent();
  }, []);

  const fetchAllContent = async () => {
    try {
      const [propertiesData, swipeCardsData, eventsData, settingsData, teamData, servicesData, diningData, menusData, accommodationData, eventPackagesData, amenitiesData, operatingHoursData, celebrationEventsData, eventVenuePackagesData, galleryImagesData, contactMessagesData, faqsData, featureFlagsData] = await Promise.all([
        supabase.from('properties').select('*').order('created_at', { ascending: false }),
        supabase.from('swipe_cards').select('*').order('display_order'),
        supabase.from('events_showcase').select('*').order('display_order'),
        supabase.from('company_settings').select('*').order('category, setting_key'),
        supabase.from('team_members').select('*').order('display_order'),
        supabase.from('services').select('*').order('display_order'),
        supabase.from('dining_experiences').select('*').order('display_order'),
        supabase.from('menus').select('*').order('display_order'),
        supabase.from('accommodation_types').select('*').order('display_order'),
        supabase.from('event_packages').select('*').order('display_order'),
        supabase.from('amenities').select('*').order('display_order'),
        supabase.from('operating_hours').select('*').order('display_order'),
        supabase.from('celebration_events').select('*').order('display_order'),
        supabase.from('event_venue_packages').select('*').order('display_order'),
        supabase.from('gallery_images').select('*').order('display_order'),
        supabase.from('contact_messages').select('*').order('created_at', { ascending: false }),
        supabase.from('faqs').select('*').order('display_order'),
        supabase.from('feature_flags').select('*').order('category, flag_name')
      ]);

      console.log('Settings data:', settingsData);


      setProperties(propertiesData.data || []);
      setSwipeCards(swipeCardsData.data || []);
      setEventsShowcase(eventsData.data || []);
      setCompanySettings(settingsData.data || []);
      setTeamMembers(teamData.data || []);
      setServices(servicesData.data || []);
      setDiningExperiences(diningData.data || []);
      setMenus(menusData.data || []);
      setAccommodationTypes(accommodationData.data || []);
      setEventPackages(eventPackagesData.data || []);
      setAmenities(amenitiesData.data || []);
      setOperatingHours(operatingHoursData.data || []);
      setCelebrationEvents(celebrationEventsData.data || []);
      setEventVenuePackages(eventVenuePackagesData.data || []);
      setGalleryImages(galleryImagesData.data || []);
      setContactMessages(contactMessagesData.data || []);
      setFaqs(faqsData.data || []);
      setFeatureFlags(featureFlagsData.data || []);
    } catch (error) {
      console.error('Error fetching content:', error);
      toast({
        title: "Error",
        description: "Failed to fetch content",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (type: string, data: any) => {
    try {
      console.log('Saving:', type, data);
      
      // Clean data - remove undefined values and ensure proper types
      const cleanData = Object.fromEntries(
        Object.entries(data).filter(([_, v]) => v !== undefined && v !== '')
      );
      
      // Ensure numeric fields are properly typed
      if (cleanData.price_per_night) {
        cleanData.price_per_night = parseFloat(cleanData.price_per_night);
      }
      if (cleanData.original_price) {
        cleanData.original_price = parseFloat(cleanData.original_price);
      }
      if (cleanData.rating) {
        cleanData.rating = parseFloat(cleanData.rating);
      }
      if (cleanData.discount_percentage) {
        cleanData.discount_percentage = parseInt(cleanData.discount_percentage);
      }
      if (cleanData.display_order) {
        cleanData.display_order = parseInt(cleanData.display_order);
      }
      
      let result;
      if (cleanData.id) {
        // Update existing item
        const { id, created_at, updated_at, ...updateData } = cleanData;
        result = await supabase
          .from(type as 'properties' | 'swipe_cards' | 'events_showcase' | 'team_members' | 'services' | 'dining_experiences' | 'menus' | 'accommodation_types' | 'event_packages' | 'amenities' | 'operating_hours' | 'celebration_events' | 'event_venue_packages' | 'gallery_images' | 'faqs' | 'feature_flags')
          .update(updateData)
          .eq('id', id)
          .select();
      } else {
        // Create new item
        const { id, created_at, updated_at, ...insertData } = cleanData;
        result = await supabase
          .from(type as 'properties' | 'swipe_cards' | 'events_showcase' | 'team_members' | 'services' | 'dining_experiences' | 'menus' | 'accommodation_types' | 'event_packages' | 'amenities' | 'operating_hours' | 'celebration_events' | 'event_venue_packages' | 'gallery_images' | 'faqs' | 'feature_flags')
          .insert([insertData])
          .select();
      }

      console.log('Save result:', result);
      
      if (result.error) throw result.error;

      toast({
        title: "Success",
        description: `Item saved successfully`,
      });

      await fetchAllContent();
      setEditingItem(null);
      setEditingType('');
    } catch (error) {
      console.error('Error saving:', error);
      toast({
        title: "Error",
        description: `Failed to save: ${error.message}`,
        variant: "destructive"
      });
    }
  };

  const handleDelete = async (type: string, id: string) => {
    if (!confirm('Are you sure you want to delete this item?')) {
      return;
    }
    
    try {
      console.log('Deleting:', type, id);
      const result = await supabase
        .from(type as 'properties' | 'swipe_cards' | 'events_showcase' | 'team_members' | 'services' | 'dining_experiences' | 'menus' | 'accommodation_types' | 'event_packages' | 'amenities' | 'operating_hours' | 'celebration_events' | 'event_venue_packages' | 'gallery_images' | 'faqs' | 'feature_flags')
        .delete()
        .eq('id', id);

      console.log('Delete result:', result);
      
      if (result.error) throw result.error;

      // Force refresh by updating state directly
      if (type === 'properties') {
        setProperties(prev => prev.filter(p => p.id !== id));

      } else if (type === 'swipe_cards') {
        setSwipeCards(prev => prev.filter(p => p.id !== id));
      } else if (type === 'events_showcase') {
        setEventsShowcase(prev => prev.filter(p => p.id !== id));
      } else if (type === 'team_members') {
        setTeamMembers(prev => prev.filter(p => p.id !== id));
      } else if (type === 'services') {
        setServices(prev => prev.filter(p => p.id !== id));
      } else if (type === 'dining_experiences') {
        setDiningExperiences(prev => prev.filter(p => p.id !== id));
      } else if (type === 'menus') {
        setMenus(prev => prev.filter(p => p.id !== id));
      } else if (type === 'accommodation_types') {
        setAccommodationTypes(prev => prev.filter(p => p.id !== id));
      } else if (type === 'event_packages') {
        setEventPackages(prev => prev.filter(p => p.id !== id));
      } else if (type === 'amenities') {
        setAmenities(prev => prev.filter(p => p.id !== id));
      } else if (type === 'operating_hours') {
        setOperatingHours(prev => prev.filter(p => p.id !== id));
      } else if (type === 'celebration_events') {
        setCelebrationEvents(prev => prev.filter(p => p.id !== id));
      } else if (type === 'event_venue_packages') {
        setEventVenuePackages(prev => prev.filter(p => p.id !== id));
      } else if (type === 'gallery_images') {
        setGalleryImages(prev => prev.filter(p => p.id !== id));
      } else if (type === 'faqs') {
        setFaqs(prev => prev.filter(p => p.id !== id));
      } else if (type === 'feature_flags') {
        setFeatureFlags(prev => prev.filter(p => p.id !== id));
      }

      toast({
        title: "Success",
        description: "Item deleted successfully",
      });

      fetchAllContent();
    } catch (error) {
      console.error('Error deleting:', error);
      toast({
        title: "Error",
        description: `Failed to delete: ${error.message}`,
        variant: "destructive"
      });
    }
  };

  const startEditing = (type: string, item?: any) => {
    setEditingType(type);
    if (item) {
      setEditingItem(item);
    } else {
      // Set default values based on type
      if (type === 'properties') {
        setEditingItem({
          name: '',
          description: '',
          image_url: '',
          location: '',
          property_type: 'hotel',
          capacity: '',
          size: '',
          price_per_night: 0,
          original_price: 0,
          discount_percentage: 0,
          rating: 4.5,
          amenities: [],
          features: [],
          available: true,
          is_featured: false,
          is_active: true
        });
      } else if (type === 'events_showcase') {
        setEditingItem({
          title: '',
          description: '',
          image_url: '',
          category: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'company_settings') {
        setEditingItem({
          setting_key: '',
          setting_value: '',
          setting_type: 'text',
          category: 'contact',
          description: '',
          is_active: true,
          is_deleted: false
        });
      } else if (type === 'team_members') {
        setEditingItem({
          name: '',
          position: '',
          experience: '',
          specialty: '',
          bio: '',
          image_url: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'services') {
        setEditingItem({
          title: '',
          description: '',
          icon: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'dining_experiences') {
        setEditingItem({
          title: '',
          description: '',
          icon: '',
          items: [],
          display_order: 0,
          is_active: true
        });
      } else if (type === 'menus') {
        setEditingItem({
          title: '',
          description: '',
          pdf_url: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'accommodation_types') {
        setEditingItem({
          title: '',
          description: '',
          icon: '',
          features: [],
          price: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'event_packages') {
        setEditingItem({
          title: '',
          description: '',
          capacity: '',
          features: [],
          price: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'amenities') {
        setEditingItem({
          title: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'operating_hours') {
        setEditingItem({
          service_name: '',
          icon: '',
          schedule: {},
          display_order: 0,
          is_active: true
        });
      } else if (type === 'celebration_events') {
        setEditingItem({
          title: '',
          description: '',
          image_url: '',
          category: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'event_venue_packages') {
        setEditingItem({
          title: '',
          description: '',
          icon: '',
          price: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'gallery_images') {
        setEditingItem({
          title: '',
          image_url: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'faqs') {
        setEditingItem({
          question: '',
          answer: '',
          display_order: 0,
          is_active: true
        });
      } else if (type === 'feature_flags') {
        setEditingItem({
          flag_key: '',
          flag_name: '',
          is_enabled: false,
          category: 'general'
        });
      } else {
        setEditingItem({
          title: '',
          description: '',
          image_url: '',
          display_order: 0,
          is_active: true
        });
      }
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `${editingType}/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      setEditingItem({
        ...editingItem,
        image_url: data.publicUrl
      });

      toast({
        title: "Success",
        description: "Image uploaded successfully",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Error",
        description: "Failed to upload image",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      event.target.value = '';
    }
  };

  const triggerImageUpload = () => {
    const input = document.getElementById('image-upload-input') as HTMLInputElement;
    input?.click();
  };



  if (loading) {
    return <div className="p-6">Loading...</div>;
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Content Management</h1>
        <p className="text-muted-foreground">Manage website content including carousel, features, and properties</p>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => {
        setActiveTab(value);
        localStorage.setItem('contentManagerActiveTab', value);
      }} className="space-y-6">
        <div className="overflow-x-auto">
          <TabsList className="grid w-max min-w-full grid-cols-9 md:w-full">
            <TabsTrigger value="swipe-cards" className="whitespace-nowrap">Swipe Cards</TabsTrigger>
            <TabsTrigger value="events" className="whitespace-nowrap">Events Showcase</TabsTrigger>
            <TabsTrigger value="properties" className="whitespace-nowrap">Properties</TabsTrigger>
            <TabsTrigger value="team" className="whitespace-nowrap">Team Members</TabsTrigger>
            <TabsTrigger value="dining" className="whitespace-nowrap">Services</TabsTrigger>
            <TabsTrigger value="celebrations" className="whitespace-nowrap">Events & Venues</TabsTrigger>
            <TabsTrigger value="messages" className="whitespace-nowrap">Contact Messages</TabsTrigger>
            <TabsTrigger value="flags" className="whitespace-nowrap">Feature Flags</TabsTrigger>
            <TabsTrigger value="settings" className="whitespace-nowrap">Settings</TabsTrigger>
          </TabsList>
        </div>



        <TabsContent value="swipe-cards" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Swipe Cards</h2>
            <Button onClick={() => startEditing('swipe_cards')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Swipe Card
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {swipeCards.map((card) => (
              <Card key={card.id}>
                <CardHeader className="pb-2">
                  <div className="aspect-video bg-muted rounded relative overflow-hidden">
                    <img 
                      src={card.image_url} 
                      alt={card.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-2">
                    <CardTitle className="text-lg">{card.title}</CardTitle>
                    <Badge variant={card.is_active ? "default" : "secondary"}>
                      {card.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <CardDescription className="mb-4">{card.description}</CardDescription>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      onClick={() => startEditing('swipe_cards', card)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => handleDelete('swipe_cards', card.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Events Showcase</h2>
            <Button onClick={() => startEditing('events_showcase')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Event
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {eventsShowcase.map((event) => (
              <Card key={event.id}>
                <CardHeader className="pb-2">
                  <div className="aspect-video bg-muted rounded relative overflow-hidden">
                    <img 
                      src={event.image_url} 
                      alt={event.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-2">
                    <CardTitle className="text-lg">{event.title}</CardTitle>
                    <Badge variant={event.is_active ? "default" : "secondary"}>
                      {event.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="mb-2 flex flex-wrap gap-1">
                    {event.category.split(',').map((cat, catIndex) => (
                      <Badge key={catIndex} variant="outline" className="text-xs">{cat.trim()}</Badge>
                    ))}
                  </div>
                  <CardDescription className="mb-4">{event.description}</CardDescription>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      onClick={() => startEditing('events_showcase', event)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => handleDelete('events_showcase', event.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>



        <TabsContent value="properties" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Properties</h2>
            <Button onClick={() => startEditing('properties')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {properties.map((property) => (
              <Card key={property.id}>
                <CardHeader className="pb-2">
                  <div className="aspect-video bg-muted rounded relative overflow-hidden">
                    <img 
                      src={property.image_url} 
                      alt={property.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-2">
                    <CardTitle className="text-lg">{property.name}</CardTitle>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-gold text-gold" />
                      <span className="text-sm">{property.rating}</span>
                    </div>
                  </div>
                  <CardDescription className="mb-2">{property.description}</CardDescription>
                  <p className="text-sm text-muted-foreground mb-2">
                    <MapPin className="h-4 w-4 inline mr-1" />
                    {property.location}
                  </p>
                  <p className="font-semibold mb-2">₹{property.price_per_night}/night</p>
                  <div className="flex flex-wrap gap-1 mb-4">
                    <Badge variant={property.is_active ? "default" : "secondary"}>
                      {property.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      onClick={() => startEditing('properties', property)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => handleDelete('properties', property.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="team" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Team Members</h2>
            <Button onClick={() => startEditing('team_members')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Team Member
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {teamMembers.map((member) => (
              <Card key={member.id}>
                <CardHeader className="pb-2">
                  <div className="aspect-square bg-muted rounded relative overflow-hidden">
                    <img 
                      src={member.image_url || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=400&auto=format&fit=crop'} 
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-2">
                    <CardTitle className="text-lg">{member.name}</CardTitle>
                    <Badge variant={member.is_active ? "default" : "secondary"}>
                      {member.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="mb-2">
                    <Badge variant="outline" className="text-xs">{member.position}</Badge>
                  </div>
                  <div className="space-y-1 text-sm text-muted-foreground mb-4">
                    <p><strong>Experience:</strong> {member.experience}</p>
                    <p><strong>Specialty:</strong> {member.specialty}</p>
                    {member.bio && <p className="text-xs">{member.bio.substring(0, 100)}...</p>}
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      onClick={() => startEditing('team_members', member)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => handleDelete('team_members', member.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="dining" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Services</h2>
          </div>

          {/* Dining Experiences */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Dining Experiences</CardTitle>
                <Button onClick={() => startEditing('dining_experiences')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Experience
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {diningExperiences.map((experience) => (
                  <div key={experience.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{experience.title}</div>
                        <div className="text-xs text-slate-500">Icon: {experience.icon}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('dining_experiences', experience)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('dining_experiences', experience.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700 mb-2">{experience.description}</div>
                    <div className="text-xs text-slate-600">{experience.items?.length || 0} items</div>
                    <Badge variant={experience.is_active ? "default" : "secondary"} className="mt-2">
                      {experience.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Menu PDFs */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Menu PDFs</CardTitle>
                <Button onClick={() => startEditing('menus')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Menu
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {menus.map((menu) => (
                  <div key={menu.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{menu.title}</div>
                        <div className="text-xs text-slate-500">{menu.description}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('menus', menu)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('menus', menu.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700 mb-2">
                      <a href={menu.pdf_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        View PDF
                      </a>
                    </div>
                    <Badge variant={menu.is_active ? "default" : "secondary"} className="mt-2">
                      {menu.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Accommodation Types */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Accommodation Types</CardTitle>
                <Button onClick={() => startEditing('accommodation_types')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Accommodation
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {accommodationTypes.map((accommodation) => (
                  <div key={accommodation.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{accommodation.title}</div>
                        <div className="text-xs text-slate-500">Icon: {accommodation.icon}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('accommodation_types', accommodation)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('accommodation_types', accommodation.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700 mb-2">{accommodation.description}</div>
                    <div className="text-xs text-slate-600 mb-2">{accommodation.features?.length || 0} features</div>
                    <div className="text-xs font-medium text-emerald-900">{accommodation.price}</div>
                    <Badge variant={accommodation.is_active ? "default" : "secondary"} className="mt-2">
                      {accommodation.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Event Packages */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Event Packages</CardTitle>
                <Button onClick={() => startEditing('event_packages')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Event Package
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {eventPackages.map((eventPkg) => (
                  <div key={eventPkg.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{eventPkg.title}</div>
                        <div className="text-xs text-slate-500">{eventPkg.capacity}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('event_packages', eventPkg)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('event_packages', eventPkg.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700 mb-2">{eventPkg.description}</div>
                    <div className="text-xs text-slate-600 mb-2">{eventPkg.features?.length || 0} features</div>
                    <div className="text-xs font-medium text-emerald-900">{eventPkg.price}</div>
                    <Badge variant={eventPkg.is_active ? "default" : "secondary"} className="mt-2">
                      {eventPkg.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Amenities */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Premium Amenities</CardTitle>
                <Button onClick={() => startEditing('amenities')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Amenity
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {amenities.map((amenity) => (
                  <div key={amenity.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="text-sm text-slate-700">{amenity.title}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('amenities', amenity)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('amenities', amenity.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <Badge variant={amenity.is_active ? "default" : "secondary"} className="mt-2">
                      {amenity.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="celebrations" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Events & Venues</h2>
          </div>

          {/* Celebration Events */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Celebrate With Us Events</CardTitle>
                <Button onClick={() => startEditing('celebration_events')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Event
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {celebrationEvents.map((event) => (
                  <div key={event.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{event.title}</div>
                        <div className="text-xs text-slate-500">{event.category}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('celebration_events', event)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('celebration_events', event.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700 mb-2">{event.description.substring(0, 80)}...</div>
                    {event.image_url && (
                      <div className="mb-2">
                        <img src={event.image_url} alt={event.title} className="w-full h-20 object-cover rounded" />
                      </div>
                    )}
                    <Badge variant={event.is_active ? "default" : "secondary"} className="mt-2">
                      {event.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Event Venue Packages */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Event Packages</CardTitle>
                <Button onClick={() => startEditing('event_venue_packages')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Package
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {eventVenuePackages.map((pkg) => (
                  <div key={pkg.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{pkg.title}</div>
                        <div className="text-xs text-slate-500">Icon: {pkg.icon}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('event_venue_packages', pkg)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('event_venue_packages', pkg.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700 mb-2">{pkg.description.substring(0, 60)}...</div>
                    <div className="text-xs font-medium text-emerald-900">{pkg.price}</div>
                    <Badge variant={pkg.is_active ? "default" : "secondary"} className="mt-2">
                      {pkg.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Gallery Images */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Gallery Images</CardTitle>
                <Button onClick={() => startEditing('gallery_images')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Image
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {galleryImages.map((image) => (
                  <div key={image.id} className="relative group">
                    <img
                      src={image.image_url}
                      alt={image.title}
                      className="w-full aspect-[4/3] object-cover rounded-lg ring-1 ring-slate-200"
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition rounded-lg flex items-center justify-center gap-1">
                      <Button size="sm" variant="ghost" className="text-white hover:bg-white/20" onClick={() => startEditing('gallery_images', image)}>
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="ghost" className="text-white hover:bg-white/20" onClick={() => handleDelete('gallery_images', image.id)}>
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="mt-1 text-xs text-slate-600 truncate">{image.title}</div>
                    <Badge variant={image.is_active ? "default" : "secondary"} className="mt-1">
                      {image.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Contact Messages</h2>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-slate-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-slate-700">Name</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-slate-700">Contact</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-slate-700">Type</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-slate-700">Message</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-slate-700">Date</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-slate-700">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-200">
                    {contactMessages.map((message) => (
                      <tr key={message.id} className="hover:bg-slate-50">
                        <td className="px-4 py-3">
                          <div className="font-medium text-sm">{message.full_name}</div>
                          {message.preferred_date && (
                            <div className="text-xs text-slate-500">Preferred: {new Date(message.preferred_date).toLocaleDateString()}</div>
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-sm">{message.phone}</div>
                          {message.email && (
                            <div className="text-xs text-slate-500">{message.email}</div>
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <Badge variant="outline" className="text-xs">{message.inquiry_type}</Badge>
                        </td>
                        <td className="px-4 py-3 max-w-xs">
                          <div className="text-sm text-slate-700 truncate">{message.message}</div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-xs text-slate-500">
                            {new Date(message.created_at).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <Badge variant={message.status === 'new' ? 'default' : 'secondary'}>
                            {message.status}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {contactMessages.length === 0 && (
                <div className="p-8 text-center text-slate-500">
                  No contact messages yet.
                </div>
              )}
            </CardContent>
          </Card>

          {/* FAQs */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Frequently Asked Questions</CardTitle>
                <Button onClick={() => startEditing('faqs')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add FAQ
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {faqs.map((faq) => (
                  <div key={faq.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="font-medium text-sm mb-1">{faq.question}</div>
                        <div className="text-xs text-slate-600">{faq.answer.substring(0, 100)}...</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('faqs', faq)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('faqs', faq.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <Badge variant={faq.is_active ? "default" : "secondary"} className="mt-2">
                      {faq.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="flags" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Feature Flags</h2>
            <Button onClick={() => startEditing('feature_flags')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Feature Flag
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-6">
            {['properties', 'bookings', 'events', 'pages', 'general'].map(category => {
              const categoryFlags = featureFlags.filter(f => f.category === category);
              if (categoryFlags.length === 0) return null;
              
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="capitalize">{category} Features</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {categoryFlags.map((flag) => (
                        <div key={flag.id} className="flex items-center justify-between p-4 rounded-lg ring-1 ring-slate-200">
                          <div className="flex-1">
                            <div className="flex items-center gap-3">
                              <Switch
                                checked={flag.is_enabled}
                                onCheckedChange={(checked) => {
                                  handleSave('feature_flags', {
                                    ...flag,
                                    is_enabled: checked
                                  });
                                }}
                              />
                              <div>
                                <div className="font-medium text-sm">{flag.flag_name}</div>
                                <div className="text-xs text-slate-500">{flag.flag_key}</div>
                                {flag.description && (
                                  <div className="text-xs text-slate-600 mt-1">{flag.description}</div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <Button size="sm" variant="ghost" onClick={() => startEditing('feature_flags', flag)}>
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="ghost" onClick={() => handleDelete('feature_flags', flag.id)}>
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Settings</h2>
          </div>

          {/* Services Section */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>What We Offer Services</CardTitle>
                <Button onClick={() => startEditing('services')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Service
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {services.map((service) => (
                  <div key={service.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{service.title}</div>
                        <div className="text-xs text-slate-500">Icon: {service.icon}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('services', service)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('services', service.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700">{service.description.substring(0, 100)}...</div>
                    <Badge variant={service.is_active ? "default" : "secondary"} className="mt-2">
                      {service.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Operating Hours */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Operating Hours</CardTitle>
                <Button onClick={() => startEditing('operating_hours')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Operating Hours
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {operatingHours.map((hours) => (
                  <div key={hours.id} className="p-4 rounded-lg ring-1 ring-slate-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium text-sm">{hours.service_name}</div>
                        <div className="text-xs text-slate-500">Icon: {hours.icon}</div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={() => startEditing('operating_hours', hours)}>
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleDelete('operating_hours', hours.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-slate-700 mb-2">
                      {Object.keys(hours.schedule || {}).length} schedule items
                    </div>
                    <Badge variant={hours.is_active ? "default" : "secondary"} className="mt-2">
                      {hours.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Company Settings */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold">Company Settings</h3>
            <Button onClick={() => startEditing('company_settings')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Setting
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-6">
            {['contact', 'social', 'company', 'seo'].map(category => {
              const categorySettings = companySettings.filter(s => s.category === category);
              if (categorySettings.length === 0) return null;
              
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="capitalize">{category} Settings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {categorySettings.map((setting) => (
                        <div key={setting.id} className="p-3 rounded-lg ring-1 ring-slate-200">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <div className="font-medium text-sm">{setting.setting_key.replace(/_/g, ' ')}</div>
                              <div className="text-xs text-slate-500">{setting.description}</div>
                            </div>
                            <div className="flex gap-1">
                              <Button size="sm" variant="ghost" onClick={() => startEditing('company_settings', setting)}>
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="ghost" onClick={() => handleDelete('company_settings', setting.id)}>
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="text-sm text-slate-700 truncate">{setting.setting_value}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>

      {/* Edit Modal */}
      {editingItem && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  {editingItem.id ? 'Edit' : 'Add'} {editingType.slice(0, -1)}
                </CardTitle>
                <Button 
                  size="sm" 
                  variant="ghost"
                  onClick={() => {
                    setEditingItem(null);
                    setEditingType('');
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Swipe Cards & Events Showcase */}
              {(editingType === 'swipe_cards' || editingType === 'events_showcase') && (
                <>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="image_url">Image URL</Label>
                    <ImageUploadField
                      value={editingItem.image_url || ''}
                      onChange={(value) => setEditingItem({ ...editingItem, image_url: value })}
                      onFileUpload={handleImageUpload}
                      uploading={uploading}
                    />
                  </div>
                  {editingType === 'events_showcase' && (
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Input id="category" value={editingItem.category || ''} onChange={(e) => setEditingItem({ ...editingItem, category: e.target.value })} />
                    </div>
                  )}
                </>
              )}

              {/* Properties */}
              {editingType === 'properties' && (
                <>
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" value={editingItem.name || ''} onChange={(e) => setEditingItem({ ...editingItem, name: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="image_url">Image URL</Label>
                    <ImageUploadField
                      value={editingItem.image_url || ''}
                      onChange={(value) => setEditingItem({ ...editingItem, image_url: value })}
                      onFileUpload={handleImageUpload}
                      uploading={uploading}
                    />
                  </div>
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input id="location" value={editingItem.location || ''} onChange={(e) => setEditingItem({ ...editingItem, location: e.target.value })} />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="property_type">Type</Label>
                      <Input id="property_type" value={editingItem.property_type || ''} onChange={(e) => setEditingItem({ ...editingItem, property_type: e.target.value })} />
                    </div>
                    <div>
                      <Label htmlFor="capacity">Capacity</Label>
                      <Input id="capacity" value={editingItem.capacity || ''} onChange={(e) => setEditingItem({ ...editingItem, capacity: e.target.value })} />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="size">Size</Label>
                      <Input id="size" value={editingItem.size || ''} onChange={(e) => setEditingItem({ ...editingItem, size: e.target.value })} />
                    </div>
                    <div>
                      <Label htmlFor="rating">Rating</Label>
                      <Input id="rating" type="number" step="0.1" min="1" max="5" value={editingItem.rating || ''} onChange={(e) => setEditingItem({ ...editingItem, rating: parseFloat(e.target.value) })} />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="price_per_night">Price/Night</Label>
                      <Input id="price_per_night" type="number" value={editingItem.price_per_night || ''} onChange={(e) => setEditingItem({ ...editingItem, price_per_night: parseFloat(e.target.value) })} />
                    </div>
                    <div>
                      <Label htmlFor="original_price">Original Price</Label>
                      <Input id="original_price" type="number" value={editingItem.original_price || ''} onChange={(e) => setEditingItem({ ...editingItem, original_price: parseFloat(e.target.value) })} />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="discount_percentage">Discount %</Label>
                    <Input id="discount_percentage" type="number" value={editingItem.discount_percentage || ''} onChange={(e) => setEditingItem({ ...editingItem, discount_percentage: parseInt(e.target.value) })} />
                  </div>
                  <div>
                    <Label htmlFor="amenities">Amenities (comma-separated)</Label>
                    <Input id="amenities" value={editingItem.amenities ? editingItem.amenities.join(', ') : ''} onChange={(e) => setEditingItem({ ...editingItem, amenities: e.target.value.split(',').map(item => item.trim()) })} />
                  </div>
                  <div>
                    <Label htmlFor="features">Features (comma-separated)</Label>
                    <Input id="features" value={editingItem.features ? editingItem.features.join(', ') : ''} onChange={(e) => setEditingItem({ ...editingItem, features: e.target.value.split(',').map(item => item.trim()) })} />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="available" checked={editingItem.available !== false} onCheckedChange={(checked) => setEditingItem({ ...editingItem, available: checked })} />
                    <Label htmlFor="available">Available</Label>
                  </div>
                </>
              )}

              {/* Team Members */}
              {editingType === 'team_members' && (
                <>
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" value={editingItem.name || ''} onChange={(e) => setEditingItem({ ...editingItem, name: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="position">Position</Label>
                    <Input id="position" value={editingItem.position || ''} onChange={(e) => setEditingItem({ ...editingItem, position: e.target.value })} />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="experience">Experience</Label>
                      <Input id="experience" value={editingItem.experience || ''} onChange={(e) => setEditingItem({ ...editingItem, experience: e.target.value })} />
                    </div>
                    <div>
                      <Label htmlFor="specialty">Specialty</Label>
                      <Input id="specialty" value={editingItem.specialty || ''} onChange={(e) => setEditingItem({ ...editingItem, specialty: e.target.value })} />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea id="bio" value={editingItem.bio || ''} onChange={(e) => setEditingItem({ ...editingItem, bio: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="image_url">Image URL</Label>
                    <ImageUploadField
                      value={editingItem.image_url || ''}
                      onChange={(value) => setEditingItem({ ...editingItem, image_url: value })}
                      onFileUpload={handleImageUpload}
                      uploading={uploading}
                    />
                  </div>
                </>
              )}

              {/* Services, Dining, Event Venue Packages */}
              {(editingType === 'services' || editingType === 'dining_experiences' || editingType === 'event_venue_packages') && (
                <>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  {editingType === 'event_venue_packages' ? (
                    <IconDropdown
                      value={editingItem.icon || ''}
                      onChange={(value) => setEditingItem({ ...editingItem, icon: value })}
                      label="Icon"
                    />
                  ) : (
                    <div>
                      <Label htmlFor="icon">Icon</Label>
                      <Input id="icon" value={editingItem.icon || ''} onChange={(e) => setEditingItem({ ...editingItem, icon: e.target.value })} />
                    </div>
                  )}
                  {editingType === 'dining_experiences' && (
                    <div>
                      <Label htmlFor="items">Items (comma-separated)</Label>
                      <Textarea id="items" value={editingItem.items ? editingItem.items.join(', ') : ''} onChange={(e) => setEditingItem({ ...editingItem, items: e.target.value.split(',').map(item => item.trim()) })} />
                    </div>
                  )}
                  {editingType === 'event_venue_packages' && (
                    <div>
                      <Label htmlFor="price">Price</Label>
                      <Input id="price" value={editingItem.price || ''} onChange={(e) => setEditingItem({ ...editingItem, price: e.target.value })} />
                    </div>
                  )}
                </>
              )}

              {/* Other content types */}
              {editingType === 'menus' && (
                <>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="pdf_url">PDF URL</Label>
                    <Input id="pdf_url" value={editingItem.pdf_url || ''} onChange={(e) => setEditingItem({ ...editingItem, pdf_url: e.target.value })} />
                  </div>
                </>
              )}

              {editingType === 'accommodation_types' && (
                <>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <IconDropdown
                        value={editingItem.icon || ''}
                        onChange={(value) => setEditingItem({ ...editingItem, icon: value })}
                        label="Icon"
                      />
                    </div>
                    <div>
                      <Label htmlFor="price">Price</Label>
                      <Input id="price" value={editingItem.price || ''} onChange={(e) => setEditingItem({ ...editingItem, price: e.target.value })} />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="features">Features (comma-separated)</Label>
                    <Textarea id="features" value={editingItem.features ? editingItem.features.join(', ') : ''} onChange={(e) => setEditingItem({ ...editingItem, features: e.target.value.split(',').map(item => item.trim()) })} />
                  </div>
                </>
              )}

              {editingType === 'event_packages' && (
                <>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="capacity">Capacity</Label>
                      <Input id="capacity" value={editingItem.capacity || ''} onChange={(e) => setEditingItem({ ...editingItem, capacity: e.target.value })} />
                    </div>
                    <div>
                      <Label htmlFor="price">Price</Label>
                      <Input id="price" value={editingItem.price || ''} onChange={(e) => setEditingItem({ ...editingItem, price: e.target.value })} />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="features">Features (comma-separated)</Label>
                    <Textarea id="features" value={editingItem.features ? editingItem.features.join(', ') : ''} onChange={(e) => setEditingItem({ ...editingItem, features: e.target.value.split(',').map(item => item.trim()) })} />
                  </div>
                </>
              )}

              {editingType === 'amenities' && (
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                </div>
              )}

              {editingType === 'operating_hours' && (
                <>
                  <div>
                    <Label htmlFor="service_name">Service Name</Label>
                    <Input id="service_name" value={editingItem.service_name || ''} onChange={(e) => setEditingItem({ ...editingItem, service_name: e.target.value })} />
                  </div>
                  <div>
                    <IconDropdown
                      value={editingItem.icon || ''}
                      onChange={(value) => setEditingItem({ ...editingItem, icon: value })}
                      label="Icon"
                    />
                  </div>
                  <div>
                    <Label>Schedule (JSON)</Label>
                    <Textarea value={JSON.stringify(editingItem.schedule || {}, null, 2)} onChange={(e) => { try { setEditingItem({ ...editingItem, schedule: JSON.parse(e.target.value) }); } catch {} }} />
                  </div>
                </>
              )}

              {editingType === 'celebration_events' && (
                <>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="image_url">Image URL</Label>
                    <ImageUploadField
                      value={editingItem.image_url || ''}
                      onChange={(value) => setEditingItem({ ...editingItem, image_url: value })}
                      onFileUpload={handleImageUpload}
                      uploading={uploading}
                    />
                  </div>
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Input id="category" placeholder="e.g. Wedding, Birthday, Corporate (comma-separated for multiple)" value={editingItem.category || ''} onChange={(e) => setEditingItem({ ...editingItem, category: e.target.value })} />
                  </div>
                </>
              )}

              {editingType === 'gallery_images' && (
                <>
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input id="title" value={editingItem.title || ''} onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="image_url">Image URL</Label>
                    <ImageUploadField
                      value={editingItem.image_url || ''}
                      onChange={(value) => setEditingItem({ ...editingItem, image_url: value })}
                      onFileUpload={handleImageUpload}
                      uploading={uploading}
                    />
                  </div>
                </>
              )}

              {editingType === 'faqs' && (
                <>
                  <div>
                    <Label htmlFor="question">Question</Label>
                    <Input id="question" value={editingItem.question || ''} onChange={(e) => setEditingItem({ ...editingItem, question: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="answer">Answer</Label>
                    <Textarea id="answer" value={editingItem.answer || ''} onChange={(e) => setEditingItem({ ...editingItem, answer: e.target.value })} />
                  </div>
                </>
              )}

              {editingType === 'feature_flags' && (
                <>
                  <div>
                    <Label htmlFor="flag_key">Flag Key</Label>
                    <Input id="flag_key" value={editingItem.flag_key || ''} onChange={(e) => setEditingItem({ ...editingItem, flag_key: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="flag_name">Flag Name</Label>
                    <Input id="flag_name" value={editingItem.flag_name || ''} onChange={(e) => setEditingItem({ ...editingItem, flag_name: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Input id="category" value={editingItem.category || ''} onChange={(e) => setEditingItem({ ...editingItem, category: e.target.value })} />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="is_enabled" checked={editingItem.is_enabled || false} onCheckedChange={(checked) => setEditingItem({ ...editingItem, is_enabled: checked })} />
                    <Label htmlFor="is_enabled">Enabled</Label>
                  </div>
                </>
              )}

              {editingType === 'company_settings' && (
                <>
                  <div>
                    <Label htmlFor="setting_key">Setting Key</Label>
                    <Input id="setting_key" value={editingItem.setting_key || ''} onChange={(e) => setEditingItem({ ...editingItem, setting_key: e.target.value })} />
                  </div>
                  <div>
                    <Label htmlFor="setting_value">Setting Value</Label>
                    <Input id="setting_value" value={editingItem.setting_value || ''} onChange={(e) => setEditingItem({ ...editingItem, setting_value: e.target.value })} />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="setting_type">Type</Label>
                      <Input id="setting_type" value={editingItem.setting_type || ''} onChange={(e) => setEditingItem({ ...editingItem, setting_type: e.target.value })} />
                    </div>
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Input id="category" value={editingItem.category || ''} onChange={(e) => setEditingItem({ ...editingItem, category: e.target.value })} />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" value={editingItem.description || ''} onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })} />
                  </div>
                </>
              )}

              {/* Display Order */}
              {(editingType !== 'feature_flags' && editingType !== 'company_settings' && editingType !== 'contact_messages') && (
                <div>
                  <Label htmlFor="display_order">Display Order</Label>
                  <Input id="display_order" type="number" value={editingItem.display_order || 0} onChange={(e) => setEditingItem({ ...editingItem, display_order: parseInt(e.target.value) })} />
                </div>
              )}

              {/* Active Switch */}
              {editingType !== 'feature_flags' && editingType !== 'contact_messages' && (
                <div className="flex items-center space-x-2">
                  <Switch id="is_active" checked={editingItem.is_active !== false} onCheckedChange={(checked) => setEditingItem({ ...editingItem, is_active: checked })} />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button 
                  onClick={() => handleSave(editingType, editingItem)}
                  className="flex-1"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => {
                    setEditingItem(null);
                    setEditingType('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ContentManager;