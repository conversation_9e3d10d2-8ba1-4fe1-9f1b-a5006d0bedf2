import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { 
  ClipboardList, 
  Clock, 
  Bed,
  CheckCircle,
  AlertTriangle,
  Users,
  Calendar,
  MapPin,
  Timer,
  Activity,
  Bell,
  TrendingUp,
  Play,
  Pause
} from 'lucide-react';

// Define proper Badge variant types
type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline';

export const HousekeepingDashboard: React.FC = () => {
  const todayStats = {
    totalRooms: 28,
    completed: 15,
    pending: 8,
    maintenance: 3,
    inspection: 2
  };

  const myTasks = [
    { 
      id: 'task1',
      room: '101', 
      type: 'checkout-cleaning', 
      priority: 'high', 
      estimatedTime: 45,
      status: 'in-progress',
      startTime: '09:30'
    },
    { 
      id: 'task2',
      room: '205', 
      type: 'maintenance-cleaning', 
      priority: 'medium', 
      estimatedTime: 30,
      status: 'pending',
      startTime: null
    },
    { 
      id: 'task3',
      room: '301', 
      type: 'inspection', 
      priority: 'low', 
      estimatedTime: 15,
      status: 'pending',
      startTime: null
    },
    { 
      id: 'task4',
      room: '108', 
      type: 'deep-cleaning', 
      priority: 'high', 
      estimatedTime: 90,
      status: 'pending',
      startTime: null
    }
  ];

  const floorStatus = [
    { id: 'ground', floor: 'Ground Floor', total: 8, clean: 6, dirty: 1, maintenance: 1 },
    { id: 'first', floor: '1st Floor', total: 10, clean: 7, dirty: 2, maintenance: 1 },
    { id: 'second', floor: '2nd Floor', total: 10, clean: 8, dirty: 2, maintenance: 0 }
  ];

  const getTaskColor = (type: string) => {
    switch (type) {
      case 'checkout-cleaning': return 'bg-red-100 text-red-800';
      case 'maintenance-cleaning': return 'bg-yellow-100 text-yellow-800';  
      case 'inspection': return 'bg-blue-100 text-blue-800';
      case 'deep-cleaning': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string): BadgeVariant => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'secondary';
    }
  };

  // Sanitize task type for display to prevent XSS
  const sanitizeTaskType = (type: string): string => {
    return type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <motion.div 
        className="relative overflow-hidden rounded-2xl bg-gradient-hero p-8 text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="absolute inset-0 bg-black/20" />
        <div className="absolute top-4 right-4">
          <Button variant="secondary" size="sm" className="bg-white/20 hover:bg-white/30 text-white border-white/30">
            <Bell className="h-4 w-4 mr-2" />
            Tasks
            <Badge variant="destructive" className="ml-2">8</Badge>
          </Button>
        </div>
        <div className="relative z-10">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <ClipboardList className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-4xl font-bold mb-2">Housekeeping Dashboard</h1>
              <p className="text-primary-foreground/80 text-lg">Your daily cleaning and maintenance tasks</p>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4 text-gold" />
                  <span className="text-sm">65% Complete</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Timer className="h-4 w-4 text-gold" />
                  <span className="text-sm">3h 45m Remaining</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Bed className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">{todayStats.totalRooms}</p>
                <p className="text-xs text-muted-foreground">Total Rooms</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{todayStats.completed}</p>
                <p className="text-xs text-muted-foreground">Completed</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-2xl font-bold">{todayStats.pending}</p>
                <p className="text-xs text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-2xl font-bold">{todayStats.maintenance}</p>
                <p className="text-xs text-muted-foreground">Maintenance</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <ClipboardList className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{todayStats.inspection}</p>
                <p className="text-xs text-muted-foreground">Inspection</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* My Tasks */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ClipboardList className="h-5 w-5" />
                <span>My Tasks Today</span>
              </CardTitle>
              <CardDescription>Your assigned cleaning tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {myTasks.map((task, index) => (
                  <motion.div 
                    key={task.id} 
                    className="p-6 border border-primary/10 rounded-xl bg-gradient-to-r from-white to-primary/5 hover:shadow-card transition-all duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    whileHover={{ scale: 1.01 }}
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center space-x-4">
                        <span className="text-xl font-bold text-foreground">Room {task.room}</span>
                        <Badge 
                          variant={getPriorityColor(task.priority)}
                          className="text-xs font-medium"
                        >
                          {task.priority.toUpperCase()} PRIORITY
                        </Badge>
                      </div>
                      <Badge 
                        variant={task.status === 'in-progress' ? 'default' : 'secondary'}
                        className={task.status === 'in-progress' ? 'bg-primary text-primary-foreground' : ''}
                      >
                        {task.status === 'in-progress' ? (
                          <><Play className="h-3 w-3 mr-1" />In Progress</>
                        ) : (
                          <><Pause className="h-3 w-3 mr-1" />Pending</>
                        )}
                      </Badge>
                    </div>
                    
                    <div className={`inline-block px-3 py-1 rounded-lg text-sm mb-4 font-medium ${getTaskColor(task.type)}`}>
                      {sanitizeTaskType(task.type)}
                    </div>

                    <div className="grid grid-cols-2 gap-6 text-sm mb-4">
                      <div className="flex items-center space-x-2">
                        <Timer className="h-4 w-4 text-primary" />
                        <span className="font-medium">Est. {task.estimatedTime} mins</span>
                      </div>
                      {task.startTime && (
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-primary" />
                          <span className="font-medium">Started: {task.startTime}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex space-x-3">
                      {task.status === 'pending' ? (
                        <Button size="sm" className="bg-success hover:bg-success/90">
                          <Play className="h-3 w-3 mr-1" />
                          Start Task
                        </Button>
                      ) : (
                        <Button size="sm" className="bg-primary hover:bg-primary-light">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Complete Task
                        </Button>
                      )}
                      <Button size="sm" variant="outline" className="border-warning/30 hover:bg-warning/5 text-warning">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Report Issue
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Floor Status */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Floor Status</CardTitle>
              <CardDescription>Room cleaning status by floor</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {floorStatus.map((floor, index) => (
                  <motion.div 
                    key={floor.id} 
                    className="space-y-3 p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <div className="flex justify-between text-sm">
                      <span className="font-bold text-foreground">{floor.floor}</span>
                      <span className="text-muted-foreground font-medium">{floor.total} rooms</span>
                    </div>
                    <div className="flex space-x-1 h-3 rounded-full overflow-hidden bg-muted">
                      <motion.div 
                        className="bg-success rounded-l" 
                        style={{ width: `${(floor.clean / floor.total) * 100}%` }}
                        initial={{ width: 0 }}
                        animate={{ width: `${(floor.clean / floor.total) * 100}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                      <motion.div 
                        className="bg-destructive" 
                        style={{ width: `${(floor.dirty / floor.total) * 100}%` }}
                        initial={{ width: 0 }}
                        animate={{ width: `${(floor.dirty / floor.total) * 100}%` }}
                        transition={{ duration: 1, delay: index * 0.2 + 0.2 }}
                      />
                      <motion.div 
                        className="bg-warning rounded-r" 
                        style={{ width: `${(floor.maintenance / floor.total) * 100}%` }}
                        initial={{ width: 0 }}
                        animate={{ width: `${(floor.maintenance / floor.total) * 100}%` }}
                        transition={{ duration: 1, delay: index * 0.2 + 0.4 }}
                      />
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-success rounded-full" />
                        <span className="font-medium">Clean: {floor.clean}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-destructive rounded-full" />
                        <span className="font-medium">Dirty: {floor.dirty}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-warning rounded-full" />
                        <span className="font-medium">Maint: {floor.maintenance}</span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start">
                <MapPin className="h-4 w-4 mr-2" />
                Room Status Board
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <ClipboardList className="h-4 w-4 mr-2" />
                Inventory Check
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Report Maintenance
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Users className="h-4 w-4 mr-2" />
                Request Assistance
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HousekeepingDashboard;