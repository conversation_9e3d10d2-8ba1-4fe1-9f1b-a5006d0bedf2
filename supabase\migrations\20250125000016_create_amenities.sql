-- Create amenities table
CREATE TABLE IF NOT EXISTS public.amenities (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.amenities ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to amenities" ON public.amenities
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage amenities" ON public.amenities
  FOR ALL USING (auth.role() = 'authenticated');

-- <PERSON>reate updated_at trigger
CREATE TRIGGER handle_amenities_updated_at
  BEFORE UPDATE ON public.amenities
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample amenities
INSERT INTO public.amenities (title, display_order, is_active) VALUES
('Elegant dining spaces with contemporary design', 1, true),
('Climate-controlled environment throughout', 2, true),
('High-speed WiFi in all areas', 3, true),
('Secure valet parking services', 4, true),
('Wheelchair accessible facilities', 5, true),
('Children''s play area with supervision', 6, true),
('Live cooking stations with chef interaction', 7, true),
('Professional event planning team', 8, true),
('State-of-the-art AV equipment', 9, true),
('Customizable menus for dietary preferences', 10, true),
('24/7 room service and concierge', 11, true),
('Spa and wellness facilities nearby', 12, true);