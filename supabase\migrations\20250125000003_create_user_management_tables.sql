-- Create roles table
CREATE TABLE public.roles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name CHAR<PERSON><PERSON><PERSON> VARYING NOT NULL UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  is_deleted <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by U<PERSON><PERSON>,
  updated_by UUID
);

-- Create permissions table
CREATE TABLE public.permissions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name CHARACTER VARYING NOT NULL UNIQUE,
  module CHARACTER VARYING NOT NULL,
  can_view BO<PERSON><PERSON>N DEFAULT false,
  can_add BOOLEAN DEFAULT false,
  can_edit BOOLEAN DEFAULT false,
  can_delete BOOLEAN DEFAULT false,
  description TEXT,
  is_active BO<PERSON>EAN DEFAULT true,
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID,
  updated_by UUID
);

-- Create role_permissions junction table
CREATE TABLE public.role_permissions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  role_id UUID NOT NULL REFERENCES public.roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES public.permissions(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT true,
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID,
  updated_by UUID,
  UNIQUE(role_id, permission_id)
);

-- Create users table
CREATE TABLE public.users (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email CHARACTER VARYING NOT NULL UNIQUE,
  first_name CHARACTER VARYING,
  last_name CHARACTER VARYING,
  role_id UUID NOT NULL REFERENCES public.roles(id),
  is_active BOOLEAN DEFAULT true,
  is_deleted BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID,
  updated_by UUID
);

-- Enable RLS
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own data" ON public.users FOR SELECT USING (auth.uid() = auth_user_id);
CREATE POLICY "Admins can manage all users" ON public.users FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.users u 
    JOIN public.roles r ON u.role_id = r.id 
    WHERE u.auth_user_id = auth.uid() AND r.name = 'admin'
  )
);

-- Insert default roles
INSERT INTO public.roles (name, description) VALUES
('admin', 'Administrator with full access'),
('hotel-manager', 'Hotel manager with management access'),
('receptionist', 'Front desk receptionist'),
('housekeeping', 'Housekeeping staff'),
('guest', 'Hotel guest');

-- Insert default permissions
INSERT INTO public.permissions (name, module, can_view, can_add, can_edit, can_delete, description) VALUES
('manage_users', 'users', true, true, true, true, 'Manage users'),
('manage_bookings', 'bookings', true, true, true, true, 'Manage hotel bookings'),
('manage_rooms', 'rooms', true, true, true, true, 'Manage room inventory'),
('view_reports', 'reports', true, false, false, false, 'View hotel reports'),
('manage_housekeeping', 'housekeeping', true, true, true, true, 'Manage housekeeping tasks');

