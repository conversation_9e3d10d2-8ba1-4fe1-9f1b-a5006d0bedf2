import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { ChevronDown, ChevronUp, HelpCircle, ArrowRight } from 'lucide-react';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  display_order: number;
  is_active: boolean;
}

const FAQSection = () => {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  useEffect(() => {
    fetchFAQs();
  }, []);

  const fetchFAQs = async () => {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .select('*')
        .eq('is_active', true)
        .order('display_order')
        .limit(5);

      if (error) throw error;
      setFaqs(data || []);
    } catch (error) {
      console.error('Error fetching FAQs:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  // Check total count separately
  const [totalCount, setTotalCount] = useState(0);
  
  useEffect(() => {
    const checkTotalCount = async () => {
      const { count } = await supabase
        .from('faqs')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);
      setTotalCount(count || 0);
    };
    checkTotalCount();
  }, []);

  if (loading) {
    return (
      <section className="bg-slate-50">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-slate-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="bg-white rounded-2xl p-6 animate-pulse">
                <div className="h-4 bg-slate-200 rounded w-3/4 mb-3"></div>
                <div className="h-3 bg-slate-200 rounded w-full"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (faqs.length === 0) {
    return null;
  }

  return (
    <section className="bg-slate-50">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 ring-1 ring-slate-200 mb-4">
            <HelpCircle className="w-4 h-4 text-emerald-900" />
            <span className="text-emerald-900 text-sm font-medium">Help Center</span>
          </div>
          <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Frequently Asked Questions
          </h2>
          <p className="mt-3 text-slate-600">
            Quick answers to common questions about our services and policies
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq) => (
            <div key={faq.id} className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              <button
                onClick={() => toggleItem(faq.id)}
                className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-slate-50 transition"
              >
                <h3 className="text-lg font-semibold text-emerald-950 pr-4">{faq.question}</h3>
                {openItems.has(faq.id) ? (
                  <ChevronUp className="w-5 h-5 text-slate-500 flex-shrink-0" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-slate-500 flex-shrink-0" />
                )}
              </button>
              {openItems.has(faq.id) && (
                <div className="px-6 pb-5">
                  <div className="pt-2 border-t border-slate-200">
                    <p className="text-slate-700 leading-relaxed">{faq.answer}</p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {totalCount > 5 && (
          <div className="mt-8 text-center">
            <Link
              to="/faq"
              className="inline-flex items-center gap-2 rounded-full px-6 py-3 text-[14px] text-white bg-emerald-900 hover:bg-emerald-800 transition"
            >
              View All FAQs
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default FAQSection;