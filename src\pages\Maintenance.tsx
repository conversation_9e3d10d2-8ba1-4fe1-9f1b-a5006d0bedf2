import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Calendar, Wrench, AlertTriangle, CheckCircle, Clock, Package, Shield, BarChart3, Link } from 'lucide-react';

const Maintenance = () => {
  const [selectedTab, setSelectedTab] = useState('ticketing');

  const tickets = [
    { id: 'T001', title: 'AC not working in Room 201', priority: 'High', category: 'HVAC', status: 'Open', assignee: '<PERSON>', created: '2024-01-15' },
    { id: 'T002', title: 'Leaky faucet in bathroom', priority: 'Medium', category: 'Plumbing', status: 'In Progress', assignee: '<PERSON>', created: '2024-01-14' },
    { id: 'T003', title: 'Elevator making noise', priority: 'High', category: 'Elevator', status: 'Pending', assignee: 'Sarah Wilson', created: '2024-01-13' },
  ];

  const workOrders = [
    { id: 'WO001', ticket: 'T001', technician: '<PERSON>', timeSpent: '2.5 hrs', status: 'In Progress', approval: 'Pending' },
    { id: 'WO002', ticket: 'T002', technician: 'Mike Johnson', timeSpent: '1.0 hrs', status: 'Completed', approval: 'Approved' },
    { id: 'WO003', ticket: 'T003', technician: 'Sarah Wilson', timeSpent: '0.5 hrs', status: 'Started', approval: 'N/A' },
  ];

  const assets = [
    { id: 'A001', name: 'Central AC Unit 1', type: 'HVAC', location: 'Rooftop', warranty: '2025-03-15', vendor: 'CoolTech Inc', status: 'Active' },
    { id: 'A002', name: 'Boiler System', type: 'Heating', location: 'Basement', warranty: '2024-12-20', vendor: 'HeatPro Ltd', status: 'Active' },
    { id: 'A003', name: 'Elevator 1', type: 'Transport', location: 'Main Lobby', warranty: '2026-01-10', vendor: 'LiftTech', status: 'Maintenance' },
  ];

  const spares = [
    { item: 'AC Filters', current: 15, minimum: 10, maximum: 50, cost: '$25', status: 'Good' },
    { item: 'Elevator Cables', current: 5, minimum: 8, maximum: 20, cost: '$150', status: 'Low Stock' },
    { item: 'Plumbing Joints', current: 25, minimum: 15, maximum: 40, cost: '$5', status: 'Good' },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'destructive';
      case 'Medium': return 'secondary';
      case 'Low': return 'outline';
      default: return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'default';
      case 'Completed': return 'default';
      case 'In Progress': return 'secondary';
      case 'Maintenance': return 'destructive';
      case 'Low Stock': return 'destructive';
      case 'Good': return 'default';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Maintenance</h1>
          <p className="text-muted-foreground">Manage maintenance operations and engineering tasks</p>
        </div>
        <Button>
          <Wrench className="mr-2 h-4 w-4" />
          New Work Order
        </Button>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-9">
          <TabsTrigger value="ticketing">Ticketing</TabsTrigger>
          <TabsTrigger value="work-orders">Work Orders</TabsTrigger>
          <TabsTrigger value="assets">Assets</TabsTrigger>
          <TabsTrigger value="pm">Preventive</TabsTrigger>
          <TabsTrigger value="spares">Spares</TabsTrigger>
          <TabsTrigger value="sla">SLA</TabsTrigger>
          <TabsTrigger value="safety">Safety</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="ticketing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Maintenance Tickets
              </CardTitle>
              <CardDescription>Manage guest and staff maintenance requests</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <Input placeholder="Search tickets..." className="max-w-sm" />
                <Button variant="outline">Filter</Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ticket ID</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assignee</TableHead>
                    <TableHead>Created</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tickets.map((ticket) => (
                    <TableRow key={ticket.id}>
                      <TableCell className="font-medium">{ticket.id}</TableCell>
                      <TableCell>{ticket.title}</TableCell>
                      <TableCell>
                        <Badge variant={getPriorityColor(ticket.priority)}>{ticket.priority}</Badge>
                      </TableCell>
                      <TableCell>{ticket.category}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(ticket.status)}>{ticket.status}</Badge>
                      </TableCell>
                      <TableCell>{ticket.assignee}</TableCell>
                      <TableCell>{ticket.created}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="work-orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Work Orders
              </CardTitle>
              <CardDescription>Track technician assignments and time logs</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Work Order</TableHead>
                    <TableHead>Ticket</TableHead>
                    <TableHead>Technician</TableHead>
                    <TableHead>Time Spent</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Approval</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">{order.id}</TableCell>
                      <TableCell>{order.ticket}</TableCell>
                      <TableCell>{order.technician}</TableCell>
                      <TableCell>{order.timeSpent}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(order.status)}>{order.status}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(order.approval)}>{order.approval}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Asset Registry
              </CardTitle>
              <CardDescription>Manage equipment assets and warranties</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Warranty</TableHead>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assets.map((asset) => (
                    <TableRow key={asset.id}>
                      <TableCell className="font-medium">{asset.id}</TableCell>
                      <TableCell>{asset.name}</TableCell>
                      <TableCell>{asset.type}</TableCell>
                      <TableCell>{asset.location}</TableCell>
                      <TableCell>{asset.warranty}</TableCell>
                      <TableCell>{asset.vendor}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(asset.status)}>{asset.status}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pm" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Preventive Maintenance
              </CardTitle>
              <CardDescription>Schedule and track preventive maintenance tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Calendar view of scheduled PM tasks</p>
                <Button className="mt-4">Schedule PM Task</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="spares" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Spares & Consumables
              </CardTitle>
              <CardDescription>Monitor spare parts inventory and stock levels</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Current Stock</TableHead>
                    <TableHead>Minimum</TableHead>
                    <TableHead>Maximum</TableHead>
                    <TableHead>Unit Cost</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {spares.map((spare, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{spare.item}</TableCell>
                      <TableCell>{spare.current}</TableCell>
                      <TableCell>{spare.minimum}</TableCell>
                      <TableCell>{spare.maximum}</TableCell>
                      <TableCell>{spare.cost}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(spare.status)}>{spare.status}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sla" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                SLAs & Escalations
              </CardTitle>
              <CardDescription>Service level agreements and escalation management</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Critical Issues</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-destructive">2 hrs</div>
                    <p className="text-xs text-muted-foreground">Response time</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">High Priority</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-secondary">4 hrs</div>
                    <p className="text-xs text-muted-foreground">Response time</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Normal Issues</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">24 hrs</div>
                    <p className="text-xs text-muted-foreground">Response time</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="safety" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Safety & Compliance
              </CardTitle>
              <CardDescription>Safety protocols and compliance documentation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Safety checklist templates and compliance documents</p>
                <Button className="mt-4">Add Checklist</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Analytics & Reports
              </CardTitle>
              <CardDescription>Maintenance performance metrics and analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">MTTR</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">4.2 hrs</div>
                    <p className="text-xs text-muted-foreground">Mean time to repair</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Downtime</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2.1%</div>
                    <p className="text-xs text-muted-foreground">System downtime</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">SLA Adherence</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">94%</div>
                    <p className="text-xs text-muted-foreground">Within SLA</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Open Tickets</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">7</div>
                    <p className="text-xs text-muted-foreground">Active tickets</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Integrations
              </CardTitle>
              <CardDescription>System integrations and data sync logs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Link className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Integration logs with housekeeping, front office, and vendors</p>
                <Button className="mt-4">View Sync Logs</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Maintenance;