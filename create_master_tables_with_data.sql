-- Run this SQL in Supabase Dashboard SQL Editor

-- Create master tables for property management system

-- Properties Admin Master Table
CREATE TABLE IF NOT EXISTS public.properties_admin (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    
    -- Property Classification
    type VARCHAR(50) NOT NULL CHECK (type IN ('HOTEL', 'RESORT', 'VILLA', 'APARTMENT', 'HOSTEL', 'BED_AND_BREAKFAST')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('BUDGET', 'STANDARD', 'DELUXE', 'LUXURY', 'SUPER_LUXURY')),
    star_rating INTEGER CHECK (star_rating BETWEEN 1 AND 7),
    
    -- Contact Information
    email VARCHAR(255),
    phone VARCHAR(20),
    alternate_phone VARCHAR(20),
    
    -- Address Information
    address_line1 TEXT NOT NULL,
    address_line2 TEXT,
    landmark VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL DEFAULT 'India',
    pincode VARCHAR(20) NOT NULL,
    
    -- Operational Settings
    currency VARCHAR(3) NOT NULL DEFAULT 'INR',
    fiscal_year_start DATE DEFAULT '2024-04-01',
    check_in_time TIME NOT NULL DEFAULT '14:00:00',
    check_out_time TIME NOT NULL DEFAULT '12:00:00',
    late_check_out_until TIME DEFAULT '14:00:00',
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Room Types Master Table
CREATE TABLE IF NOT EXISTS public.room_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties_admin(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- Occupancy Details
    base_occupancy INTEGER NOT NULL CHECK (base_occupancy > 0),
    max_occupancy INTEGER NOT NULL CHECK (max_occupancy >= base_occupancy),
    extra_bed_capacity INTEGER DEFAULT 0 CHECK (extra_bed_capacity >= 0),
    extra_bed_charge DECIMAL(10,2) DEFAULT 0,
    
    -- Physical Attributes
    bed_type VARCHAR(100),
    bed_count INTEGER DEFAULT 1,
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    UNIQUE(property_id, code)
);

-- Amenities Admin Master Table
CREATE TABLE IF NOT EXISTS public.amenities_admin (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID REFERENCES properties_admin(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('ROOM', 'BATHROOM', 'TECHNOLOGY', 'FACILITY', 'SERVICE')),
    sub_category VARCHAR(100),
    description TEXT,
    
    -- Pricing
    is_chargeable BOOLEAN NOT NULL DEFAULT false,
    charge_amount DECIMAL(10,2) DEFAULT 0,
    charge_type VARCHAR(20) CHECK (charge_type IN ('PER_NIGHT', 'ONE_TIME', 'PER_PERSON')),
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(property_id, name)
);

-- Room Type Amenities Junction Table
CREATE TABLE IF NOT EXISTS public.room_type_amenities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_type_id UUID NOT NULL REFERENCES room_types(id) ON DELETE CASCADE,
    amenity_id UUID NOT NULL REFERENCES amenities_admin(id) ON DELETE CASCADE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(room_type_id, amenity_id)
);

-- Rooms Inventory Table
CREATE TABLE IF NOT EXISTS public.rooms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties_admin(id) ON DELETE CASCADE,
    room_type_id UUID NOT NULL REFERENCES room_types(id) ON DELETE CASCADE,
    room_number VARCHAR(20) NOT NULL,
    floor INTEGER CHECK (floor >= 0),
    wing VARCHAR(50),
    view_type VARCHAR(50),
    
    -- Status Management
    status VARCHAR(20) NOT NULL DEFAULT 'AVAILABLE' CHECK (status IN ('AVAILABLE', 'OCCUPIED', 'MAINTENANCE', 'OUT_OF_ORDER', 'CLEANING')),
    key_code VARCHAR(50),
    tax_percentages VARCHAR(50),
    
    -- Status & Audit
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    UNIQUE(property_id, room_number)
);

-- Enable Row Level Security
ALTER TABLE public.properties_admin ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.amenities_admin ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_type_amenities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Authenticated users can manage properties_admin" ON public.properties_admin;
DROP POLICY IF EXISTS "Authenticated users can manage room_types" ON public.room_types;
DROP POLICY IF EXISTS "Authenticated users can manage amenities_admin" ON public.amenities_admin;
DROP POLICY IF EXISTS "Authenticated users can manage room_type_amenities" ON public.room_type_amenities;
DROP POLICY IF EXISTS "Authenticated users can manage rooms" ON public.rooms;

-- Create RLS policies for authenticated users
CREATE POLICY "Authenticated users can manage properties_admin" ON public.properties_admin FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage room_types" ON public.room_types FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage amenities_admin" ON public.amenities_admin FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage room_type_amenities" ON public.room_type_amenities FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated users can manage rooms" ON public.rooms FOR ALL USING (auth.role() = 'authenticated');

-- Insert sample data (only if tables are empty)
INSERT INTO public.properties_admin (name, code, description, type, category, star_rating, email, phone, address_line1, city, state, pincode) 
SELECT 'Meadow De Jalsa Resort', 'MDJ001', 'Luxury resort with world-class amenities', 'RESORT', 'LUXURY', 5, '<EMAIL>', '+91-87400-27008', 'Bardoli–Vyara Highway', 'Bardoli', 'Gujarat', '394601'
WHERE NOT EXISTS (SELECT 1 FROM public.properties_admin WHERE code = 'MDJ001');

INSERT INTO public.properties_admin (name, code, description, type, category, star_rating, email, phone, address_line1, city, state, pincode) 
SELECT 'Red Chili Grand Hotel', 'RCH001', 'Premium business hotel in city center', 'HOTEL', 'DELUXE', 4, '<EMAIL>', '+91-98765-43210', '123 Business District', 'Surat', 'Gujarat', '395007'
WHERE NOT EXISTS (SELECT 1 FROM public.properties_admin WHERE code = 'RCH001');

-- Sample Room Types
INSERT INTO public.room_types (property_id, name, code, description, base_occupancy, max_occupancy, extra_bed_capacity, extra_bed_charge, bed_type, bed_count) 
SELECT (SELECT id FROM properties_admin WHERE code = 'MDJ001'), 'Deluxe Room', 'DLX', 'Spacious room with garden view', 2, 3, 1, 1500.00, 'King', 1
WHERE NOT EXISTS (SELECT 1 FROM public.room_types WHERE code = 'DLX' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001'));

INSERT INTO public.room_types (property_id, name, code, description, base_occupancy, max_occupancy, extra_bed_capacity, extra_bed_charge, bed_type, bed_count) 
SELECT (SELECT id FROM properties_admin WHERE code = 'RCH001'), 'Standard Room', 'STD', 'Comfortable standard accommodation', 2, 2, 0, 0.00, 'Queen', 1
WHERE NOT EXISTS (SELECT 1 FROM public.room_types WHERE code = 'STD' AND property_id = (SELECT id FROM properties_admin WHERE code = 'RCH001'));

-- Sample Amenities
INSERT INTO public.amenities_admin (property_id, name, category, sub_category, description, is_chargeable, charge_amount, charge_type) 
SELECT NULL, 'Wi-Fi', 'TECHNOLOGY', 'Internet', 'High-speed wireless internet', false, 0, NULL
WHERE NOT EXISTS (SELECT 1 FROM public.amenities_admin WHERE name = 'Wi-Fi' AND property_id IS NULL);

INSERT INTO public.amenities_admin (property_id, name, category, sub_category, description, is_chargeable, charge_amount, charge_type) 
SELECT NULL, 'Air Conditioning', 'ROOM', 'Climate Control', 'Individual room AC control', false, 0, NULL
WHERE NOT EXISTS (SELECT 1 FROM public.amenities_admin WHERE name = 'Air Conditioning' AND property_id IS NULL);

-- Sample Rooms
INSERT INTO public.rooms (property_id, room_type_id, room_number, floor, wing, view_type, status, key_code) 
SELECT (SELECT id FROM properties_admin WHERE code = 'MDJ001'), (SELECT id FROM room_types WHERE code = 'DLX' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001')), '101', 1, 'East Wing', 'Garden View', 'AVAILABLE', 'MDJ101'
WHERE NOT EXISTS (SELECT 1 FROM public.rooms WHERE room_number = '101' AND property_id = (SELECT id FROM properties_admin WHERE code = 'MDJ001'));

INSERT INTO public.rooms (property_id, room_type_id, room_number, floor, wing, view_type, status, key_code) 
SELECT (SELECT id FROM properties_admin WHERE code = 'RCH001'), (SELECT id FROM room_types WHERE code = 'STD' AND property_id = (SELECT id FROM properties_admin WHERE code = 'RCH001')), '301', 3, 'North Tower', 'City View', 'AVAILABLE', 'RCH301'
WHERE NOT EXISTS (SELECT 1 FROM public.rooms WHERE room_number = '301' AND property_id = (SELECT id FROM properties_admin WHERE code = 'RCH001'));