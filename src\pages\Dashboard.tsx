import React from "react";
import { DashboardStats } from "@/components/dashboard/DashboardStats";
import { ContentManager } from "@/components/admin/ContentManager";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  BarChart3,
  Settings,
  Users,
  Calendar,
  TrendingUp,
  Activity,
  Bell,
  Plus,
} from "lucide-react";

interface DashboardProps {
  userRole: string;
}

const Dashboard: React.FC<DashboardProps> = ({ userRole }) => {
  const recentActivities = [
    { action: "New booking received", time: "2 minutes ago", type: "booking" },
    {
      action: "Room 205 checked out",
      time: "15 minutes ago",
      type: "checkout",
    },
    { action: "Payment processed", time: "1 hour ago", type: "payment" },
    { action: "Maintenance request", time: "2 hours ago", type: "maintenance" },
  ];

  const quickActions = [
    { label: "New Booking", icon: Plus, color: "primary" },
    { label: "Check-in Guest", icon: Users, color: "success" },
    { label: "View Reports", icon: BarChart3, color: "gold" },
    { label: "Settings", icon: Settings, color: "warning" },
  ];

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Enhanced Header */}
      <motion.div
        className="relative overflow-hidden rounded-xl lg:rounded-2xl bg-gradient-hero p-4 sm:p-6 lg:p-8 text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
      >
        <div className="absolute inset-0 bg-black/20" />
        <div className="absolute -top-10 -right-10 w-32 h-32 sm:w-40 sm:h-40 bg-white/10 rounded-full blur-3xl" />
        <div className="absolute -bottom-10 -left-10 w-24 h-24 sm:w-32 sm:h-32 bg-gold/20 rounded-full blur-2xl" />

        <div className="relative z-10">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6">
            <div className="flex-1">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2 leading-tight">
                Admin Dashboard
              </h1>
              <p className="text-primary-foreground/80 text-sm sm:text-base lg:text-lg">
                Hotel management and website content control
              </p>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4">
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/20 hover:bg-white/30 text-white border-white/30 text-xs sm:text-sm px-3 sm:px-4"
              >
                <Bell className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Notifications</span>
                <span className="sm:hidden">Alerts</span>
                <Badge variant="destructive" className="ml-1 sm:ml-2 text-xs">
                  3
                </Badge>
              </Button>
            </div>
          </div>
        </div>
      </motion.div>

      <Tabs defaultValue="overview" className="space-y-6 lg:space-y-8">
        <TabsList className="grid w-full grid-cols-2 h-10 sm:h-12 bg-white/50 backdrop-blur-sm border border-border/50 rounded-lg">
          <TabsTrigger
            value="overview"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-xs sm:text-sm font-medium transition-all duration-200"
          >
            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Overview</span>
            <span className="sm:hidden">Stats</span>
          </TabsTrigger>
          <TabsTrigger
            value="content"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-xs sm:text-sm font-medium transition-all duration-200"
          >
            <Settings className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Website Content</span>
            <span className="sm:hidden">Content</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 lg:space-y-8">
          <DashboardStats userRole={userRole} />

          {/* Additional Dashboard Content */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}
            >
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant h-fit">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                    <Activity className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                    <span>Quick Actions</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 sm:space-y-3">
                  {quickActions.map((action, index) => (
                    <Button
                      key={action.label}
                      variant="outline"
                      className="w-full justify-start hover:bg-primary/5 focus:bg-primary/10 focus:ring-2 focus:ring-primary/20 transition-colors text-xs sm:text-sm h-9 sm:h-10"
                    >
                      <action.icon className="h-3 w-3 sm:h-4 sm:w-4 mr-2 sm:mr-3" />
                      <span className="truncate">{action.label}</span>
                    </Button>
                  ))}
                </CardContent>
              </Card>
            </motion.div>

            {/* Recent Activity */}
            <motion.div
              className="xl:col-span-2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.2, ease: [0.4, 0, 0.2, 1] }}
            >
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-elegant">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                    <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                    <span>Recent Activity</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 sm:space-y-4">
                    {recentActivities.map((activity, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-lg bg-muted/50 hover:bg-muted focus-within:bg-muted focus-within:ring-2 focus-within:ring-primary/20 transition-colors group cursor-pointer"
                        tabIndex={0}
                        role="button"
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") {
                            e.preventDefault();
                            // Handle activity click
                          }
                        }}
                      >
                        <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                          <div
                            className={`w-2 h-2 rounded-full flex-shrink-0 ${
                              activity.type === "booking"
                                ? "bg-success ring-2 ring-success/20"
                                : activity.type === "checkout"
                                ? "bg-warning ring-2 ring-warning/20"
                                : activity.type === "payment"
                                ? "bg-gold ring-2 ring-gold/20"
                                : "bg-destructive ring-2 ring-destructive/20"
                            }`}
                          />
                          <span className="font-medium text-sm sm:text-base truncate text-foreground">
                            {activity.action}
                          </span>
                        </div>
                        <span className="text-xs sm:text-sm text-muted-foreground flex-shrink-0 ml-2">
                          {activity.time}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        <TabsContent value="content">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <ContentManager />
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
