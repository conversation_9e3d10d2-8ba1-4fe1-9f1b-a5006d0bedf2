import React from 'react';
import { Phone, Mail, MapPin, Menu, Calendar, Sparkles, KeyRound, Clock3, Wifi, Car, Building2, Utensils, PartyPopper, Map, ChevronLeft, ChevronRight, Instagram, Facebook, MessagesSquare, Leaf, Route, Medal, Snowflake, Tv2, CupSoda, Image } from 'lucide-react';

const PreviewDesign: React.FC = () => {
  return (
    <div className="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900" style={{ fontFamily: "'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'", scrollBehavior: 'smooth' }}>
      {/* Sticky Utility Bar */}
      <div className="fixed inset-x-0 top-0 z-50">
        <div className="w-full bg-white/80 backdrop-blur border-b border-slate-200">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-2 flex items-center justify-between">
            <div className="flex items-center gap-5 text-[13px]">
              <a href="tel:+918740027008" className="inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
                <Phone className="w-4 h-4" />
                <span>+91 87400-27008</span>
              </a>
              <a href="mailto:<EMAIL>" className="hidden sm:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </a>
              <a href="#contact" className="hidden md:inline-flex items-center gap-2 text-slate-700 hover:text-emerald-800 transition">
                <MapPin className="w-4 h-4" />
                <span>Bardoli–Vyara Highway</span>
              </a>
            </div>
            <div className="flex items-center gap-3">
              <a href="https://instagram.com" target="_blank" rel="noopener" className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Instagram">
                <Instagram className="w-4 h-4" />
              </a>
              <a href="https://facebook.com" target="_blank" rel="noopener" className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700 hover:text-emerald-800 transition" aria-label="Facebook">
                <Facebook className="w-4 h-4" />
              </a>
            </div>
          </div>
        </div>

        {/* Main Nav */}
        <nav className="w-full bg-white/90 backdrop-blur border-b border-slate-200">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
            <a href="#home" className="flex items-center gap-3">
              <div className="h-9 w-9 rounded-lg bg-emerald-950 text-amber-400 grid place-items-center text-sm tracking-tight font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>MJ</div>
              <div className="flex flex-col leading-tight">
                <span className="text-[15px] sm:text-[16px] text-emerald-950 font-semibold tracking-tight" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Meadow De Jalsa</span>
                <span className="text-[11px] text-slate-500">Luxury Stay & Events</span>
              </div>
            </a>
            <button className="lg:hidden inline-flex items-center justify-center rounded-md p-2 hover:bg-slate-100 text-slate-700 hover:text-emerald-900 transition" aria-label="Toggle navigation">
              <Menu className="w-5 h-5" />
            </button>
            <div className="hidden lg:flex items-center gap-1">
              <a href="#home" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Home</a>
              <a href="#about" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">About</a>
              <a href="#rooms" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Rooms</a>
              <a href="#restaurant" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Restaurant</a>
              <a href="#events" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Events</a>
              <a href="#gallery" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Gallery</a>
              <a href="#contact" className="px-3 py-2 rounded-md text-[14px] text-slate-700 hover:text-emerald-900 hover:bg-slate-100 transition">Contact</a>
              <button className="ml-2 inline-flex items-center gap-2 rounded-full px-4 py-2 text-[14px] text-emerald-950 font-medium tracking-tight shadow-sm ring-1 ring-amber-400/40 hover:ring-amber-500/60 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-900/30">
                <Calendar className="w-4 h-4" />
                Book Now
              </button>
            </div>
          </div>
        </nav>
      </div>

      <main className="pt-[128px]">
        {/* HOME */}
        <section id="home" className="relative">
          {/* Hero */}
          <div className="relative h-[72vh] sm:h-[80vh] w-full overflow-hidden">
            <video className="absolute inset-0 w-full h-full object-cover" src="https://videos.pexels.com/video-files/856130/856130-hd_1920_1080_25fps.mp4" autoPlay loop muted playsInline poster="https://images.unsplash.com/photo-1501117716987-c8e2a3a67a0b?q=80&w=1760&auto=format&fit=crop" aria-label="Aerial view of property" />
            <div className="absolute inset-0 bg-gradient-to-b from-emerald-950/60 via-emerald-950/40 to-emerald-950/60"></div>
            <div className="relative z-10 h-full">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full grid">
                <div className="m-auto text-center">
                  <div className="inline-flex items-center gap-3 px-3 py-1.5 rounded-full bg-white/10 backdrop-blur ring-1 ring-white/20 mb-5">
                    <MapPin className="w-4 h-4 text-amber-300" />
                    <span className="text-white/90 text-[12px] sm:text-[13px]">Bardoli–Vyara Highway</span>
                  </div>
                  <h1 className="text-white tracking-tight text-4xl sm:text-5xl lg:text-6xl font-semibold leading-[1.1]" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
                    Where Luxury Meets Tradition
                  </h1>
                  <p className="mt-4 text-white/85 text-[15px] sm:text-[16px] max-w-2xl mx-auto">
                    Experience elegant stays, gourmet dining, and unforgettable celebrations at Meadow De Jalsa.
                  </p>
                  <div className="mt-8 flex items-center justify-center gap-3">
                    <button className="group inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/50">
                      <Calendar className="w-5 h-5" />
                      Book Your Stay
                    </button>
                    <a href="#events" className="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white/90 ring-1 ring-white/30 hover:bg-white/10 transition">
                      <Sparkles className="w-5 h-5" />
                      Plan Your Event
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Info Strip */}
          <div className="bg-white border-y border-slate-200">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-5 grid grid-cols-2 sm:grid-cols-4 gap-3">
              <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                  <KeyRound className="w-5 h-5" />
                </div>
                <div>
                  <div className="text-[12px] text-slate-500">Check-in</div>
                  <div className="text-sm font-medium">2:00 PM</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                  <Clock3 className="w-5 h-5" />
                </div>
                <div>
                  <div className="text-[12px] text-slate-500">Restaurant</div>
                  <div className="text-sm font-medium">11 AM – 11 PM</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                  <Wifi className="w-5 h-5" />
                </div>
                <div>
                  <div className="text-[12px] text-slate-500">Connectivity</div>
                  <div className="text-sm font-medium">High-speed WiFi</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-200 transition">
                <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                  <Car className="w-5 h-5" />
                </div>
                <div>
                  <div className="text-[12px] text-slate-500">Parking</div>
                  <div className="text-sm font-medium">Ample & Secure</div>
                </div>
              </div>
            </div>
          </div>

          {/* About Snapshot */}
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
                Luxury Stay & Memorable Events on Bardoli–Vyara Highway
              </h2>
              <p className="mt-3 text-slate-600 max-w-2xl mx-auto">
                A modern retreat offering refined rooms, curated dining, and bespoke event experiences.
              </p>
            </div>
          </div>

          {/* Highlights */}
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-14">
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Stay */}
              <a href="#rooms" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1760&auto=format&fit=crop" alt="Rooms & Suites" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
                <div className="absolute inset-x-0 bottom-0 p-4">
                  <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                    <Building2 className="w-4 h-4 text-emerald-900" />
                    <span className="text-[13px] font-medium text-emerald-950">Stay</span>
                  </div>
                  <p className="mt-3 text-white/90 text-sm">Rooms & suites designed for comfort and elegance.</p>
                </div>
              </a>
              {/* Dine */}
              <a href="#restaurant" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=1760&auto=format&fit=crop" alt="Restaurant" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
                <div className="absolute inset-x-0 bottom-0 p-4">
                  <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                    <Utensils className="w-4 h-4 text-emerald-900" />
                    <span className="text-[13px] font-medium text-emerald-950">Dine</span>
                  </div>
                  <p className="mt-3 text-white/90 text-sm">Taste luxury, taste tradition with curated cuisine.</p>
                </div>
              </a>
              {/* Celebrate */}
              <a href="#events" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&w=1760&auto=format&fit=crop" alt="Events & Banquets" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
                <div className="absolute inset-x-0 bottom-0 p-4">
                  <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                    <PartyPopper className="w-4 h-4 text-emerald-900" />
                    <span className="text-[13px] font-medium text-emerald-950">Celebrate</span>
                  </div>
                  <p className="mt-3 text-white/90 text-sm">Weddings, corporate events, parties, and more.</p>
                </div>
              </a>
              {/* Explore */}
              <a href="#about" className="group relative rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80" alt="Nearby Attractions" className="h-48 w-full object-cover group-hover:scale-[1.03] transition duration-500" />
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-950/70 via-emerald-950/10 to-transparent"></div>
                <div className="absolute inset-x-0 bottom-0 p-4">
                  <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-white/90 ring-1 ring-slate-200">
                    <Map className="w-4 h-4 text-emerald-900" />
                    <span className="text-[13px] font-medium text-emerald-950">Explore</span>
                  </div>
                  <p className="mt-3 text-white/90 text-sm">Discover the best of Bardoli & Vyara nearby.</p>
                </div>
              </a>
            </div>
          </div>

          {/* Events Showcase Carousel */}
          <div className="bg-emerald-950">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-white tracking-tight text-2xl sm:text-3xl font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Events Showcase</h3>
                <div className="flex items-center gap-2">
                  <button className="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Previous">
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  <button className="p-2 rounded-md bg-white/10 hover:bg-white/15 text-white ring-1 ring-white/15 transition" aria-label="Next">
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
              <div className="relative">
                <div className="flex gap-4 overflow-hidden scroll-smooth">
                  {/* Slides */}
                  <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                    <img src="https://images.unsplash.com/photo-1522673607200-164d1b6ce486?q=80&w=1760&auto=format&fit=crop" alt="Weddings" className="h-44 w-full object-cover" />
                    <div className="p-4">
                      <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Weddings</div>
                      <p className="mt-3 text-sm text-slate-600">Grand celebrations tailored to your story.</p>
                    </div>
                  </div>
                  <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                    <img src="https://images.unsplash.com/photo-1511578314322-379afb476865?q=80&w=1760&auto=format&fit=crop" alt="Corporate Events" className="h-44 w-full object-cover" />
                    <div className="p-4">
                      <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Corporate</div>
                      <p className="mt-3 text-sm text-slate-600">Professional settings for conferences and meets.</p>
                    </div>
                  </div>
                  <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                    <img src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&w=1760&auto=format&fit=crop" alt="Parties" className="h-44 w-full object-cover" />
                    <div className="p-4">
                      <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Parties</div>
                      <p className="mt-3 text-sm text-slate-600">Birthdays and anniversaries with flair.</p>
                    </div>
                  </div>
                  <div className="min-w-[80%] sm:min-w-[48%] lg:min-w-[32%] bg-white rounded-2xl overflow-hidden ring-1 ring-emerald-900/10">
                    <img src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?q=80&w=1760&auto=format&fit=crop" alt="Exhibitions" className="h-44 w-full object-cover" />
                    <div className="p-4">
                      <div className="inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-emerald-50 text-emerald-900 text-[12px] ring-1 ring-emerald-200">Exhibitions</div>
                      <p className="mt-3 text-sm text-slate-600">Versatile layouts for shows and expos.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social Live Feed */}
          <div className="bg-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Live Feed</h3>
                <div className="flex items-center gap-3">
                  <a href="https://instagram.com" target="_blank" className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <Instagram className="w-4 h-4 text-emerald-900" />
                    <span className="text-[13px]">Instagram</span>
                  </a>
                  <a href="https://facebook.com" target="_blank" className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <Facebook className="w-4 h-4 text-emerald-900" />
                    <span className="text-[13px]">Facebook</span>
                  </a>
                </div>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
                {/* 6 feed items */}
                <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&q=80" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                  <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
                </a>
                <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&w=1200&auto=format&fit=crop" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                  <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
                </a>
                <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&q=80" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                  <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
                </a>
                <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?q=80&w=1200&auto=format&fit=crop" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                  <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
                </a>
                <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&q=80" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                  <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
                </a>
                <a href="#" className="group relative rounded-xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&w=1200&auto=format&fit=crop" className="h-32 w-full object-cover group-hover:scale-[1.03] transition" alt="Feed" />
                  <div className="absolute inset-0 bg-emerald-950/0 group-hover:bg-emerald-950/20 transition"></div>
                </a>
              </div>
            </div>
          </div>

          {/* CTA */}
          <div className="bg-[url('https://images.unsplash.com/photo-1518277850984-94e89c892b26?q=80&w=1760&auto=format&fit=crop')] bg-cover bg-center bg-fixed">
            <div className="bg-emerald-950/80">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
                <h3 className="text-white tracking-tight text-3xl sm:text-4xl font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Ready to make memories?</h3>
                <p className="mt-3 text-white/85">Reserve your stay or plan a bespoke event with our expert team.</p>
                <div className="mt-7 flex items-center justify-center gap-3">
                  <button className="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500">
                    <Calendar className="w-5 h-5" />
                    Book Now
                  </button>
                  <a href="#contact" className="inline-flex items-center gap-2 rounded-full px-5 py-3 text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                    <MessagesSquare className="w-5 h-5" />
                    Talk to Us
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* ABOUT */}
        <section id="about" className="bg-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
            <div className="grid lg:grid-cols-2 gap-10 items-center">
              <div>
                <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold mb-3" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Meadow De Jalsa — where luxury meets tradition.</h2>
                <p className="text-slate-600">
                  From a humble vision to a captivating destination, our story is woven with heartfelt hospitality, contemporary design, and timeless values.
                </p>
                {/* Timeline */}
                <div className="mt-8 space-y-6">
                  <div className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div className="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                      <div className="flex-1 w-px bg-slate-200"></div>
                    </div>
                    <div>
                      <div className="text-sm text-slate-500">Est. 2016</div>
                      <div className="text-[18px] font-medium text-emerald-950">Foundations</div>
                      <p className="text-sm text-slate-600">The idea of a luxury stopover on the Bardoli–Vyara highway comes alive.</p>
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div className="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                      <div className="flex-1 w-px bg-slate-200"></div>
                    </div>
                    <div>
                      <div className="text-sm text-slate-500">2018–2021</div>
                      <div className="text-[18px] font-medium text-emerald-950">Growth</div>
                      <p className="text-sm text-slate-600">Rooms, banquet spaces, and a signature restaurant take shape.</p>
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div className="h-5 w-5 rounded-full bg-emerald-900 ring-4 ring-emerald-200"></div>
                      <div className="flex-1 w-px bg-slate-200"></div>
                    </div>
                    <div>
                      <div className="text-sm text-slate-500">Today</div>
                      <div className="text-[18px] font-medium text-emerald-950">Destination</div>
                      <p className="text-sm text-slate-600">A preferred venue for luxury stays and memorable events.</p>
                    </div>
                  </div>
                </div>
                {/* Highlights */}
                <div className="mt-8 grid sm:grid-cols-3 gap-3">
                  <div className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <div className="flex items-center gap-2 text-emerald-950">
                      <Leaf className="w-4 h-4" />
                      <span className="text-sm font-medium">Eco-friendly</span>
                    </div>
                    <p className="text-[13px] text-slate-600 mt-1.5">Energy-efficient lighting, water-saving fixtures, greenery.</p>
                  </div>
                  <div className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <div className="flex items-center gap-2 text-emerald-950">
                      <Route className="w-4 h-4" />
                      <span className="text-sm font-medium">Highway Location</span>
                    </div>
                    <p className="text-[13px] text-slate-600 mt-1.5">Seamless access and ample secure parking.</p>
                  </div>
                  <div className="p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                    <div className="flex items-center gap-2 text-emerald-950">
                      <Medal className="w-4 h-4" />
                      <span className="text-sm font-medium">Premium Hospitality</span>
                    </div>
                    <p className="text-[13px] text-slate-600 mt-1.5">Attentive service and bespoke experiences.</p>
                  </div>
                </div>
              </div>
              {/* Gallery Masonry */}
              <div className="columns-2 gap-3">
                <img src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?q=80&w=1600&auto=format&fit=crop" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Lobby" />
                <img src="https://images.unsplash.com/photo-1642615835477-d303d7dc9ee9?w=1080&q=80" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Banquet" />
                <img src="https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?q=80&w=1600&auto=format&fit=crop" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Dining" />
                <img src="https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1600&auto=format&fit=crop" className="mb-3 w-full rounded-xl ring-1 ring-slate-200" alt="Room" />
              </div>
            </div>

            {/* Mission & Vision */}
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                  <h4 className="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Our Mission</h4>
                  <p className="text-slate-600 text-[15px]">To craft refined stays and memorable events through attentive service, culinary excellence, and thoughtful design.</p>
                </div>
                <div className="p-6 rounded-2xl ring-1 ring-slate-200 hover:ring-emerald-300 transition bg-white">
                  <h4 className="text-xl tracking-tight text-emerald-950 font-semibold mb-2" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Our Vision</h4>
                  <p className="text-slate-600 text-[15px]">To be the region's most loved destination for celebrations and serene getaways.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* ROOMS & SUITES */}
        <section id="rooms" className="bg-slate-50">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
            <div className="flex items-end justify-between gap-4">
              <div>
                <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>Rooms & Suites</h2>
                <p className="text-slate-600 mt-1">Choose your perfect stay with immersive visuals and amenities.</p>
              </div>
              <div className="flex items-center gap-2">
                <button className="px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">All</button>
                <button className="px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Standard</button>
                <button className="px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Deluxe</button>
                <button className="px-3 py-1.5 rounded-full text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">Suite</button>
              </div>
            </div>

            <div className="mt-8 grid md:grid-cols-2 lg:grid-cols-3 gap-5">
              {/* Room Card */}
              <div className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                {/* Slider */}
                <div className="relative">
                  <div className="overflow-hidden">
                    <div className="flex transition-transform duration-500">
                      <img src="https://images.unsplash.com/photo-1562790351-d273a961e0e9?q=80&w=1600&auto=format&fit=crop" className="h-48 w-full object-cover flex-shrink-0" alt="Standard Room 1" />
                    </div>
                  </div>
                  <button className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition" aria-label="Previous image">
                    <ChevronLeft className="w-4 h-4" />
                  </button>
                  <button className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-md bg-white/80 hover:bg-white/90 text-slate-700 ring-1 ring-slate-200 shadow-sm transition" aria-label="Next image">
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
                {/* Content */}
                <div className="p-4">
                  <div className="flex items-start justify-between gap-3">
                    <div>
                      <h3 className="text-[18px] font-semibold text-emerald-950">Standard Room</h3>
                      <p className="text-[13px] text-slate-500">Cozy comfort, perfect for solo travelers or couples.</p>
                    </div>
                    <div className="text-right">
                      <div className="text-[11px] text-slate-500">From</div>
                      <div className="text-[15px] font-semibold text-emerald-900">₹2,999</div>
                    </div>
                  </div>
                  <div className="mt-3 flex flex-wrap items-center gap-2 text-[12px] text-slate-600">
                    <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><Wifi className="w-3.5 h-3.5" /> WiFi</span>
                    <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><Snowflake className="w-3.5 h-3.5" /> AC</span>
                    <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><Tv2 className="w-3.5 h-3.5" /> Smart TV</span>
                    <span className="inline-flex items-center gap-1.5 px-2 py-1 rounded-full bg-slate-50 ring-1 ring-slate-200"><CupSoda className="w-3.5 h-3.5" /> Minibar</span>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <button className="inline-flex items-center gap-2 text-[14px] text-emerald-900 hover:text-emerald-700 transition">
                      <Calendar className="w-4 h-4" />
                      Book
                    </button>
                    <a href="#gallery" className="inline-flex items-center gap-2 text-[14px] text-slate-700 hover:text-emerald-900 transition">
                      <Image className="w-4 h-4" />
                      Gallery
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default PreviewDesign;