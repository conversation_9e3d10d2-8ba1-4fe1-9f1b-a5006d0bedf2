import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  UserCog, 
  Search, 
  Filter,
  Plus,
  Eye,
  Edit,
  Phone,
  Mail,
  Calendar,
  Clock,
  Star,
  MapPin,
  Briefcase
} from 'lucide-react';

interface StaffMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  position: string;
  department: 'front-desk' | 'housekeeping' | 'maintenance' | 'food-service' | 'management' | 'security';
  status: 'active' | 'on-leave' | 'terminated';
  hireDate: string;
  shift: 'morning' | 'afternoon' | 'night' | 'flexible';
  hourlyRate: number;
  address: string;
  emergencyContact: string;
  emergencyPhone: string;
  performance: number; // 1-5 rating
  currentShift?: {
    start: string;
    end: string;
    status: 'scheduled' | 'clocked-in' | 'on-break' | 'clocked-out';
  };
}

const mockStaff: StaffMember[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Housekeeping Supervisor',
    department: 'housekeeping',
    status: 'active',
    hireDate: '2022-03-15',
    shift: 'morning',
    hourlyRate: 18.50,
    address: '123 Oak St, Downtown',
    emergencyContact: 'Carlos Garcia',
    emergencyPhone: '+****************',
    performance: 4.8,
    currentShift: {
      start: '07:00',
      end: '15:00',
      status: 'clocked-in'
    }
  },
  {
    id: '2',
    name: 'John Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Maintenance Technician',
    department: 'maintenance',
    status: 'active',
    hireDate: '2021-08-22',
    shift: 'afternoon',
    hourlyRate: 22.00,
    address: '456 Pine Ave, Suburbs',
    emergencyContact: 'Anna Rodriguez',
    emergencyPhone: '+****************',
    performance: 4.6,
    currentShift: {
      start: '15:00',
      end: '23:00',
      status: 'on-break'
    }
  },
  {
    id: '3',
    name: 'Lisa Chen',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Front Desk Manager',
    department: 'front-desk',
    status: 'active',
    hireDate: '2020-01-10',
    shift: 'flexible',
    hourlyRate: 25.00,
    address: '789 Elm St, City Center',
    emergencyContact: 'David Chen',
    emergencyPhone: '+****************',
    performance: 4.9
  },
  {
    id: '4',
    name: 'Sofia Martinez',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Room Attendant',
    department: 'housekeeping',
    status: 'on-leave',
    hireDate: '2023-06-01',
    shift: 'morning',
    hourlyRate: 16.00,
    address: '321 Maple Dr, Northside',
    emergencyContact: 'Luis Martinez',
    emergencyPhone: '+****************',
    performance: 4.2
  }
];

const departmentColors = {
  'front-desk': 'bg-primary text-primary-foreground',
  'housekeeping': 'bg-blue-100 text-blue-800',
  'maintenance': 'bg-orange-100 text-orange-800',
  'food-service': 'bg-green-100 text-green-800',
  'management': 'bg-purple-100 text-purple-800',
  'security': 'bg-red-100 text-red-800'
};

const statusColors = {
  'active': 'bg-success text-success-foreground',
  'on-leave': 'bg-warning text-warning-foreground',
  'terminated': 'bg-destructive text-destructive-foreground'
};

const shiftColors = {
  'morning': 'bg-yellow-100 text-yellow-800',
  'afternoon': 'bg-blue-100 text-blue-800',
  'night': 'bg-purple-100 text-purple-800',
  'flexible': 'bg-green-100 text-green-800'
};

const currentShiftColors = {
  'scheduled': 'bg-muted text-muted-foreground',
  'clocked-in': 'bg-success text-success-foreground',
  'on-break': 'bg-warning text-warning-foreground',
  'clocked-out': 'bg-muted text-muted-foreground'
};

const StaffCard: React.FC<{ staff: StaffMember }> = ({ staff }) => {
  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{staff.name}</CardTitle>
            <p className="text-muted-foreground">{staff.position}</p>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <Badge className={statusColors[staff.status]}>
              {staff.status.replace('-', ' ')}
            </Badge>
            <Badge className={departmentColors[staff.department]}>
              {staff.department.replace('-', ' ')}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Contact Info */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{staff.email}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{staff.phone}</span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{staff.address}</span>
            </div>
          </div>

          {/* Performance & Rate */}
          <div className="grid grid-cols-2 gap-4 bg-accent p-3 rounded-lg">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1">
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                <span className="text-lg font-semibold">{staff.performance}</span>
              </div>
              <div className="text-xs text-muted-foreground">Performance</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">${staff.hourlyRate}</div>
              <div className="text-xs text-muted-foreground">Hourly Rate</div>
            </div>
          </div>

          {/* Shift Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Shift:</span>
            </div>
            <Badge className={shiftColors[staff.shift]}>
              {staff.shift}
            </Badge>
          </div>

          {/* Current Shift Status */}
          {staff.currentShift && (
            <div className="bg-primary/10 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Today's Shift:</span>
                <Badge className={currentShiftColors[staff.currentShift.status]}>
                  {staff.currentShift.status.replace('-', ' ')}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                {staff.currentShift.start} - {staff.currentShift.end}
              </div>
            </div>
          )}

          {/* Hire Date */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Hired:</span>
            </div>
            <span className="text-sm">{new Date(staff.hireDate).toLocaleDateString()}</span>
          </div>

          {/* Emergency Contact */}
          <div className="bg-muted p-3 rounded-lg">
            <div className="text-xs text-muted-foreground mb-1">Emergency Contact:</div>
            <div className="text-sm font-medium">{staff.emergencyContact}</div>
            <div className="text-sm">{staff.emergencyPhone}</div>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
            <Button size="sm" variant="outline" className="flex-1">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button size="sm" className="flex-1 btn-primary">
              <Briefcase className="h-4 w-4 mr-1" />
              Schedule
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Staff: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredStaff = mockStaff.filter(staff => {
    const matchesSearch = 
      staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.position.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = departmentFilter === 'all' || staff.department === departmentFilter;
    const matchesStatus = statusFilter === 'all' || staff.status === statusFilter;
    
    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const staffStats = {
    total: mockStaff.length,
    active: mockStaff.filter(s => s.status === 'active').length,
    onLeave: mockStaff.filter(s => s.status === 'on-leave').length,
    frontDesk: mockStaff.filter(s => s.department === 'front-desk').length,
    housekeeping: mockStaff.filter(s => s.department === 'housekeeping').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Staff Management</h1>
          <p className="text-muted-foreground">Manage your hotel staff and schedules</p>
        </div>
        <Button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Add Staff Member
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{staffStats.total}</div>
            <div className="text-sm text-muted-foreground">Total Staff</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-success">{staffStats.active}</div>
            <div className="text-sm text-muted-foreground">Active</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-warning">{staffStats.onLeave}</div>
            <div className="text-sm text-muted-foreground">On Leave</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{staffStats.frontDesk}</div>
            <div className="text-sm text-muted-foreground">Front Desk</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{staffStats.housekeeping}</div>
            <div className="text-sm text-muted-foreground">Housekeeping</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search staff by name, email, or position..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                <SelectItem value="front-desk">Front Desk</SelectItem>
                <SelectItem value="housekeeping">Housekeeping</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="food-service">Food Service</SelectItem>
                <SelectItem value="management">Management</SelectItem>
                <SelectItem value="security">Security</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <UserCog className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="on-leave">On Leave</SelectItem>
                <SelectItem value="terminated">Terminated</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Staff Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {filteredStaff.map((staff) => (
          <StaffCard key={staff.id} staff={staff} />
        ))}
      </div>

      {filteredStaff.length === 0 && (
        <div className="text-center py-12">
          <UserCog className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No staff members found</h3>
          <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Staff;