import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Users, 
  Search, 
  Filter,
  Plus,
  Eye,
  Edit,
  Phone,
  Mail,
  MapPin,
  Star,
  Calendar,
  CreditCard
} from 'lucide-react';

interface Guest {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  country: string;
  vipStatus: 'regular' | 'silver' | 'gold' | 'platinum';
  totalStays: number;
  totalSpent: number;
  lastStay: string;
  currentBooking?: string;
  loyaltyPoints: number;
  preferences: string[];
}

const mockGuests: Guest[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main St, New York',
    country: 'USA',
    vipStatus: 'gold',
    totalStays: 12,
    totalSpent: 4500,
    lastStay: '2024-01-15',
    currentBooking: 'Room 205',
    loyaltyPoints: 2450,
    preferences: ['Non-smoking', 'High floor', 'Late checkout']
  },
  {
    id: '2',
    name: 'Emma Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Oak Ave, Los Angeles',
    country: 'USA',
    vipStatus: 'platinum',
    totalStays: 28,
    totalSpent: 12300,
    lastStay: '2024-01-14',
    loyaltyPoints: 5680,
    preferences: ['Ocean view', 'Quiet room', 'Early check-in']
  },
  {
    id: '3',
    name: 'Michael Brown',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Pine St, Chicago',
    country: 'USA',
    vipStatus: 'silver',
    totalStays: 5,
    totalSpent: 1200,
    lastStay: '2024-01-12',
    loyaltyPoints: 890,
    preferences: ['Business center access', 'Gym access']
  },
  {
    id: '4',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Elm St, Miami',
    country: 'USA',
    vipStatus: 'regular',
    totalStays: 2,
    totalSpent: 480,
    lastStay: '2023-12-20',
    loyaltyPoints: 340,
    preferences: ['Pool access']
  }
];

const vipColors = {
  regular: 'bg-muted text-muted-foreground',
  silver: 'bg-gray-100 text-gray-800 border border-gray-300',
  gold: 'bg-yellow-100 text-yellow-800 border border-yellow-300',
  platinum: 'bg-purple-100 text-purple-800 border border-purple-300'
};

const vipLabels = {
  regular: 'Regular',
  silver: 'Silver',
  gold: 'Gold',
  platinum: 'Platinum'
};

const GuestCard: React.FC<{ guest: Guest }> = ({ guest }) => {
  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{guest.name}</CardTitle>
            <p className="text-muted-foreground">{guest.country}</p>
          </div>
          <Badge className={vipColors[guest.vipStatus]}>
            <Star className="h-3 w-3 mr-1" />
            {vipLabels[guest.vipStatus]}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Contact Info */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{guest.email}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{guest.phone}</span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{guest.address}</span>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-4 bg-accent p-3 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-semibold">{guest.totalStays}</div>
              <div className="text-xs text-muted-foreground">Total Stays</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">${guest.totalSpent}</div>
              <div className="text-xs text-muted-foreground">Total Spent</div>
            </div>
          </div>

          {/* Current Booking */}
          {guest.currentBooking && (
            <div className="bg-primary/10 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Stay:</span>
                <span className="text-sm text-primary font-semibold">{guest.currentBooking}</span>
              </div>
            </div>
          )}

          {/* Loyalty Points */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Loyalty Points:</span>
            <span className="text-sm font-semibold text-gold">{guest.loyaltyPoints} pts</span>
          </div>

          {/* Last Stay */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Last Stay:</span>
            <span className="text-sm">{new Date(guest.lastStay).toLocaleDateString()}</span>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            <Button size="sm" variant="outline" className="flex-1">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
            <Button size="sm" variant="outline" className="flex-1">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button size="sm" className="flex-1 btn-primary">
              <Calendar className="h-4 w-4 mr-1" />
              Book
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Guests: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [vipFilter, setVipFilter] = useState<string>('all');

  const filteredGuests = mockGuests.filter(guest => {
    const matchesSearch = 
      guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.phone.includes(searchTerm);
    
    const matchesVip = vipFilter === 'all' || guest.vipStatus === vipFilter;
    
    return matchesSearch && matchesVip;
  });

  const guestStats = {
    total: mockGuests.length,
    regular: mockGuests.filter(g => g.vipStatus === 'regular').length,
    silver: mockGuests.filter(g => g.vipStatus === 'silver').length,
    gold: mockGuests.filter(g => g.vipStatus === 'gold').length,
    platinum: mockGuests.filter(g => g.vipStatus === 'platinum').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Guest Management</h1>
          <p className="text-muted-foreground">Manage your hotel guests and their preferences</p>
        </div>
        <Button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Add Guest
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{guestStats.total}</div>
            <div className="text-sm text-muted-foreground">Total Guests</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-muted-foreground">{guestStats.regular}</div>
            <div className="text-sm text-muted-foreground">Regular</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-600">{guestStats.silver}</div>
            <div className="text-sm text-muted-foreground">Silver</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{guestStats.gold}</div>
            <div className="text-sm text-muted-foreground">Gold</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{guestStats.platinum}</div>
            <div className="text-sm text-muted-foreground">Platinum</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search guests by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={vipFilter} onValueChange={setVipFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by VIP status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Guests</SelectItem>
                <SelectItem value="regular">Regular</SelectItem>
                <SelectItem value="silver">Silver</SelectItem>
                <SelectItem value="gold">Gold</SelectItem>
                <SelectItem value="platinum">Platinum</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Guests Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredGuests.map((guest) => (
          <GuestCard key={guest.id} guest={guest} />
        ))}
      </div>

      {filteredGuests.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No guests found</h3>
          <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Guests;