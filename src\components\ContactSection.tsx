import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Phone, Mail, MapPin, Clock, Facebook, Instagram, Twitter, Youtube, Linkedin } from 'lucide-react';
import ContactForm from './ContactForm';

interface CompanySetting {
  setting_key: string;
  setting_value: string;
  setting_type: string;
  category: string;
}

export const ContactSection: React.FC = () => {
  const [settings, setSettings] = useState<CompanySetting[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('company_settings')
        .select('setting_key, setting_value, setting_type, category')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setSettings(data || []);
    } catch (error) {
      console.error('Error fetching settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSetting = (key: string, defaultValue: string = '') => {
    const setting = settings.find(s => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  const socialIcons = {
    facebook: Facebook,
    instagram: Instagram,
    twitter: Twitter,
    youtube: Youtube,
    linkedin: Linkedin
  };

  if (loading) {
    return (
      <section className="bg-emerald-950">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="grid lg:grid-cols-2 gap-8">
              <div className="bg-white/10 rounded-2xl h-96"></div>
              <div className="bg-white/5 rounded-2xl h-96"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="bg-emerald-950">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Contact Card */}
          <div className="rounded-2xl bg-white p-6 ring-1 ring-emerald-900/10">
            <h2 className="text-2xl font-semibold text-emerald-950" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
              Get in Touch
            </h2>
            <p className="mt-1 text-slate-600 text-[15px]">
              {getSetting('contact_form_subtitle', "We'd love to help you plan your stay or event. Reach out to us and our team will get back shortly.")}
            </p>

            <div className="mt-4 grid sm:grid-cols-2 gap-3">
              {getSetting('phone_primary') && (
                <a href={`tel:${getSetting('phone_primary')}`} className="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <Phone className="w-5 h-5" />
                  </div>
                  <div className="text-sm">
                    <div className="text-slate-500">Phone</div>
                    <div className="font-medium text-emerald-950">{getSetting('phone_primary')}</div>
                  </div>
                </a>
              )}
              
              {getSetting('email_primary') && (
                <a href={`mailto:${getSetting('email_primary')}`} className="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200 hover:ring-emerald-300 transition">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <Mail className="w-5 h-5" />
                  </div>
                  <div className="text-sm">
                    <div className="text-slate-500">Email</div>
                    <div className="font-medium text-emerald-950">{getSetting('email_primary')}</div>
                  </div>
                </a>
              )}
              
              {getSetting('address_full') && (
                <div className="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <MapPin className="w-5 h-5" />
                  </div>
                  <div className="text-sm">
                    <div className="text-slate-500">Address</div>
                    <div className="font-medium text-emerald-950">{getSetting('address_full')}</div>
                  </div>
                </div>
              )}
              
              {getSetting('business_hours') && (
                <div className="inline-flex items-center gap-3 p-4 rounded-xl ring-1 ring-slate-200">
                  <div className="h-9 w-9 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center">
                    <Clock className="w-5 h-5" />
                  </div>
                  <div className="text-sm">
                    <div className="text-slate-500">Hours</div>
                    <div className="font-medium text-emerald-950">{getSetting('business_hours')}</div>
                  </div>
                </div>
              )}
            </div>

            {/* Social Links */}
            <div className="mt-4 flex gap-3">
              {Object.entries(socialIcons).map(([platform, Icon]) => {
                const url = getSetting(`social_${platform}`);
                if (!url) return null;
                
                return (
                  <a
                    key={platform}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="h-10 w-10 rounded-lg bg-emerald-50 text-emerald-900 grid place-items-center hover:bg-emerald-100 transition"
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                );
              })}
            </div>

            {/* Map */}
            {getSetting('map_embed_url') && (
              <div className="mt-4">
                <iframe
                  title={`${getSetting('company_name', 'Company')} Location`}
                  className="w-full h-56 rounded-xl ring-1 ring-slate-200"
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  src={getSetting('map_embed_url')}
                />
              </div>
            )}
          </div>

          {/* Form */}
          <ContactForm variant="dark" />
        </div>
      </div>
    </section>
  );
};

export default ContactSection;