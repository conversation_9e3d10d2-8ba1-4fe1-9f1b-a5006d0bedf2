import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import CelebrateWithUsSection from '@/components/website/CelebrateWithUsSection';
import EventPackagesSection from '@/components/website/EventPackagesSection';
import GallerySection from '@/components/website/GallerySection';
import { 
  PartyPopper, 
  Users, 
  Calendar, 
  Phone, 
  ArrowRight,
  Building2,
  TreePine,
  Presentation,
  CalendarClock,
  Camera,
  Check,
  Crown,
  Sparkles,
  Music,
  Utensils,
  MapPin
} from 'lucide-react';

const Events = () => {

  return (
    <div className="bg-white text-slate-800 antialiased selection:bg-amber-200/60 selection:text-slate-900 min-h-screen" style={{ fontFamily: "'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji'", scrollBehavior: 'smooth' }}>
      {/* Hero Section */}
      <section className="relative">
        <div className="relative h-[60vh] sm:h-[70vh] w-full overflow-hidden">
          <motion.img
            initial={{ scale: 1.1, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 w-full h-full object-cover"
            src="https://images.unsplash.com/photo-1492684223066-81342ee5ff30?q=80&w=1760&auto=format&fit=crop"
            alt="Event Venues"
          />
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute inset-0 bg-gradient-to-b from-emerald-950/70 via-emerald-950/50 to-emerald-950/70"
          ></motion.div>
          <div className="relative z-10 h-full">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4 sm:space-y-6">
                  <motion.div 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-white/10 backdrop-blur-sm ring-1 ring-white/20"
                  >
                    <PartyPopper className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-amber-300" />
                    <span className="text-white/90 text-xs sm:text-sm font-medium">Event Venues</span>
                  </motion.div>
                  <motion.h1 
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 1, delay: 1, ease: "easeOut" }}
                    className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold leading-[1.1] tracking-tight text-white px-4 sm:px-0" 
                    style={{ fontFamily: "'Cormorant Garamond', serif" }}
                  >
                    Perfect <span className="text-amber-300">Event Venues</span>
                  </motion.h1>
                  <motion.p 
                    initial={{ y: 30, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 1.3, ease: "easeOut" }}
                    className="text-sm sm:text-base lg:text-lg xl:text-xl text-white/90 leading-relaxed max-w-3xl mx-auto px-4 sm:px-0"
                  >
                    Create unforgettable memories at our stunning venues - from intimate gatherings to grand celebrations
                  </motion.p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Celebrate With Us Section */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <CelebrateWithUsSection />
      </motion.div>

      {/* Gallery Section */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <GallerySection />
      </motion.div>

      {/* Event Packages Section */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <EventPackagesSection />
      </motion.div>

      {/* CTA Section */}
      <section className="bg-emerald-950">
        <motion.div 
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16 text-center"
        >
          <motion.h2 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-white font-semibold" 
            style={{ fontFamily: "'Cormorant Garamond', serif" }}
          >
            Ready to Plan Your Event?
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-3 text-sm sm:text-base text-white/85 max-w-2xl mx-auto px-4 sm:px-0"
          >
            Let our expert team help you create an unforgettable celebration. Contact us for personalized packages and venue tours.
          </motion.p>
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-6 sm:mt-8 flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4"
          >
            <motion.a 
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              href="tel:+918740027008" 
              className="w-full sm:w-auto inline-flex items-center justify-center gap-2 rounded-full px-5 sm:px-6 py-3 text-sm sm:text-[15px] text-emerald-950 font-medium tracking-tight shadow-[0_10px_30px_-10px_rgba(245,158,11,0.45)] ring-1 ring-amber-400/50 hover:ring-amber-500/70 transition bg-gradient-to-br from-amber-300 to-yellow-400 hover:from-amber-400 hover:to-yellow-500"
            >
              <Phone className="w-4 sm:w-5 h-4 sm:h-5" />
              Call: +91 87400-27008
            </motion.a>
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link to="/contact" className="w-full sm:w-auto inline-flex items-center justify-center gap-2 rounded-full px-5 sm:px-6 py-3 text-sm sm:text-[15px] text-white ring-1 ring-white/30 hover:bg-white/10 transition">
                <MapPin className="w-4 sm:w-5 h-4 sm:h-5" />
                Visit Our Venue
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>
    </div>
  );
};

export default Events;