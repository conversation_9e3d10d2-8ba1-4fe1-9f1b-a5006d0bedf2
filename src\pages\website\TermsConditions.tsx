import React, { useState, useEffect } from 'react';
import { WebsiteLayout } from '@/components/website/WebsiteLayout';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import { FileText, Users, CreditCard, Calendar, Shield, Globe, Scale, Phone, Mail, MapPin } from 'lucide-react';

interface CompanySetting {
  setting_key: string;
  setting_value: string;
}

const TermsConditions: React.FC = () => {
  const [settings, setSettings] = useState<CompanySetting[]>([]);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data } = await supabase
        .from('company_settings')
        .select('setting_key, setting_value')
        .eq('is_active', true)
        .eq('is_deleted', false);

      setSettings(data || []);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const getSetting = (key: string, defaultValue: string = '') => {
    const setting = settings.find(s => s.setting_key === key);
    return setting?.setting_value || defaultValue;
  };

  return (
    <WebsiteLayout>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-emerald-700 pt-24 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 bg-[url('/hero1.png')] bg-cover bg-center opacity-10"></div>
        
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="relative container mx-auto px-4 text-center"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full mb-6"
          >
            <FileText className="w-10 h-10 text-white" />
          </motion.div>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Terms & Conditions
          </h1>
          <p className="text-xl text-emerald-100 max-w-2xl mx-auto">
            Please read our terms carefully before using our services
          </p>
        </motion.div>
      </section>

      {/* Main Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-2xl p-8 mb-12"
          >
            <p className="text-lg text-emerald-900 leading-relaxed text-center">
              Welcome to <span className="font-semibold">{getSetting('company_name', '[Your Hotel Name]')}</span>'s website. By accessing or using our website, services, and online booking system, you agree to comply with the following Terms & Conditions. Please read them carefully before using our services.
            </p>
          </motion.div>

          <div className="space-y-12">
            {[
              {
                icon: Globe,
                title: "Scope of Services",
                content: [
                  "This website provides hotel information, marketing content, promotions, and an online booking platform for reservations.",
                  `All bookings are subject to availability and confirmation by ${getSetting('company_name', '[Your Hotel Name]')}.`
                ]
              },
              {
                icon: Users,
                title: "User Responsibilities",
                content: [
                  "Provide accurate and up-to-date information when making reservations.",
                  "Use the website only for lawful purposes and not for fraudulent activities.",
                  "Not attempt to disrupt or misuse the website, servers, or networks."
                ]
              },
              {
                icon: CreditCard,
                title: "Booking & Payments",
                content: [
                  "Reservations must be made through our official website, phone, or authorized partners.",
                  "Full or partial payment may be required to confirm your booking.",
                  "Payments are processed securely through trusted payment gateways.",
                  "You are responsible for ensuring sufficient funds/credit in your payment method."
                ]
              },
              {
                icon: Calendar,
                title: "Cancellation & Refund Policy",
                content: [
                  "Cancellations must be made within the timeline mentioned at the time of booking.",
                  "Refunds (if applicable) will be processed according to the hotel's cancellation policy.",
                  "No-shows and late cancellations may result in full or partial charges.",
                  "Special offers, promotional bookings, and peak-season reservations may have non-refundable terms."
                ]
              },
              {
                icon: Shield,
                title: "Check-In & Stay Conditions",
                content: [
                  "Guests must present valid government-issued identification at check-in.",
                  "The hotel reserves the right to refuse service to any guest not complying with policies.",
                  "Guests are responsible for any damage caused to hotel property during their stay.",
                  "Illegal activities or misconduct will lead to immediate cancellation of stay without refund."
                ]
              },
              {
                icon: FileText,
                title: "Intellectual Property",
                content: [
                  `All website content, including text, images, logos, and designs, is the property of ${getSetting('company_name', '[Your Hotel Name]')} and protected under applicable copyright and trademark laws.`,
                  "You may not copy, distribute, modify, or use our content without prior written consent."
                ]
              },
              {
                icon: Shield,
                title: "Limitation of Liability",
                content: [
                  `While we strive to ensure accurate information, ${getSetting('company_name', '[Your Hotel Name]')} is not responsible for errors, omissions, or interruptions in website services.`,
                  "We are not liable for any direct, indirect, or incidental damages resulting from the use of our website or services."
                ]
              },
              {
                icon: Globe,
                title: "Third-Party Services",
                content: [
                  "Our website may include links to third-party platforms (e.g., booking engines, travel partners).",
                  "We are not responsible for the accuracy, services, or privacy practices of third parties."
                ]
              },
              {
                icon: FileText,
                title: "Privacy Policy",
                content: [
                  "Your use of this website is also governed by our Privacy Policy.",
                  "Please review it to understand how we collect, use, and protect your information."
                ]
              },
              {
                icon: Scale,
                title: "Governing Law & Dispute Resolution",
                content: [
                  "These Terms & Conditions are governed by the laws of India.",
                  "Any disputes shall be subject to the exclusive jurisdiction of the courts in Gujarat, India."
                ]
              },
              {
                icon: FileText,
                title: "Changes to Terms",
                content: [
                  "We may update or modify these Terms & Conditions at any time.",
                  "Continued use of our website constitutes acceptance of any changes."
                ]
              }
            ].map((section, index) => {
              const IconComponent = section.icon;
              
              return (
                <motion.section 
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl border border-emerald-100 p-8 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                      <IconComponent className="w-6 h-6 text-emerald-700" />
                    </div>
                    <h2 className="text-2xl font-bold text-emerald-900" style={{ fontFamily: "'Cormorant Garamond', serif" }}>{index + 1}. {section.title}</h2>
                  </div>
                  <ul className="space-y-3">
                    {section.content.map((item, i) => (
                      <li key={i} className="flex items-start">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </motion.section>
              );
            })}

            {/* Contact Section */}
            <motion.section 
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-emerald-900 to-emerald-800 rounded-2xl p-8 text-white"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4">
                  <Phone className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white" style={{ fontFamily: "'Cormorant Garamond', serif" }}>12. Contact Us</h2>
              </div>
              <p className="text-emerald-100 mb-6">For any questions about these Terms & Conditions, please contact us at:</p>
              <div className="bg-white/10 rounded-xl p-6">
                <h3 className="text-xl font-bold text-white mb-4">{getSetting('company_name', '[Your Hotel Name]')}</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="flex items-center">
                    <Mail className="w-5 h-5 text-emerald-200 mr-2" />
                    <span className="text-emerald-100">{getSetting('email_primary', '[Insert Email]')}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="w-5 h-5 text-emerald-200 mr-2" />
                    <span className="text-emerald-100">{getSetting('phone_primary', '[Insert Phone]')}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-emerald-200 mr-2" />
                    <span className="text-emerald-100">{getSetting('address_full', '[Insert Address]')}</span>
                  </div>
                </div>
              </div>
            </motion.section>
          </div>
        </div>
      </section>
    </WebsiteLayout>
  );
};

export default TermsConditions;