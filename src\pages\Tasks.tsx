import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  ClipboardList, 
  Search,
  CheckCircle,
  Clock,
  AlertTriangle,
  User,
  Bed,
  Calendar,
  MessageSquare,
  Play,
  Pause,
  RotateCcw,
  Camera
} from 'lucide-react';

interface Task {
  id: string;
  title: string;
  roomNumber: string;
  roomType: string;
  taskType: 'cleaning' | 'maintenance' | 'inspection' | 'setup';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'assigned' | 'in-progress' | 'completed' | 'blocked' | 'paused';
  assignedTo: string;
  estimatedTime: number; // in minutes
  actualTime?: number;
  startTime?: string;
  endTime?: string;
  dueTime?: string;
  description: string;
  checklistItems: {
    id: string;
    task: string;
    completed: boolean;
  }[];
  notes?: string;
  issues?: string[];
  photos?: string[];
}

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Deep Clean Room 205',
    roomNumber: '205',
    roomType: 'Deluxe',
    taskType: 'cleaning',
    priority: 'high',
    status: 'assigned',
    assignedTo: 'Maria Garcia',
    estimatedTime: 45,
    dueTime: '14:00',
    description: 'Complete deep cleaning after guest checkout',
    checklistItems: [
      { id: '1', task: 'Strip and remake bed', completed: false },
      { id: '2', task: 'Clean bathroom thoroughly', completed: false },
      { id: '3', task: 'Vacuum and mop floors', completed: false },
      { id: '4', task: 'Dust all surfaces', completed: false },
      { id: '5', task: 'Restock amenities', completed: false },
      { id: '6', task: 'Check and replace towels', completed: false }
    ]
  },
  {
    id: '2',
    title: 'Fix AC in Room 301',
    roomNumber: '301',
    roomType: 'Standard',
    taskType: 'maintenance',
    priority: 'urgent',
    status: 'in-progress',
    assignedTo: 'You',
    estimatedTime: 120,
    actualTime: 45,
    startTime: '10:30',
    dueTime: '12:00',
    description: 'Air conditioning unit not cooling properly',
    checklistItems: [
      { id: '1', task: 'Check thermostat settings', completed: true },
      { id: '2', task: 'Inspect air filter', completed: true },
      { id: '3', task: 'Check refrigerant levels', completed: false },
      { id: '4', task: 'Test compressor', completed: false },
      { id: '5', task: 'Final system test', completed: false }
    ],
    issues: ['Filter was extremely dirty', 'Thermostat showing error code']
  },
  {
    id: '3',
    title: 'Room Inspection - Suite 108',
    roomNumber: '108',
    roomType: 'Suite',
    taskType: 'inspection',
    priority: 'medium',
    status: 'completed',
    assignedTo: 'You',
    estimatedTime: 30,
    actualTime: 25,
    startTime: '08:00',
    endTime: '08:25',
    description: 'Post-cleaning quality inspection',
    checklistItems: [
      { id: '1', task: 'Check room cleanliness', completed: true },
      { id: '2', task: 'Verify amenities stocked', completed: true },
      { id: '3', task: 'Test all equipment', completed: true },
      { id: '4', task: 'Check for damages', completed: true }
    ],
    notes: 'Room passed inspection. Ready for next guest.'
  }
];

const statusColors = {
  assigned: 'bg-blue-100 text-blue-800',
  'in-progress': 'bg-orange-100 text-orange-800',
  completed: 'bg-success text-success-foreground',
  blocked: 'bg-destructive text-destructive-foreground',
  paused: 'bg-yellow-100 text-yellow-800'
};

const priorityColors = {
  low: 'bg-muted text-muted-foreground',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-destructive text-destructive-foreground'
};

const taskTypeColors = {
  cleaning: 'bg-green-100 text-green-800',
  maintenance: 'bg-orange-100 text-orange-800',
  inspection: 'bg-purple-100 text-purple-800',
  setup: 'bg-blue-100 text-blue-800'
};

const TaskCard: React.FC<{ task: Task; onTaskUpdate: (taskId: string, updates: Partial<Task>) => void }> = ({ 
  task, 
  onTaskUpdate 
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [newNote, setNewNote] = useState('');

  const handleStatusChange = (newStatus: Task['status']) => {
    const updates: Partial<Task> = { status: newStatus };
    
    if (newStatus === 'in-progress' && !task.startTime) {
      updates.startTime = new Date().toLocaleTimeString();
    } else if (newStatus === 'completed' && !task.endTime) {
      updates.endTime = new Date().toLocaleTimeString();
      updates.actualTime = task.actualTime || task.estimatedTime;
    }
    
    onTaskUpdate(task.id, updates);
  };

  const handleChecklistUpdate = (itemId: string, completed: boolean) => {
    const updatedItems = task.checklistItems.map(item => 
      item.id === itemId ? { ...item, completed } : item
    );
    onTaskUpdate(task.id, { checklistItems: updatedItems });
  };

  const addNote = () => {
    if (newNote.trim()) {
      onTaskUpdate(task.id, { notes: (task.notes || '') + '\n' + newNote });
      setNewNote('');
    }
  };

  const getStatusIcon = () => {
    switch (task.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'in-progress':
        return <Clock className="h-4 w-4" />;
      case 'blocked':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <ClipboardList className="h-4 w-4" />;
    }
  };

  const completedItems = task.checklistItems.filter(item => item.completed).length;
  const progressPercentage = (completedItems / task.checklistItems.length) * 100;

  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{task.title}</CardTitle>
            <p className="text-muted-foreground">Room {task.roomNumber} • {task.roomType}</p>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <Badge className={statusColors[task.status]}>
              {getStatusIcon()}
              <span className="ml-1">{task.status.replace('-', ' ')}</span>
            </Badge>
            <Badge className={priorityColors[task.priority]}>
              {task.priority} priority
            </Badge>
            <Badge className={taskTypeColors[task.taskType]}>
              {task.taskType}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Progress Bar */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Progress</span>
              <span>{completedItems}/{task.checklistItems.length} tasks</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary rounded-full h-2 transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>

          {/* Time Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            {task.dueTime && (
              <div>
                <span className="text-muted-foreground">Due Time:</span>
                <div className="font-medium">{task.dueTime}</div>
              </div>
            )}
            <div>
              <span className="text-muted-foreground">Estimated:</span>
              <div className="font-medium">{task.estimatedTime} min</div>
            </div>
            {task.startTime && (
              <div>
                <span className="text-muted-foreground">Started:</span>
                <div className="font-medium">{task.startTime}</div>
              </div>
            )}
            {task.actualTime && (
              <div>
                <span className="text-muted-foreground">Actual Time:</span>
                <div className="font-medium">{task.actualTime} min</div>
              </div>
            )}
          </div>

          {/* Issues Alert */}
          {task.issues && task.issues.length > 0 && (
            <div className="bg-destructive/10 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <span className="text-sm font-medium text-destructive">Issues Reported:</span>
              </div>
              <ul className="text-sm space-y-1">
                {task.issues.map((issue, index) => (
                  <li key={index} className="text-destructive">• {issue}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {task.status === 'assigned' && (
              <Button 
                size="sm" 
                className="flex-1 btn-primary"
                onClick={() => handleStatusChange('in-progress')}
              >
                <Play className="h-4 w-4 mr-1" />
                Start Task
              </Button>
            )}
            
            {task.status === 'in-progress' && (
              <>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleStatusChange('paused')}
                >
                  <Pause className="h-4 w-4 mr-1" />
                  Pause
                </Button>
                <Button 
                  size="sm" 
                  className="flex-1 bg-success hover:bg-success/90 text-success-foreground"
                  onClick={() => handleStatusChange('completed')}
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Complete
                </Button>
              </>
            )}

            {task.status === 'paused' && (
              <Button 
                size="sm" 
                className="flex-1 btn-primary"
                onClick={() => handleStatusChange('in-progress')}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Resume
              </Button>
            )}

            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Hide' : 'Details'}
            </Button>
          </div>

          {/* Detailed View */}
          {showDetails && (
            <div className="space-y-4 pt-4 border-t">
              {/* Description */}
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm text-muted-foreground">{task.description}</p>
              </div>

              {/* Checklist */}
              <div>
                <h4 className="font-medium mb-2">Checklist</h4>
                <div className="space-y-2">
                  {task.checklistItems.map((item) => (
                    <label key={item.id} className="flex items-center space-x-2 text-sm cursor-pointer">
                      <input
                        type="checkbox"
                        checked={item.completed}
                        onChange={(e) => handleChecklistUpdate(item.id, e.target.checked)}
                        className="rounded border-muted-foreground"
                      />
                      <span className={item.completed ? 'line-through text-muted-foreground' : ''}>
                        {item.task}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Notes */}
              {task.notes && (
                <div>
                  <h4 className="font-medium mb-2">Notes</h4>
                  <div className="bg-muted p-3 rounded-lg">
                    <p className="text-sm whitespace-pre-wrap">{task.notes}</p>
                  </div>
                </div>
              )}

              {/* Add Note */}
              <div>
                <h4 className="font-medium mb-2">Add Note</h4>
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Add a note about this task..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    className="min-h-[80px]"
                  />
                </div>
                <Button 
                  size="sm" 
                  onClick={addNote}
                  className="mt-2"
                  disabled={!newNote.trim()}
                >
                  <MessageSquare className="h-4 w-4 mr-1" />
                  Add Note
                </Button>
              </div>

              {/* Photo Upload */}
              <div>
                <Button size="sm" variant="outline" className="w-full">
                  <Camera className="h-4 w-4 mr-2" />
                  Add Photos
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export const Tasks: React.FC = () => {
  const [tasks, setTasks] = useState(mockTasks);
  const [searchTerm, setSearchTerm] = useState('');

  const handleTaskUpdate = (taskId: string, updates: Partial<Task>) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === taskId ? { ...task, ...updates } : task
      )
    );
  };

  const filteredTasks = tasks.filter(task => 
    task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.roomNumber.includes(searchTerm) ||
    task.taskType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const myTasks = filteredTasks.filter(task => task.assignedTo === 'You');
  const taskStats = {
    total: myTasks.length,
    assigned: myTasks.filter(t => t.status === 'assigned').length,
    inProgress: myTasks.filter(t => t.status === 'in-progress').length,
    completed: myTasks.filter(t => t.status === 'completed').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Tasks</h1>
          <p className="text-muted-foreground">Your assigned housekeeping and maintenance tasks</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{taskStats.total}</div>
            <div className="text-sm text-muted-foreground">Total Tasks</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{taskStats.assigned}</div>
            <div className="text-sm text-muted-foreground">Assigned</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{taskStats.inProgress}</div>
            <div className="text-sm text-muted-foreground">In Progress</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-success">{taskStats.completed}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search your tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Tasks List */}
      <div className="space-y-6">
        {myTasks.map((task) => (
          <TaskCard key={task.id} task={task} onTaskUpdate={handleTaskUpdate} />
        ))}
      </div>

      {myTasks.length === 0 && (
        <div className="text-center py-12">
          <ClipboardList className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tasks assigned</h3>
          <p className="text-muted-foreground">You don't have any tasks assigned at the moment</p>
        </div>
      )}
    </div>
  );
};

export default Tasks;