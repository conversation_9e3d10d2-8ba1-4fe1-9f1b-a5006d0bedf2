import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash2, Search, Eye, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import RoomTypesForm from "@/components/master-tables/RoomTypesForm";
import { motion } from "framer-motion";

interface RoomType {
  id: string;
  property_id: string;
  name: string;
  code: string;
  description?: string;
  base_occupancy: number;
  max_occupancy: number;
  extra_bed_capacity: number;
  extra_bed_charge: number;
  bed_type?: string;
  bed_count: number;
  is_active: boolean;
}

const RoomTypes = () => {
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showForm, setShowForm] = useState(false);
  const [editingRoomType, setEditingRoomType] = useState<RoomType | null>(null);
  const [viewingRoomType, setViewingRoomType] = useState<RoomType | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchRoomTypes();
  }, []);

  const fetchRoomTypes = async () => {
    try {
      const { data, error } = await supabase
        .from("room_types")
        .select("*")
        .eq("is_deleted", false)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setRoomTypes(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch room types",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this room type?")) return;

    try {
      const { error } = await supabase
        .from("room_types")
        .update({ is_deleted: true })
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Room type deleted successfully",
      });
      fetchRoomTypes();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete room type",
        variant: "destructive",
      });
    }
  };

  const filteredRoomTypes = roomTypes.filter((roomType) => {
    if (!searchTerm.trim()) return true;

    const searchWords = searchTerm.toLowerCase().trim().split(/\s+/);
    const searchableText = `${roomType.name} ${roomType.code} ${
      roomType.description || ""
    } ${roomType.bed_type || ""}`.toLowerCase();

    // Try both strict (all words) and lenient (any word) matching
    const strictMatch = searchWords.every((word) =>
      searchableText.includes(word)
    );
    const lenientMatch = searchWords.some((word) =>
      searchableText.includes(word)
    );

    // Use strict match for single words, lenient for multiple words
    return searchWords.length === 1 ? strictMatch : lenientMatch;
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-80" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
          Room Types Master
        </h1>
        <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Room Type
        </Button>
      </motion.div>

      <motion.div
        className="flex items-center space-x-4"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search room types..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </motion.div>

      <div className="flex-1 min-h-0">
        <div className="grid gap-4 pb-4">
          {filteredRoomTypes.map((roomType) => (
            <Card key={roomType.id} className="flex-shrink-0">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {roomType.name}
                      <Badge
                        variant={roomType.is_active ? "default" : "secondary"}
                      >
                        {roomType.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Code: {roomType.code}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewingRoomType(roomType)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingRoomType(roomType);
                        setShowForm(true);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(roomType.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
                  <div>
                    <span className="font-medium">Base Occupancy:</span>{" "}
                    {roomType.base_occupancy}
                  </div>
                  <div>
                    <span className="font-medium">Max Occupancy:</span>{" "}
                    {roomType.max_occupancy}
                  </div>
                  <div>
                    <span className="font-medium">Bed Type:</span>{" "}
                    {roomType.bed_type || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Bed Count:</span>{" "}
                    {roomType.bed_count}
                  </div>
                  <div>
                    <span className="font-medium">Extra Bed:</span>{" "}
                    {roomType.extra_bed_capacity}
                  </div>
                  <div>
                    <span className="font-medium">Extra Charge:</span> ₹
                    {roomType.extra_bed_charge}
                  </div>
                </div>
                {roomType.description && (
                  <p className="text-sm text-muted-foreground mt-2">
                    {roomType.description}
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {showForm && (
        <RoomTypesForm
          roomType={editingRoomType}
          onClose={() => {
            setShowForm(false);
            setEditingRoomType(null);
          }}
          onSuccess={() => {
            fetchRoomTypes();
            setShowForm(false);
            setEditingRoomType(null);
          }}
        />
      )}

      {viewingRoomType && (
        <Dialog
          open={!!viewingRoomType}
          onOpenChange={() => setViewingRoomType(null)}
        >
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader className="sticky top-0 bg-background z-10 pb-4">
              <DialogTitle>Room Type Details</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 pb-4">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Name
                  </label>
                  <p className="text-base font-medium">
                    {viewingRoomType.name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Code
                  </label>
                  <p className="text-base">{viewingRoomType.code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Base Occupancy
                  </label>
                  <p className="text-base">
                    {viewingRoomType.base_occupancy} persons
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Max Occupancy
                  </label>
                  <p className="text-base">
                    {viewingRoomType.max_occupancy} persons
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Bed Type
                  </label>
                  <p className="text-base">
                    {viewingRoomType.bed_type || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Bed Count
                  </label>
                  <p className="text-base">{viewingRoomType.bed_count}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Extra Bed Capacity
                  </label>
                  <p className="text-base">
                    {viewingRoomType.extra_bed_capacity}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Extra Bed Charge
                  </label>
                  <p className="text-base">
                    ₹{viewingRoomType.extra_bed_charge}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Status
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        viewingRoomType.is_active
                          ? "bg-green-500"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <Badge
                      variant={
                        viewingRoomType.is_active ? "default" : "secondary"
                      }
                      className="font-medium"
                    >
                      {viewingRoomType.is_active ? "✓ Active" : "✕ Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>

              {viewingRoomType.description && (
                <div className="border-t pt-3">
                  <label className="text-sm font-medium text-muted-foreground">
                    Description
                  </label>
                  <p className="text-base mt-1">
                    {viewingRoomType.description}
                  </p>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default RoomTypes;
