import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  ClipboardList, 
  Search, 
  Filter,
  Plus,
  CheckCircle,
  Clock,
  AlertTriangle,
  User,
  Bed,
  Calendar,
  MessageSquare
} from 'lucide-react';

interface HousekeepingTask {
  id: string;
  roomNumber: string;
  roomType: string;
  taskType: 'cleaning' | 'maintenance' | 'inspection';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  assignedTo: string;
  estimatedTime: number; // in minutes
  actualTime?: number;
  checkoutTime?: string;
  checkinTime?: string;
  notes?: string;
  issues?: string[];
  createdAt: string;
  completedAt?: string;
}

const mockTasks: HousekeepingTask[] = [
  {
    id: '1',
    roomNumber: '205',
    roomType: 'Deluxe',
    taskType: 'cleaning',
    priority: 'high',
    status: 'pending',
    assignedTo: 'Maria Garcia',
    estimatedTime: 45,
    checkoutTime: '11:00 AM',
    checkinTime: '3:00 PM',
    createdAt: '2024-01-15T09:00:00Z'
  },
  {
    id: '2',
    roomNumber: '108',
    roomType: 'Suite',
    taskType: 'cleaning',
    priority: 'medium',
    status: 'in-progress',
    assignedTo: 'Lisa Chen',
    estimatedTime: 60,
    actualTime: 35,
    checkoutTime: '10:30 AM',
    checkinTime: '4:00 PM',
    createdAt: '2024-01-15T08:30:00Z'
  },
  {
    id: '3',
    roomNumber: '301',
    roomType: 'Standard',
    taskType: 'maintenance',
    priority: 'urgent',
    status: 'blocked',
    assignedTo: 'John Rodriguez',
    estimatedTime: 120,
    issues: ['AC not working', 'Plumbing issue'],
    notes: 'Need maintenance team assistance',
    createdAt: '2024-01-15T07:00:00Z'
  },
  {
    id: '4',
    roomNumber: '103',
    roomType: 'Standard',
    taskType: 'cleaning',
    priority: 'low',
    status: 'completed',
    assignedTo: 'Sofia Martinez',
    estimatedTime: 30,
    actualTime: 28,
    completedAt: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-15T09:45:00Z'
  }
];

const statusColors = {
  pending: 'bg-warning text-warning-foreground',
  'in-progress': 'bg-primary text-primary-foreground',
  completed: 'bg-success text-success-foreground',
  blocked: 'bg-destructive text-destructive-foreground'
};

const priorityColors = {
  low: 'bg-muted text-muted-foreground',
  medium: 'bg-primary/20 text-primary',
  high: 'bg-warning/20 text-warning-foreground',
  urgent: 'bg-destructive/20 text-destructive'
};

const taskTypeColors = {
  cleaning: 'bg-blue-100 text-blue-800',
  maintenance: 'bg-orange-100 text-orange-800',
  inspection: 'bg-purple-100 text-purple-800'
};

const TaskCard: React.FC<{ task: HousekeepingTask }> = ({ task }) => {
  const [showNotes, setShowNotes] = useState(false);

  return (
    <Card className="card-hover">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">Room {task.roomNumber}</CardTitle>
            <p className="text-muted-foreground">{task.roomType}</p>
          </div>
          <div className="flex gap-2">
            <Badge className={taskTypeColors[task.taskType]}>
              {task.taskType}
            </Badge>
            <Badge className={statusColors[task.status]}>
              {task.status.replace('-', ' ')}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Assignment & Priority */}
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{task.assignedTo}</span>
            </div>
            <Badge className={priorityColors[task.priority]}>
              {task.priority} priority
            </Badge>
          </div>

          {/* Times */}
          {task.checkoutTime && task.checkinTime && (
            <div className="bg-accent p-3 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-muted-foreground">Checkout</p>
                  <p className="text-sm font-medium">{task.checkoutTime}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Next Checkin</p>
                  <p className="text-sm font-medium">{task.checkinTime}</p>
                </div>
              </div>
            </div>
          )}

          {/* Time Estimate */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Est. {task.estimatedTime} min</span>
            </div>
            {task.actualTime && (
              <span className="text-sm text-success">
                Actual: {task.actualTime} min
              </span>
            )}
          </div>

          {/* Issues */}
          {task.issues && task.issues.length > 0 && (
            <div className="bg-destructive/10 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <span className="text-sm font-medium text-destructive">Issues:</span>
              </div>
              <ul className="text-sm space-y-1">
                {task.issues.map((issue, index) => (
                  <li key={index} className="text-destructive">• {issue}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Notes */}
          {task.notes && (
            <div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowNotes(!showNotes)}
                className="p-0 h-auto text-primary hover:text-primary-light"
              >
                <MessageSquare className="h-4 w-4 mr-1" />
                View Notes
              </Button>
              {showNotes && (
                <div className="mt-2 p-3 bg-muted rounded-lg">
                  <p className="text-sm">{task.notes}</p>
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            {task.status === 'pending' && (
              <Button size="sm" className="flex-1 btn-primary">
                Start Task
              </Button>
            )}
            {task.status === 'in-progress' && (
              <Button size="sm" className="flex-1 bg-success hover:bg-success/90 text-success-foreground">
                <CheckCircle className="h-4 w-4 mr-1" />
                Complete
              </Button>
            )}
            <Button size="sm" variant="outline" className="flex-1">
              View Details
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const Housekeeping: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  const filteredTasks = mockTasks.filter(task => {
    const matchesSearch = 
      task.roomNumber.includes(searchTerm) ||
      task.assignedTo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.roomType.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const taskStats = {
    total: mockTasks.length,
    pending: mockTasks.filter(t => t.status === 'pending').length,
    inProgress: mockTasks.filter(t => t.status === 'in-progress').length,
    completed: mockTasks.filter(t => t.status === 'completed').length,
    blocked: mockTasks.filter(t => t.status === 'blocked').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Housekeeping Management</h1>
          <p className="text-muted-foreground">Manage cleaning tasks and room maintenance</p>
        </div>
        <Button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Add Task
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{taskStats.total}</div>
            <div className="text-sm text-muted-foreground">Total Tasks</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-warning">{taskStats.pending}</div>
            <div className="text-sm text-muted-foreground">Pending</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{taskStats.inProgress}</div>
            <div className="text-sm text-muted-foreground">In Progress</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-success">{taskStats.completed}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-destructive">{taskStats.blocked}</div>
            <div className="text-sm text-muted-foreground">Blocked</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by room, staff, or room type..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="blocked">Blocked</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <AlertTriangle className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tasks Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredTasks.map((task) => (
          <TaskCard key={task.id} task={task} />
        ))}
      </div>

      {filteredTasks.length === 0 && (
        <div className="text-center py-12">
          <ClipboardList className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tasks found</h3>
          <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default Housekeeping;