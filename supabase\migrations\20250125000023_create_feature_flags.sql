-- Create feature_flags table for managing application features
CREATE TABLE feature_flags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  flag_key VARCHAR(100) UNIQUE NOT NULL,
  flag_name VARCHAR(200) NOT NULL,
  description TEXT,
  is_enabled BOOLEAN DEFAULT FALSE,
  category VARCHAR(50) DEFAULT 'general',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX idx_feature_flags_key ON feature_flags(flag_key);
CREATE INDEX idx_feature_flags_category ON feature_flags(category);
CREATE INDEX idx_feature_flags_enabled ON feature_flags(is_enabled);

-- Enable RLS
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can view enabled flags" ON feature_flags
  FOR SELECT USING (is_enabled = true);

CREATE POLICY "Authenticated users can manage flags" ON feature_flags
  FOR ALL USING (auth.role() = 'authenticated');

-- Insert default feature flags
INSERT INTO feature_flags (flag_key, flag_name, description, is_enabled, category) VALUES
('property_booking_enabled', 'Property Booking Buttons', 'Show Details and Book buttons on properties page. When disabled, shows Coming Soon button instead.', true, 'properties'),
('online_reservations', 'Online Reservations', 'Enable online table reservations and room bookings', true, 'bookings'),
('event_booking', 'Event Booking', 'Allow users to book events and venues online', true, 'events'),
('page_about_visible', 'About Us Page', 'Show/hide About Us page in navigation and access', true, 'pages'),
('page_services_visible', 'Services Page', 'Show/hide Services page in navigation and access', true, 'pages'),
('page_properties_visible', 'Properties Page', 'Show/hide Properties page in navigation and access', true, 'pages'),
('page_events_visible', 'Events & Venues Page', 'Show/hide Events & Venues page in navigation and access', true, 'pages');

-- Create trigger for updated_at
CREATE TRIGGER update_feature_flags_updated_at 
  BEFORE UPDATE ON feature_flags 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();