import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { motion } from 'framer-motion';
import {
  Calendar,
  Image,
  Wifi,
  Snowflake,
  Tv2,
  CupSoda,
  Coffee,
  BedDouble,
  ShowerHead,
  CheckCircle2,
  Building2,
  Sparkles,
  Bath
} from 'lucide-react';

interface Property {
  id: string;
  name: string;
  description: string;
  image_url: string;
  price_per_night: number;
  amenities: string[];
  is_active: boolean;
}

const iconMap: { [key: string]: React.ComponentType<any> } = {
  'WiFi': Wifi,
  'AC': Snowflake,
  'Smart TV': Tv2,
  'Minibar': CupSoda,
  'Tea/Coffee': Coffee,
  'King Bed': BedDouble,
  'Rain Shower': ShowerHead,
  'Butler Service': CheckCircle2,
  'Lounge': Building2,
  'Luxury Linen': Sparkles,
  'Soaking Tub': Bath
};

export const RoomsShowcase: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProperties();
  }, []);

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('is_active', true)
        .order('price_per_night')
        .limit(3);

      if (error) throw error;
      setProperties(data || []);
    } catch (error) {
      console.error('Error fetching properties:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section className="bg-slate-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mb-8"></div>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-5">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white rounded-2xl h-80"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="bg-slate-50">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          viewport={{ once: true }}
          className="flex flex-col sm:flex-row items-start sm:items-end justify-between gap-3 sm:gap-4"
        >
          <div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
              Rooms & Suites
            </h2>
            <p className="text-sm sm:text-base text-slate-600 mt-1">Choose your perfect stay with immersive visuals and amenities.</p>
          </div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/properties" className="px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-[13px] ring-1 ring-slate-200 hover:ring-emerald-300 transition">
              View All
            </Link>
          </motion.div>
        </motion.div>

        <div className="mt-6 sm:mt-8 grid grid-cols-1 sm:grid-cols-2 laptop-sm:grid-cols-3 gap-4 sm:gap-5">
          {properties.map((property, index) => (
            <motion.div 
              key={property.id} 
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.15, ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="bg-white rounded-2xl overflow-hidden ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300"
            >
              <div className="relative overflow-hidden">
                <motion.img 
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.4 }}
                  src={property.image_url} 
                  className="h-40 sm:h-48 w-full object-cover" 
                  alt={property.name} 
                />
              </div>
              <div className="p-3 sm:p-4">
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="flex items-start justify-between gap-2 sm:gap-3"
                >
                  <div className="min-w-0 flex-1">
                    <h3 className="text-base sm:text-[18px] font-semibold text-emerald-950 truncate">{property.name}</h3>
                    <p className="text-xs sm:text-[13px] text-slate-500 line-clamp-2">{property.description}</p>
                  </div>
                  <div className="text-right flex-shrink-0">
                    <div className="text-[10px] sm:text-[11px] text-slate-500">From</div>
                    <div className="text-sm sm:text-[15px] font-semibold text-emerald-900">₹{property.price_per_night?.toLocaleString()}</div>
                  </div>
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="mt-2 sm:mt-3 flex flex-wrap items-center gap-1.5 sm:gap-2 text-[11px] sm:text-[12px] text-slate-600"
                >
                  {property.amenities?.slice(0, 4).map((amenity, idx) => {
                    const IconComponent = iconMap[amenity] || Wifi;
                    return (
                      <motion.span 
                        key={idx} 
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: 0.4 + (idx * 0.1) }}
                        viewport={{ once: true }}
                        whileHover={{ scale: 1.1 }}
                        className="inline-flex items-center gap-1 sm:gap-1.5 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full bg-slate-50 ring-1 ring-slate-200 hover:bg-emerald-50 hover:ring-emerald-200 transition-all"
                      >
                        <IconComponent className="w-3 h-3 sm:w-3.5 sm:h-3.5" /> 
                        <span className="hidden sm:inline">{amenity}</span>
                        <span className="sm:hidden">{amenity.slice(0, 3)}</span>
                      </motion.span>
                    );
                  })}
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 }}
                  viewport={{ once: true }}
                  className="mt-3 sm:mt-4 flex items-center justify-between"
                >
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link to="/properties" className="inline-flex items-center gap-1.5 sm:gap-2 text-xs sm:text-[14px] text-emerald-900 hover:text-emerald-700 transition">
                      <Calendar className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                      Book
                    </Link>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link to="/gallery" className="inline-flex items-center gap-1.5 sm:gap-2 text-xs sm:text-[14px] text-slate-700 hover:text-emerald-900 transition">
                      <Image className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                      Gallery
                    </Link>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mt-6 sm:mt-8 flex items-center justify-center"
        >
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link to="/properties" className="inline-flex items-center gap-2 rounded-full px-4 sm:px-5 py-2 sm:py-2.5 text-xs sm:text-[14px] text-emerald-950 font-medium ring-1 ring-slate-300 hover:ring-emerald-300 hover:bg-emerald-50 transition">
              View All Properties
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default RoomsShowcase;