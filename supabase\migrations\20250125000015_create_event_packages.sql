-- Create event_packages table
CREATE TABLE IF NOT EXISTS public.event_packages (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  title text NOT NULL,
  description text NOT NULL,
  capacity text NOT NULL,
  features text[] DEFAULT '{}',
  price text NOT NULL,
  display_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.event_packages ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate policies
CREATE POLICY "Allow public read access to event_packages" ON public.event_packages
  FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to manage event_packages" ON public.event_packages
  FOR ALL USING (auth.role() = 'authenticated');

-- Create updated_at trigger
CREATE TRIGGER handle_event_packages_updated_at
  BEFORE UPDATE ON public.event_packages
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Insert sample event packages
INSERT INTO public.event_packages (title, description, capacity, features, price, display_order, is_active) VALUES
('Grand Weddings', 'Elegant celebrations with traditional and contemporary setups', '100-500 guests', ARRAY['Mandap Decoration', 'Multi-cuisine Catering', 'Photography', 'Live Music', 'Bridal Suite', 'Guest Accommodation'], 'Custom packages from ₹1,500/person', 1, true),
('Corporate Events', 'Professional venues for conferences and business meetings', '25-300 guests', ARRAY['AV Equipment', 'High-speed WiFi', 'Business Lunch', 'Parking', 'Meeting Rooms', 'Accommodation'], 'Starting ₹800/person', 2, true),
('Birthday Celebrations', 'Memorable parties with personalized themes and entertainment', '20-150 guests', ARRAY['Theme Decoration', 'Custom Cakes', 'Entertainment', 'Photography', 'Special Menus', 'Party Games'], 'Starting ₹1,200/person', 3, true),
('Social Gatherings', 'Intimate spaces for family reunions and celebrations', '30-200 guests', ARRAY['Flexible Layouts', 'Sound System', 'Catering Options', 'Decoration', 'Valet Parking', 'Service Staff'], 'Customized pricing available', 4, true);