import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Hotel, Calendar, DollarSign, Package, BarChart3, ArrowRightLeft, FileText, Shield, Link } from 'lucide-react';

const MultiProperty = () => {
  const [selectedTab, setSelectedTab] = useState('registry');

  const properties = [
    { id: 'P001', name: 'Red Chili Main', location: 'Bardoli', rooms: 50, status: 'Active', occupancy: '85%', revenue: '$45,200', manager: '<PERSON>' },
    { id: 'P002', name: 'Red Chili Express', location: 'Surat', rooms: 30, status: 'Active', occupancy: '72%', revenue: '$28,400', manager: '<PERSON>' },
    { id: 'P003', name: 'Red Chili Resort', location: 'Daman', rooms: 80, status: 'Maintenance', occupancy: '0%', revenue: '$0', manager: 'Mike Johnson' },
  ];

  const availability = [
    { property: 'Red Chili Main', single: 5, double: 8, suite: 2, total: 15, percentage: '70%' },
    { property: 'Red Chili Express', single: 12, double: 6, suite: 1, total: 19, percentage: '63%' },
    { property: 'Red Chili Resort', single: 0, double: 0, suite: 0, total: 0, percentage: '0%' },
  ];

  const ratePlans = [
    { plan: 'Standard Rate', property: 'All Properties', season: 'Regular', rate: '$120', restrictions: 'None', status: 'Active' },
    { plan: 'Weekend Special', property: 'Main & Express', season: 'Weekend', rate: '$150', restrictions: 'Min 2 nights', status: 'Active' },
    { plan: 'Corporate Rate', property: 'All Properties', season: 'All Year', rate: '$100', restrictions: 'Corporate ID', status: 'Active' },
  ];

  const inventory = [
    { item: 'Towels', main: 250, express: 150, resort: 0, status: 'Good', lastUpdated: '2024-01-15' },
    { item: 'Bed Sheets', main: 180, express: 120, resort: 0, status: 'Low', lastUpdated: '2024-01-14' },
    { item: 'Toiletries', main: 500, express: 300, resort: 0, status: 'Good', lastUpdated: '2024-01-13' },
  ];

  const contracts = [
    { id: 'C001', client: 'Tech Corp', properties: 'All', rate: '$95', rooms: 10, validity: '2024-12-31', status: 'Active' },
    { id: 'C002', client: 'Travel Agency XYZ', properties: 'Main, Express', rate: '$110', rooms: 15, validity: '2024-06-30', status: 'Active' },
    { id: 'C003', client: 'Event Company', properties: 'Resort', rate: '$130', rooms: 20, validity: '2024-08-15', status: 'Inactive' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'default';
      case 'Inactive': return 'secondary';
      case 'Maintenance': return 'destructive';
      case 'Good': return 'default';
      case 'Low': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Multi-Property Management</h1>
          <p className="text-muted-foreground">Centralized control for multiple hotel properties</p>
        </div>
        <Button>
          <Hotel className="mr-2 h-4 w-4" />
          Add Property
        </Button>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-9">
          <TabsTrigger value="registry">Registry</TabsTrigger>
          <TabsTrigger value="availability">Availability</TabsTrigger>
          <TabsTrigger value="rates">Rates</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="reporting">Reporting</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="audit">Audit</TabsTrigger>
          <TabsTrigger value="channel">Channel</TabsTrigger>
        </TabsList>

        <TabsContent value="registry" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Hotel className="h-5 w-5" />
                Property Registry
              </CardTitle>
              <CardDescription>Manage multiple properties and staff access</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <Input placeholder="Search properties..." className="max-w-sm" />
                <Button variant="outline">Filter</Button>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Property ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Rooms</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Occupancy</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Manager</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {properties.map((property) => (
                    <TableRow key={property.id}>
                      <TableCell className="font-medium">{property.id}</TableCell>
                      <TableCell>{property.name}</TableCell>
                      <TableCell>{property.location}</TableCell>
                      <TableCell>{property.rooms}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(property.status)}>{property.status}</Badge>
                      </TableCell>
                      <TableCell>{property.occupancy}</TableCell>
                      <TableCell>{property.revenue}</TableCell>
                      <TableCell>{property.manager}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="availability" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Cross-Property Availability
              </CardTitle>
              <CardDescription>Unified availability across all properties</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Property</TableHead>
                    <TableHead>Single Rooms</TableHead>
                    <TableHead>Double Rooms</TableHead>
                    <TableHead>Suites</TableHead>
                    <TableHead>Total Available</TableHead>
                    <TableHead>Availability %</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {availability.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{item.property}</TableCell>
                      <TableCell>{item.single}</TableCell>
                      <TableCell>{item.double}</TableCell>
                      <TableCell>{item.suite}</TableCell>
                      <TableCell>{item.total}</TableCell>
                      <TableCell>{item.percentage}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Central Rate Management
              </CardTitle>
              <CardDescription>Manage rate plans and pricing across properties</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Rate Plan</TableHead>
                    <TableHead>Property</TableHead>
                    <TableHead>Season</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Restrictions</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {ratePlans.map((plan, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{plan.plan}</TableCell>
                      <TableCell>{plan.property}</TableCell>
                      <TableCell>{plan.season}</TableCell>
                      <TableCell>{plan.rate}</TableCell>
                      <TableCell>{plan.restrictions}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(plan.status)}>{plan.status}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Shared Inventory Management
              </CardTitle>
              <CardDescription>Monitor inventory across all properties</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Main Hotel</TableHead>
                    <TableHead>Express</TableHead>
                    <TableHead>Resort</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inventory.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{item.item}</TableCell>
                      <TableCell>{item.main}</TableCell>
                      <TableCell>{item.express}</TableCell>
                      <TableCell>{item.resort}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(item.status)}>{item.status}</Badge>
                      </TableCell>
                      <TableCell>{item.lastUpdated}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reporting" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Centralized Reporting
              </CardTitle>
              <CardDescription>Chain-wide performance analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Total Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">$73,600</div>
                    <p className="text-xs text-muted-foreground">All properties</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Avg Occupancy</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">52%</div>
                    <p className="text-xs text-muted-foreground">Chain average</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Active Properties</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2/3</div>
                    <p className="text-xs text-muted-foreground">Operational</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Total Rooms</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">160</div>
                    <p className="text-xs text-muted-foreground">Room inventory</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workflows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowRightLeft className="h-5 w-5" />
                Inter-Property Workflows
              </CardTitle>
              <CardDescription>Manage guest transfers and shared operations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <ArrowRightLeft className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Guest transfer ledger and workflow management</p>
                <Button className="mt-4">Create Transfer</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contracts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Corporate Contracts
              </CardTitle>
              <CardDescription>Manage corporate and agent contracts</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contract ID</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Properties</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Room Allotment</TableHead>
                    <TableHead>Valid Until</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {contracts.map((contract) => (
                    <TableRow key={contract.id}>
                      <TableCell className="font-medium">{contract.id}</TableCell>
                      <TableCell>{contract.client}</TableCell>
                      <TableCell>{contract.properties}</TableCell>
                      <TableCell>{contract.rate}</TableCell>
                      <TableCell>{contract.rooms}</TableCell>
                      <TableCell>{contract.validity}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(contract.status)}>{contract.status}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Data Governance & Audit
              </CardTitle>
              <CardDescription>Audit logs and data governance tracking</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">System audit logs and data governance events</p>
                <Button className="mt-4">View Audit Trail</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Channel Strategy
              </CardTitle>
              <CardDescription>OTA rate parity and distribution strategy</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Link className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Rate parity rules and channel distribution logs</p>
                <Button className="mt-4">Manage Channels</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MultiProperty;