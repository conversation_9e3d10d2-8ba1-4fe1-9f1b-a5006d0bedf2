-- Create company_settings table
CREATE TABLE IF NOT EXISTS public.company_settings (
  id uuid not null default gen_random_uuid (),
  setting_key character varying not null,
  setting_value text null,
  setting_type character varying null default 'text'::character varying,
  category character varying null,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  created_by uuid null,
  updated_by uuid null,
  constraint company_settings_pkey primary key (id),
  constraint company_settings_setting_key_key unique (setting_key),
  constraint company_settings_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint company_settings_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

-- Create trigger
CREATE TRIGGER update_company_settings_updated_at 
BEFORE UPDATE ON company_settings 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE public.company_settings ENABLE ROW LEVEL SECURITY;

-- R<PERSON> Policies
CREATE POLICY "Allow public read access for active settings" ON public.company_settings
    FOR SELECT USING (is_active = true AND is_deleted = false);

CREATE POLICY "Allow authenticated users to manage settings" ON public.company_settings
    FOR ALL USING (true);

-- Insert company settings data
INSERT INTO public.company_settings (setting_key, setting_value, setting_type, category, description) VALUES
-- Contact Information
('company_name', 'Meadow De Jalsa', 'text', 'contact', 'Company name'),
('company_tagline', 'Where Luxury Meets Tradition', 'text', 'contact', 'Company tagline'),
('phone_primary', '+91 87400-27008', 'text', 'contact', 'Primary phone number'),
('phone_secondary', '+91 87400-27009', 'text', 'contact', 'Secondary phone number'),
('email_primary', '<EMAIL>', 'email', 'contact', 'Primary email address'),
('email_secondary', '<EMAIL>', 'email', 'contact', 'Secondary email address'),
('address_full', 'Bardoli–Vyara Highway, Bardoli', 'textarea', 'contact', 'Full address'),
('address_city', 'Bardoli', 'text', 'contact', 'City'),
('address_state', 'Gujarat', 'text', 'contact', 'State'),
('address_pincode', '394601', 'text', 'contact', 'Pin code'),
('business_hours', 'Hotel: 24/7 · Restaurant: 11 AM – 11 PM', 'text', 'contact', 'Business hours'),
('map_embed_url', 'https://maps.google.com/maps?q=Bardoli-Vyara%20Highway&t=&z=12&ie=UTF8&iwloc=&output=embed', 'url', 'contact', 'Google Maps embed URL'),

-- Social Media Links
('social_facebook', 'https://facebook.com/meadowdejalsa', 'url', 'social', 'Facebook page URL'),
('social_instagram', 'https://instagram.com/meadowdejalsa', 'url', 'social', 'Instagram profile URL'),
('social_twitter', 'https://twitter.com/meadowdejalsa', 'url', 'social', 'Twitter profile URL'),
('social_youtube', 'https://youtube.com/@meadowdejalsa', 'url', 'social', 'YouTube channel URL'),
('social_linkedin', 'https://linkedin.com/company/meadowdejalsa', 'url', 'social', 'LinkedIn company page URL'),
('social_whatsapp', 'https://wa.me/918740027008', 'url', 'social', 'WhatsApp business number'),

-- Company Details
('company_description', 'Experience elegant stays, gourmet dining, and unforgettable celebrations at Meadow De Jalsa.', 'textarea', 'company', 'Company description'),
('company_established', '2020', 'text', 'company', 'Year established'),
('company_license', 'HTL-2020-001', 'text', 'company', 'Hotel license number'),
('company_gst', '24XXXXX1234X1ZX', 'text', 'company', 'GST number'),

-- Contact Form Settings
('contact_form_title', 'Send us a message', 'text', 'form', 'Contact form title'),
('contact_form_subtitle', 'We''d love to help you plan your stay or event. Reach out to us and our team will get back shortly.', 'textarea', 'form', 'Contact form subtitle'),
('contact_success_message', 'Thank you for your message! We will get back to you soon.', 'textarea', 'form', 'Success message after form submission'),

-- SEO Settings
('meta_title', 'Meadow De Jalsa - Luxury Hotel & Event Venue in Bardoli', 'text', 'seo', 'Website meta title'),
('meta_description', 'Experience luxury accommodation and world-class event venues at Meadow De Jalsa, located on Bardoli-Vyara Highway.', 'textarea', 'seo', 'Website meta description'),
('meta_keywords', 'hotel, luxury, events, wedding, bardoli, vyara, gujarat', 'text', 'seo', 'Website meta keywords');