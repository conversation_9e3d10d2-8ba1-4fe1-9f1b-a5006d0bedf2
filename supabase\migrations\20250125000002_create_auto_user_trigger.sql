-- Create function to automatically add user to users table when auth user is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    default_role_id UUID;
BEGIN
    -- Get default role ID (guest)
    SELECT id INTO default_role_id FROM public.roles WHERE name = 'guest';
    
    -- Insert into users table
    INSERT INTO public.users (
        id,
        auth_user_id, 
        email, 
        role_id, 
        created_at, 
        updated_at
    )
    VALUES (
        NEW.id,
        NEW.id, 
        NEW.email, 
        default_role_id, 
        now(), 
        now()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to run function when new auth user is created
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();