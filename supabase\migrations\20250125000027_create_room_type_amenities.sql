-- Create room_type_amenities junction table
CREATE TABLE IF NOT EXISTS public.room_type_amenities (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  room_type_id uuid NOT NULL REFERENCES public.room_types(id) ON DELETE CASCADE,
  amenity_id uuid NOT NULL REFERENCES public.amenities_admin(id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  created_by uuid REFERENCES auth.users(id),
  UNIQUE(room_type_id, amenity_id)
);

-- Enable RLS
ALTER TABLE public.room_type_amenities ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access to room_type_amenities" ON public.room_type_amenities
  FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to manage room_type_amenities" ON public.room_type_amenities
  FOR ALL USING (auth.role() = 'authenticated');

-- Create index for better performance
CREATE INDEX idx_room_type_amenities_room_type_id ON public.room_type_amenities(room_type_id);
CREATE INDEX idx_room_type_amenities_amenity_id ON public.room_type_amenities(amenity_id);