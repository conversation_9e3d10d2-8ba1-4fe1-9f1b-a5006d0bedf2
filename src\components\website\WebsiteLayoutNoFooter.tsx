import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Menu, X } from 'lucide-react';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import { FEATURE_FLAGS } from '@/utils/featureFlags';
import { useScrollToTop } from '@/hooks/useScrollToTop';
import ScrollToTop from '@/components/ui/ScrollToTop';
import { motion, AnimatePresence } from 'framer-motion';

interface WebsiteLayoutNoFooterProps {
  children: React.ReactNode;
}

export const WebsiteLayoutNoFooter: React.FC<WebsiteLayoutNoFooterProps> = ({ children }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { isEnabled } = useFeatureFlags();
  useScrollToTop();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about', flag: FEATURE_FLAGS.PAGE_ABOUT_VISIBLE },
    { name: 'Services', href: '/services', flag: FEATURE_FLAGS.PAGE_SERVICES_VISIBLE },
    { name: 'Properties', href: '/properties', flag: FEATURE_FLAGS.PAGE_PROPERTIES_VISIBLE },
    { name: 'Events & Venues', href: '/events', flag: FEATURE_FLAGS.PAGE_EVENTS_VISIBLE },
    { name: 'Contact', href: '/contact' },
  ].filter(item => !item.flag || isEnabled(item.flag));

  return (
    <div className="min-h-screen bg-background">
      {/* Modern Floating Navbar */}
      <motion.nav 
        initial={{ y: -100, opacity: 0, x: "-50%" }}
        animate={{ y: 0, opacity: 1, x: "-50%" }}
        transition={{ duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="fixed top-4 left-1/2 z-50 transition-all duration-500 w-[95%] sm:w-[85%] laptop-sm:w-[75%] laptop:w-[70%] xl:w-[60%]"
        style={{ transform: "translateX(-50%)" }}
      >
        <motion.div 
          whileHover={{ scale: 1.01 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
          className="relative bg-[#1e2a23] backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-[#81928d]/20 via-[#81928d]/10 to-[#81928d]/20 rounded-2xl blur-sm opacity-50"></div>

          <div className="relative px-4 sm:px-6 py-3 sm:py-4">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <Link to="/" className="flex items-center space-x-3 group">
                <motion.div 
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                  className="relative w-full"
                >
                  <img src="/logo2.png" alt="Meadow De Jalsa" className="w-full h-8 sm:h-12 rounded-full object-contain " />
                </motion.div>
              </Link>

              {/* Desktop Navigation */}
              <div className="hidden laptop-sm:flex items-center space-x-1">
                {navigation.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ y: -10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: index * 0.05, duration: 0.3, ease: "easeOut" }}
                  >
                    <Link
                      to={item.href}
                      className="relative px-3 laptop:px-4 py-2 text-xs laptop:text-sm font-medium text-white hover:text-[#81928d] transition-all duration-300 rounded-xl group overflow-hidden"
                    >
                      <span className="relative z-10">{item.name}</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-[#81928d]/10 to-[#81928d]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                      <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[#81928d] to-[#81928d] group-hover:w-full transition-all duration-300"></div>
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* CTA Button */}
              <motion.div 
                initial={{ x: 20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.3, ease: "easeOut" }}
                className="hidden laptop-sm:block"
              >
                <Link to="/admin">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button className="bg-[#81928d] hover:bg-[#6d7d78] text-white px-4 laptop:px-6 py-2 rounded-full text-xs laptop:text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300">
                      Admin Portal
                    </Button>
                  </motion.div>
                </Link>
              </motion.div>

              {/* Mobile Menu Button */}
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="laptop-sm:hidden p-2 rounded-xl hover:bg-white/10 transition-all duration-300 group"
              >
                <AnimatePresence mode="wait">
                  {isMobileMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ rotate: -90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: 90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <X size={20} className="text-white group-hover:text-[#81928d] transition-colors" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ rotate: 90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: -90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Menu size={20} className="text-white group-hover:text-[#81928d] transition-colors" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div 
              initial={{ opacity: 0, y: -10, scale: 0.98 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.98 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="laptop-sm:hidden absolute top-full left-0 right-0 mt-2 bg-white/90 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl overflow-hidden"
            >
              <div className="p-6">
                <div className="flex flex-col space-y-3">
                  {navigation.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ x: -10, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: index * 0.03, duration: 0.2, ease: "easeOut" }}
                    >
                      <Link
                        to={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="text-left text-gray-700 hover:text-[#81928d] hover:bg-gradient-to-r hover:from-[#81928d]/5 hover:to-[#81928d]/5 transition-all duration-300 py-3 px-4 rounded-xl font-medium group block"
                      >
                        <span className="relative z-10">{item.name}</span>
                      </Link>
                    </motion.div>
                  ))}
                  <motion.div 
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: navigation.length * 0.03, duration: 0.2, ease: "easeOut" }}
                    className="pt-4 border-t border-gray-200/50"
                  >
                    <Link to="/admin">
                      <Button className="w-full bg-[#81928d] hover:bg-[#6d7d78] text-white px-6 py-2 rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300">
                        Admin Portal
                      </Button>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      <ScrollToTop />
    </div>
  );
};