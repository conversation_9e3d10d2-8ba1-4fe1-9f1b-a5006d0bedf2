import React, { useState, useEffect } from 'react';
import { Clock, ChevronLeft, ChevronRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';

interface TeamMember {
  id: string;
  name: string;
  position: string;
  experience: string;
  specialty: string;
  bio: string;
  image_url: string;
  display_order: number;
  is_active: boolean;
}

const TeamSection: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    fetchTeamMembers();
  }, []);

  const fetchTeamMembers = async () => {
    try {
      const { data } = await supabase
        .from('team_members')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      setTeamMembers(data || []);
    } catch (error) {
      console.error('Error fetching team members:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section className="bg-slate-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-slate-200 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-slate-200 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (teamMembers.length === 0) {
    return null;
  }

  const hasMoreThanFour = teamMembers.length > 4;
  const visibleMembers = hasMoreThanFour 
    ? teamMembers.slice(currentIndex, currentIndex + 4)
    : teamMembers;

  const nextSlide = () => {
    if (currentIndex + 4 < teamMembers.length) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  return (
    <section className="bg-slate-50">
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
        className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16"
      >
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl sm:text-4xl tracking-tight text-emerald-950 font-semibold" style={{ fontFamily: "'Cormorant Garamond', serif" }}>
            Meet Our Team
          </h2>
          <p className="mt-3 text-slate-600 max-w-2xl mx-auto">
            The passionate professionals behind Meadow De Jalsa's success story
          </p>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.4 }}
          viewport={{ once: true }}
          className="relative flex items-center gap-6"
        >
          {hasMoreThanFour && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={prevSlide}
              disabled={currentIndex === 0}
              className="flex-shrink-0 p-2 rounded-full bg-white ring-1 ring-slate-200 hover:ring-emerald-300 disabled:opacity-50 disabled:cursor-not-allowed transition shadow-lg"
            >
              <ChevronLeft className="w-5 h-5 text-slate-600" />
            </motion.button>
          )}
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 flex-1">
          {visibleMembers.map((member, index) => (
            <motion.div 
              key={member.id} 
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.5 + (index * 0.1), ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ y: -8, scale: 1.02 }}
              className="bg-white rounded-2xl p-6 ring-1 ring-slate-200 hover:ring-emerald-300 hover:shadow-lg transition-all duration-300 text-center"
            >
              <motion.div 
                initial={{ scale: 0, rotate: -180 }}
                whileInView={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.5, delay: 0.7 + (index * 0.1) }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.1 }}
                className="w-20 h-20 bg-slate-100 rounded-full mx-auto mb-4 overflow-hidden"
              >
                <img 
                  src={member.image_url || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=400&auto=format&fit=crop'} 
                  alt={member.name}
                  className="w-full h-full object-cover"
                />
              </motion.div>
              <motion.h4 
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.8 + (index * 0.1) }}
                viewport={{ once: true }}
                className="text-[18px] font-semibold text-emerald-950 mb-1"
              >
                {member.name}
              </motion.h4>
              <motion.p 
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.9 + (index * 0.1) }}
                viewport={{ once: true }}
                className="text-[14px] font-medium text-amber-600 mb-3"
              >
                {member.position}
              </motion.p>
              <motion.div 
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 1 + (index * 0.1) }}
                viewport={{ once: true }}
                className="space-y-2 text-[13px] text-slate-600"
              >
                {member.experience && (
                  <div className="flex items-center justify-center gap-2">
                    <Clock className="w-3.5 h-3.5" />
                    <span>{member.experience}</span>
                  </div>
                )}
                {member.specialty && <p>{member.specialty}</p>}
                {member.bio && (
                  <p className="text-xs text-slate-500 mt-2 leading-relaxed">
                    {member.bio}
                  </p>
                )}
              </motion.div>
            </motion.div>
          ))}
          </div>
          
          {hasMoreThanFour && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={nextSlide}
              disabled={currentIndex + 4 >= teamMembers.length}
              className="flex-shrink-0 p-2 rounded-full bg-white ring-1 ring-slate-200 hover:ring-emerald-300 disabled:opacity-50 disabled:cursor-not-allowed transition shadow-lg"
            >
              <ChevronRight className="w-5 h-5 text-slate-600" />
            </motion.button>
          )}
        </motion.div>
      </motion.div>
    </section>
  );
};

export default TeamSection;